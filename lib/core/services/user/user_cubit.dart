import 'dart:async';
import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/account.dart';
import 'package:gp_stock_app/core/models/apis/auth.dart';
import 'package:gp_stock_app/core/models/entities/account/account.dart';
import 'package:gp_stock_app/core/models/entities/user/user.dart';
import 'package:gp_stock_app/core/utils/engage_lab/engage_lab_app.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/secure_storage_helper.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/notifications/logic/notifications_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/string_extension.dart';
import 'package:gp_stock_app/shared/constants/keys.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/services/polling/polling_service_v2.dart';
import 'package:injectable/injectable.dart';

import 'user_state.dart';

/// 用户信息全局状态管理 Cubit
/// Global user info state management Cubit
///
/// 使用示例 Usage Example:
/// ```dart
/// // 1. 监听登录状态变化
/// // Listen to login status changes
/// final userCubit = getIt<UserCubit>();
/// StreamSubscription? loginSubscription;
///
/// loginSubscription = userCubit.loginStatusStream.listen((isLoggedIn) {
///   if (isLoggedIn) {
///     print('用户已登录 User logged in');
///     // 执行登录后的操作，如跳转到主页
///     // Perform post-login actions, like navigating to home
///   } else {
///     print('用户已登出 User logged out');
///     // 执行登出后的操作，如跳转到登录页
///     // Perform post-logout actions, like navigating to login
///   }
/// });
///
/// // 2. 获取当前状态
/// // Get current state
/// bool isLoggedIn = userCubit.isLoggedIn;
/// UserModel? currentUser = userCubit.currentUser;
///
/// // 3. 记得在不需要时取消订阅
/// // Remember to cancel subscriptions when not needed
/// loginSubscription?.cancel();
/// ```
@singleton
class UserCubit extends Cubit<UserState> {
  /// 登录状态变化的 StreamController
  /// StreamController for login status changes
  final StreamController<bool> _loginStatusController = StreamController<bool>.broadcast();

  /// 语言变化的 StreamController
  /// StreamController for locale changes
  final StreamController<String> currentLocaleController = StreamController<String>.broadcast();

  /// 构造函数，初始化时自动加载本地用户信息
  /// Constructor, auto load local user info on init
  UserCubit() : super(UserState()) {
    loadUser();
  }

  /// 初始化语言设置
  /// Initialize locale settings
  void initializeLocale() {
    try {
      // Try to get current locale from context, fallback to English if not available
      final context = getIt<NavigatorService>().navigatorKey.currentContext;
      String currentLocale = 'en-US'; // Default fallback

      if (context != null) {
        final locale = context.locale;
        currentLocale = '${locale.languageCode}-${locale.countryCode}';
      }

      currentLocaleController.add(currentLocale);
      LogD("初始化语言 Initialize locale: $currentLocale");
    } catch (e) {
      // Fallback to English if there's any error
      const defaultLocale = 'en-US';
      currentLocaleController.add(defaultLocale);
      LogD("语言初始化失败，使用默认语言 Locale initialization failed, using default: $defaultLocale");
    }
  }

  /// 设置当前语言
  /// Set current locale
  void setCurrentLocale(String locale) {
    currentLocaleController.add(locale);
    LogD("语言变化 Locale changed: $locale");
  }

  /// 获取登录状态变化的 Stream（对外注册使用）
  /// Get login status change stream (for external registration)
  Stream<bool> get loginStatusStream => _loginStatusController.stream;

  /// 当前登录状态（便捷访问）
  /// Current login status (convenient access)
  bool get isLoggedIn => state.isLogin;

  /// 当前用户信息（便捷访问）
  /// Current user info (convenient access)
  UserModel? get currentUser => state.userInfo;

  bool get isUserRegisteredViaEmail => currentUser?.registerType == 'EMAIL';
  bool get isUserRegisteredViaMobile => currentUser?.registerType == 'MOBILE';

  bool get hasEmailAndMobile => (currentUser?.email.isNotNullNorEmpty ?? false) && (currentUser?.mobile.isNotNullNorEmpty ?? false);

  /// 设置 token，并保存到本地
  /// Set token and save to local storage
  void setToken(String? token) {
    final wasLoggedIn = state.isLogin;
    final isLoggedIn = token != null;

    SecureStorageHelper().writeSecureData(LocalStorageKeys.token, token ?? '');
    emit(state.copyWith(token: () => token, isLogin: isLoggedIn));

    // 如果登录状态发生变化，通知监听者
    // If login status changed, notify listeners
    if (wasLoggedIn != isLoggedIn) {
      _loginStatusController.add(isLoggedIn);
      LogD("登录状态变化 Login status changed: $wasLoggedIn -> $isLoggedIn");
    }
  }

  void onChangeRemotePushToken(String? token) {
    emit(state.copyWith(remotePushToken: token));
    if (token != null) {
      AccountApi.updatePushToken(token);
    }
  }

  /// 获取用户信息
  void fetchUserInfo() async {
    final res = await AuthApi.getUserInfo();
    if (res != null) {
      setUserInfo(res);
    }
  }

  /// 设置用户信息，保存到本地并启动账户信息轮询
  /// Set user info, save to local and start account info polling
  void setUserInfo(UserModel? entity) {
    emit(state.copyWith(userInfo: () => entity));

    if (entity != null) {
      // 保存用户信息到本地
      // Save user info to local storage
      SecureStorageHelper().writeSecureData(LocalStorageKeys.userV2, jsonEncode(entity));
      fetchAccountInfo(); // 立即获取一次账户信息 / Fetch account info immediately
      startAccountInfoPolling();
      if (getIt.isRegistered<AccountScreenCubitV2>()) {
        getIt<AccountScreenCubitV2>().fetchData();
      }
      getIt<NotificationsCubit>().getNotificationCount();
      onChangeRemotePushToken(EngageLabUtil().currentRid);
    } else {
      _clearUser();
    }
  }

  /// 设置账户信息
  /// Set account info
  void setAccountInfo(AccountInfo? entity) {
    emit(state.copyWith(accountInfo: entity));
  }

  /// 拉取账户信息（需已登录）
  /// Fetch account info (must be logged in)
  Future<AccountInfo?> fetchAccountInfo() async {
    if (!state.isLogin) return null;
    final result = await AccountApi.fetchAccountInfo();
    if (result != null && state.isLogin) setAccountInfo(result);
    return result;
  }

  /// 启动账户信息轮询
  /// Start polling for account info
  void startAccountInfoPolling() {
    getIt<PollingServiceV2>().startPolling(
      id: kGPAccountInfoPolling,
      onPoll: () async {
        fetchAccountInfo();
        return true;
      },
      interval: const Duration(seconds: 5),
      shouldStop: () => !state.isLogin,
    );
  }

  /// 加载本地用户信息和 token
  /// Load user info and token from local storage
  Future<void> loadUser() async {
    final token = await SecureStorageHelper().readSecureData(LocalStorageKeys.token);
    if (!StringUtil.isEmpty(token)) setToken(token);

    final userInfoStr = await SecureStorageHelper().readSecureData(LocalStorageKeys.userV2);
    if (userInfoStr != null) {
      setUserInfo(UserModel.fromJson(jsonDecode(userInfoStr)));
    }
    fetchUserInfo();
  }

  /// 用户登出（清除所有用户数据）
  /// User logout (clear all user data)
  Future<void> logout() async {
    LogD("用户登出 User logout");
    setToken(null);
    setUserInfo(null);
  }

  /// 清除用户信息（登出时调用）
  /// Clear user info (called on logout)
  Future<void> _clearUser() async {
    await SecureStorageHelper().deleteAllExcept();
    NetworkProvider.clearCache();
    getIt<PollingServiceV2>().stopAll();
    emit(UserState());
  }

  /// 释放资源
  /// Dispose resources
  @override
  Future<void> close() {
    _loginStatusController.close();
    currentLocaleController.close();
    return super.close();
  }
}
