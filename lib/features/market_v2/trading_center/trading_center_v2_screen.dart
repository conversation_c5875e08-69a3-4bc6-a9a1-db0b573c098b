import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/core/models/entities/market/market_search_result.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/quote_page.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trade/trade_page.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/tab_bar_widget.dart';
import 'package:gp_stock_app/shared/widgets/trade_tab_switcher.dart';

import 'trading_center_v2_cubit.dart';

class TradingCenterV2Screen extends StatelessWidget {
  const TradingCenterV2Screen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<TradingCenterV2Cubit, TradingCenterV2State,
        ({Security? security, TradeTabType currentTabType})>(
      selector: (state) =>
          (security: state.security, currentTabType: state.currentTabType),
      builder: (context, state) {
        if (state.security == null) return const SizedBox.shrink();
        final security = state.security!;
        final isIndexTrading = security.isIndexTrading;
        return Scaffold(
          appBar: AppBar(
            centerTitle: true,
            backgroundColor: context.theme.cardColor,
            surfaceTintColor: Colors.transparent,
            title: _buildAppBarTitle(isIndexTrading: isIndexTrading, instrument: security.instrument),
            actions: [
              _buildAppBarSearchAction(context, market: security.market),
            ],
          ),
          body: state.currentTabType == TradeTabType.Quotes
              ? const QuotePage()
              : TradePage(security: security),
        );
      },
    );
  }

  Widget _buildAppBarTitle({required bool isIndexTrading, required String instrument}) {
    return BlocSelector<TradingCenterV2Cubit, TradingCenterV2State, TradeTabType>(
      selector: (state) => state.currentTabType,
      builder: (context, currentTabType) {
        return TradeTabSwitcher(
          currentTab: currentTabType,
          style: switch (AppConfig.instance.skinStyle) {
            AppSkinStyle.kTemplateD => TabComponentBoxStyle.kSecondary,
            _ => TabComponentBoxStyle.kPrimary,
          },
          onTabSelected: (tab) {
            // 股指点击交易跳转至行情股指页
            // Navigate to the trade tab on bottom tabs if the index trading is selected
            if (isIndexTrading && tab == TradeTabType.Trading) {
              context.read<MainCubit>().selectedNavigationItem(BottomNavType.trade);
              context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.stockIndex);

              final index = context
                  .read<IndexTradeCubit>()
                  .state
                  .indexStockConfigList
                  .indexWhere((element) => element.instrument == instrument);
              context.read<IndexTradeCubit>().updateSelectedIndex(index, animate: true);
              getIt<NavigatorService>().popToRoot();
              return;
            }
            // Change the tab index
            context.read<TradingCenterV2Cubit>().onChangeCurrentTabType(tab);
          },
        );
      },
    );
  }

  Widget _buildAppBarSearchAction(BuildContext context, {required String market}) {
    return IconButton(
      onPressed: () {
        AuthUtils.verifyAuth(() {
          getIt<NavigatorService>().push(AppRouter.routeInstrumentSearch, arguments: {
            "market": market,
            "onSelect": (MarketSearchResult selected) {
              final cubit = context.read<TradingCenterV2Cubit>();
              cubit.setSecurity(selected.getInstrument);
              cubit.onChangeCurrentTabType(TradeTabType.Quotes);
            }
          });
        });
      },
      icon: Icon(
        LucideIcons.search,
        size: 18.gsp,
      ),
    );
  }
}
