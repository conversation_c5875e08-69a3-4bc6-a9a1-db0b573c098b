import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/funds_record_list.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/funds_record_list.g.dart';

/// 资金记录
@JsonSerializable()
class FundsRecordList {
	final int current;
	final bool hasNext;
	final List<FundsRecord> records;
	final int total;

	const FundsRecordList({
		this.current = 0,
		this.hasNext = false,
		this.records = const [],
		this.total = 0,
	});

	factory FundsRecordList.fromJson(Map<String, dynamic> json) => $FundsRecordListFromJson(json);

	Map<String, dynamic> toJson() => $FundsRecordListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class FundsRecord {
	final double afterNum;
	final double beforeNum;
	final int contractAccountId;
	final int contractType;
	final String createTime;
	final String currency;
	final String email;
	final int fromType;
	final int id;
	final String marketType;
	final String mobile;
	final String multiple;
	final int periodType;
	final String realName;
	final int refId;
	final String serialNo;
	final int type;
	final double updateNum;
	final int userId;
	final int userType;

	const FundsRecord({
		this.afterNum = 0.0,
		this.beforeNum = 0.0,
		this.contractAccountId = 0,
		this.contractType = 0,
		this.createTime = '',
		this.currency = '',
		this.email = '',
		this.fromType = 0,
		this.id = 0,
		this.marketType = '',
		this.mobile = '',
		this.multiple = '',
		this.periodType = 0,
		this.realName = '',
		this.refId = 0,
		this.serialNo = '',
		this.type = 0,
		this.updateNum = 0.0,
		this.userId = 0,
		this.userType = 0,
	});

	factory FundsRecord.fromJson(Map<String, dynamic> json) => $FundsRecordFromJson(json);

	Map<String, dynamic> toJson() => $FundsRecordToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}