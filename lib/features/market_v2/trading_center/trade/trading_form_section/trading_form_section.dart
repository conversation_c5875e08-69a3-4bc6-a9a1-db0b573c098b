import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_state.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trade/trading_form_section/3_position_fraction_selector/position_fraction_selector.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/utils/trade_calculator.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/layout/expanded_row.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/stepper_text_field.dart';
import 'package:shimmer/shimmer.dart';

import '0_direction/trading_direction_section.dart';
import '1_trade_account_dropdown/trade_account_dropdown.dart';
import '2_order_type_dropdown/order_type_dropdown.dart';
import '4_trade_summary_section/trade_summary_section.dart';
import '../order_confirm_dialog/order_confirm_dialog.dart';

class TradingFormSection extends StatelessWidget {
  const TradingFormSection({
    super.key,
    required this.security,
    required this.isContractTrading,
  });

  final Security security;
  final bool isContractTrading;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      spacing: 8.gw,
      children: [
        TradingDirectionSection(
          isCNShare: security.marketCategory == MarketCategory.cnStocks,
          isIndexTrading: security.isIndexTrading,
        ),
        ExpandedRow(
          spacing: 8.gw,
          children: [
            _buildTradeAccountDropdown(context),
            _buildPriceInput(context),
          ],
        ),
        ExpandedRow(
          spacing: 8.gw,
          children: [
            _buildOrderTypeDropdown(context),
            _buildCountInput(context),
          ],
        ),
        _buildAvailableBalanceWidget(),
        _buildPositionFractionSelector(),
        _buildTradeSummarySections(context),
      ],
    );
  }

  /// 构建价格输入框 / Build price input field
  Widget _buildPriceInput(BuildContext context) {
    return BlocSelector<
        TradingCenterV2Cubit,
        TradingCenterV2State,
        ({
          OrderPriceType orderPriceType,
          double inputPrice,
          int pricePrecision,
          double priceDownLimited,
          double priceUpLimited,
          bool isDataReady
        })>(
      selector: (state) => (
        orderPriceType: state.tradeForm.orderPriceType,
        inputPrice: state.tradeForm.inputPrice,
        pricePrecision: state.tradeForm.pricePrecision,
        priceDownLimited: state.stockInfo?.priceDownLimited ?? 0,
        priceUpLimited: state.stockInfo?.priceUpLimited ?? double.infinity,
        isDataReady:
            state.stockInfoStatus == DataStatus.success && state.stockInfo != null && state.stockInfo!.latestPrice > 0,
      ),
      builder: (context, data) {
        // 数据未就绪时显示骨架屏
        if (!data.isDataReady) {
          return _buildShimmerField();
        }

        final cubit = context.read<TradingCenterV2Cubit>();

        // 市价单：显示提示文本 / Market order: show hint text
        if (data.orderPriceType == OrderPriceType.market) {
          return _buildRealTimePriceLabelDisplay(context);
        }

        // 限价单：显示价格输入控件 / Limit order: show price input
        return StepperTextField(
          displayValue: data.inputPrice.toStringAsFixed(data.pricePrecision),
          decimalDigits: data.pricePrecision,
          canDecrement: data.inputPrice > data.priceDownLimited,
          canIncrement: data.inputPrice < data.priceUpLimited,
          onIncrement: () => cubit.incrementPrice(),
          onDecrement: () => cubit.decrementPrice(),
          onValueChanged: (value) {
            final price = double.tryParse(value);
            if (price != null) {
              cubit.onInputPriceChanged(price, needClamp: false); // 实时输入不 clamp
            }
          },
          onFocusChanged: (value) {
            // 失去焦点时，使用用户输入的值进行验证
            // When focus is lost, validate using the user's input value
            final price = double.tryParse(value);
            if (price == null) return null;
            final aligned = cubit.onInputPriceChanged(price);
            return aligned.toStringAsFixed(data.pricePrecision);
          },
        );
      },
    );
  }

  /// ‘以实时价格成交’Label
  Widget _buildRealTimePriceLabelDisplay(BuildContext context) {
    return Container(
      height: 32.gw,
      padding: EdgeInsets.symmetric(horizontal: 3.gw),
      decoration: BoxDecoration(
        color: context.theme.inputDecorationTheme.fillColor,
        borderRadius: BorderRadius.circular(6.gr),
      ),
      alignment: Alignment.center,
      child: Text(
        'realTimePrice'.tr(), // 以实时价格成交
        style: context.textTheme.primary.copyWith(
          color: context.colorTheme.textPrimary,
        ),
      ),
    );
  }

  /// 构建数量输入框 / Build quantity input field
  Widget _buildCountInput(BuildContext context) {
    return BlocBuilder<TradingCenterV2Cubit, TradingCenterV2State>(
      builder: (context, state) {
        // 检查数据就绪状态：stockInfo 加载成功、价格有效、quantityStep 有效
        final isDataReady = state.stockInfoStatus == DataStatus.success &&
            state.stockInfo != null &&
            state.stockInfo!.latestPrice > 0 &&
            state.tradeForm.quantityStep > 0 &&
            state.tradeForm.openLongSummary != null;

        // 数据未就绪时显示骨架屏
        if (!isDataReady) {
          return _buildShimmerField();
        }

        final cubit = context.read<TradingCenterV2Cubit>();
        final tradeForm = state.tradeForm;

        // 获取当前使用的数量和最大可用数量 / Get current quantity and max available quantity（只有开仓）
        final double currentQty = tradeForm.quantity;
        final double maxQty = tradeForm.openLongSummary?.availableQty ?? 0;
        final bool hasMaxLimit = maxQty > 0;

        // 从汇总中获取小数位数，如果没有则根据步进值计算 / Get decimal digits from summary, or calculate from step
        final decimalDigits = tradeForm.openLongSummary?.quantityFractionDigits ??
            TradeCalculator.calculateFractionDigits(tradeForm.quantityStep);
        final displayValue = currentQty.toStringAsFixed(decimalDigits);

        return StepperTextField(
          displayValue: displayValue,
          decimalDigits: decimalDigits,
          canDecrement: hasMaxLimit && currentQty > tradeForm.quantityStep,
          canIncrement: hasMaxLimit && currentQty < maxQty,
          onIncrement: () => cubit.incrementQuantity(),
          onDecrement: () => cubit.decrementQuantity(),
          onValueChanged: (value) {
            final qty = double.tryParse(value);
            if (qty != null) {
              cubit.onQuantityInputChanged(qty);
            }
          },
          onFocusChanged: (value) {
            final qty = double.tryParse(value);
            if (qty == null) return null;
            final aligned = cubit.onQuantityInputChanged(qty);
            return aligned.toStringAsFixed(decimalDigits);
          },
        );
      },
    );
  }

  /// 构建交易账户下拉框 / Build trade account dropdown
  Widget _buildTradeAccountDropdown(BuildContext context) {
    return BlocSelector<TradingCenterV2Cubit, TradingCenterV2State, bool>(
      selector: (state) =>
          state.stockInfoStatus == DataStatus.success && state.stockInfo != null && state.stockInfo!.latestPrice > 0,
      builder: (context, isDataReady) {
        // 数据未就绪时显示骨架屏
        if (!isDataReady) {
          return _buildShimmerField();
        }

        return TradeAccountDropdown(security: security);
      },
    );
  }

  /// 构建订单类型下拉框 / Build order type dropdown
  Widget _buildOrderTypeDropdown(BuildContext context) {
    return BlocSelector<TradingCenterV2Cubit, TradingCenterV2State, bool>(
      selector: (state) =>
          state.stockInfoStatus == DataStatus.success && state.stockInfo != null && state.stockInfo!.latestPrice > 0,
      builder: (context, isDataReady) {
        // 数据未就绪时显示骨架屏
        if (!isDataReady) {
          return _buildShimmerField();
        }

        return const OrderTypeDropdown();
      },
    );
  }

  Widget _buildAvailableBalanceWidget() {
    return BlocSelector<TradingCenterV2Cubit, TradingCenterV2State, ({double balance, String currency})>(
        selector: (state) => (
              balance: state.tradeForm.availBal,
              currency: state.tradeForm.availBalCcy,
            ),
        builder: (context, state) {
          return AmountRow(
            title: "availableBalance".tr(), // 可用余额
            amount: state.balance,
            currency: state.currency,
          );
        });
  }

  /// 构建仓位比例选择器 / Build position fraction selector
  Widget _buildPositionFractionSelector() {
    return BlocSelector<TradingCenterV2Cubit, TradingCenterV2State, OrderFraction?>(
      selector: (state) => state.tradeForm.selectedFraction,
      builder: (context, selectedFraction) {
        final cubit = context.read<TradingCenterV2Cubit>();
        return PositionFractionSelector(
          selectedFraction: selectedFraction,
          onFractionChanged: cubit.onFractionChanged,
        );
      },
    );
  }

  /// 构建交易汇总区域（显示开仓 summaries）/ Build trade summary sections (show open summaries)
  Widget _buildTradeSummarySections(BuildContext context) {
    return BlocSelector<TradingCenterV2Cubit, TradingCenterV2State,
        ({TradeSummary? longSummary, TradeSummary? shortSummary})>(
      selector: (state) => (
        longSummary: state.tradeForm.openLongSummary,
        shortSummary: state.tradeForm.openShortSummary,
      ),
      builder: (context, state) {
        // 过滤掉为空的
        final summaries = <TradeSummary>[
          if (state.longSummary != null) state.longSummary!,
          if (state.shortSummary != null) state.shortSummary!,
        ];
        if (summaries.isEmpty) return const SizedBox.shrink();

        final cubit = context.read<TradingCenterV2Cubit>();

        return Column(
          spacing: 8.gw,
          children: summaries
              .map(
                (e) => TradeSummarySection(
                  summary: e,
                  onActionPressed: () {
                    // 验证是否已选择交易账户 / Validate if trade account is selected
                    if (!cubit.requireTradeAccount()) {
                      return;
                    }

                    // 保存交易类型，用于传递给对话框
                    // Save trade type to pass to dialog
                    final tradeType = e.tradeType;

                    // 收起键盘 / Dismiss keyboard
                    getIt<NavigatorService>().unFocus();

                    showDialog(
                      context: context,
                      builder: (_) {
                        return BlocProvider.value(
                          value: cubit,
                          child: OrderConfirmDialog(tradeType: tradeType),
                        );
                      },
                    );
                  },
                ),
              )
              .toList(),
        );
      },
    );
  }

  /// 构建字段级别的骨架屏 / Build field-level shimmer
  Widget _buildShimmerField() {
    return ShimmerWidget(height: 32.gw, width: double.infinity);
  }
}
