import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/models/entities/trade/trade_handling_fee_config.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class TradeSellState extends Equatable {
  /// 持仓记录
  final FTradeAcctOrderRecords record;

  /// 合约账户ID（从上层传入，避免依赖 record 中可能缺失/不准的 contractId）
  final int? contractAccountId;

  /// 订单类型（市价/限价）
  final OrderPriceType orderPriceType;

  /// 卖出数量
  final double sellQuantity;

  /// 选中的仓位比例
  final OrderFraction? selectedFraction;

  /// 总价（含手续费）
  final double totalAmount;

  /// 手续费
  final double fee;

  /// 是否正在提交订单
  final bool isLoading;

  /// 限价单输入价格
  final double inputPrice;

  /// 当前市场价格（实时更新）
  final double currentPrice;

  /// 手续费配置列表（股票使用）/ Fee config list (for stocks)
  final List<TradeHandlingFeeConfig> feeConfigs;

  /// 期货手续费配置（期货使用）/ Futures fee config (for futures)
  final FTradeConfigModel? futuresConfig;

  /// 跌停价（仅A股，默认0表示无限制）/ Price down limited (A-Share only, default 0 means no limit)
  final double priceDownLimited;

  /// 涨停价（仅A股，默认infinity表示无限制）/ Price up limited (A-Share only, default infinity means no limit)
  final double priceUpLimited;

  /// 数量步进值（一手数量）/ Quantity step (lot size)
  final double quantityStep;

  /// 价格精度（小数位数）/ Price precision (decimal digits)
  final int pricePrecision;

  /// 价格步进值（最小变动单位）/ Price tick size (minimum price increment)
  final double priceTickSize;

  /// 股票信息加载状态 / Stock info loading status
  final DataStatus stockInfoStatus;

  const TradeSellState({
    required this.record,
    this.contractAccountId,
    this.orderPriceType = OrderPriceType.market,
    this.sellQuantity = 0.0,
    this.selectedFraction = OrderFraction.full,
    this.totalAmount = 0.0,
    this.fee = 0.0,
    this.isLoading = false,
    this.inputPrice = 0.0,
    this.currentPrice = 0.0,
    this.feeConfigs = const [],
    this.futuresConfig,
    this.priceDownLimited = 0,
    this.priceUpLimited = double.infinity,
    this.quantityStep = 0.01, // 默认最小步进，适配股指交易 / Default min step for index trading
    this.pricePrecision = 2,
    this.priceTickSize = 0.01,
    this.stockInfoStatus = DataStatus.idle,
  });

  TradeSellState copyWith({
    FTradeAcctOrderRecords? record,
    int? contractAccountId,
    OrderPriceType? orderPriceType,
    double? sellQuantity,
    ValueGetter<OrderFraction?>? selectedFraction,
    double? totalAmount,
    double? fee,
    bool? isLoading,
    double? inputPrice,
    double? currentPrice,
    List<TradeHandlingFeeConfig>? feeConfigs,
    FTradeConfigModel? futuresConfig,
    double? priceDownLimited,
    double? priceUpLimited,
    double? quantityStep,
    int? pricePrecision,
    double? priceTickSize,
    DataStatus? stockInfoStatus,
  }) {
    return TradeSellState(
      record: record ?? this.record,
      contractAccountId: contractAccountId ?? this.contractAccountId,
      orderPriceType: orderPriceType ?? this.orderPriceType,
      sellQuantity: sellQuantity ?? this.sellQuantity,
      selectedFraction: selectedFraction != null ? selectedFraction() : this.selectedFraction,
      totalAmount: totalAmount ?? this.totalAmount,
      fee: fee ?? this.fee,
      isLoading: isLoading ?? this.isLoading,
      inputPrice: inputPrice ?? this.inputPrice,
      currentPrice: currentPrice ?? this.currentPrice,
      feeConfigs: feeConfigs ?? this.feeConfigs,
      futuresConfig: futuresConfig ?? this.futuresConfig,
      priceDownLimited: priceDownLimited ?? this.priceDownLimited,
      priceUpLimited: priceUpLimited ?? this.priceUpLimited,
      quantityStep: quantityStep ?? this.quantityStep,
      pricePrecision: pricePrecision ?? this.pricePrecision,
      priceTickSize: priceTickSize ?? this.priceTickSize,
      stockInfoStatus: stockInfoStatus ?? this.stockInfoStatus,
    );
  }

  @override
  List<Object?> get props => [
        record,
        contractAccountId,
        orderPriceType,
        sellQuantity,
        selectedFraction,
        totalAmount,
        fee,
        isLoading,
        inputPrice,
        currentPrice,
        feeConfigs,
        futuresConfig,
        priceDownLimited,
        priceUpLimited,
        quantityStep,
        pricePrecision,
        priceTickSize,
        stockInfoStatus,
      ];
}
