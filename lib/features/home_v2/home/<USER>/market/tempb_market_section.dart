import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/market/watch_list/widgets/wishlist_data_table.dart';
import 'charts/home_index_cards_yhxt.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import 'market_data_table.dart';

/// 首页行情 Tab 区域（tempb 样式）
class TempBHomeMarketSection extends StatelessWidget {
  final int tabIndex;
  final List<MarketSectionTab> tabTypes;
  final Function(int) onTabChanged;

  const TempBHomeMarketSection({
    super.key,
    required this.tabIndex,
    required this.tabTypes,
    required this.onTabChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      margin: EdgeInsets.symmetric(horizontal: 15.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Stack(
        children: [
          Positioned.fill(
            child: Container(color: Colors.white),
          ),
          Container(
            width: 195.gw,
            height: 195.gw,
            padding: EdgeInsets.fromLTRB(16.gw, 11.gw, 0, 26.gw),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFFf6d0d1),
                  Colors.white,
                ],
                stops: [0, 0.25],
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(16.gw, 11.gw, 0, 26.gw),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(context),
                SizedBox(height: 14.gw),
                _buildTabContent(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Image.asset(
              "assets/images/icon_home_stock.png",
              width: 16.gw,
              height: 16.gw,
            ),
            SizedBox(width: 11.gw),
            Text(
              "featuredStocks".tr(),
              style: TextStyle(color: context.colorTheme.tabActive).fs16.w500,
            ),

            _buildTabBar(context),
          ],
        ),
      ],
    );
  }

  Widget _buildTabBar(BuildContext context) {
    final tabData = tabTypes.map((tabType) => tr(tabType.name, context: context)).toList();

    return CommonTabBar.withAutoKey(
      tabData,
      currentIndex: tabIndex,
      onTap: onTabChanged,
      style: CommonTabBarStyle.rectangular,
      isScrollable: true,
      height: 30.gw,
      labelPadding: EdgeInsets.symmetric(horizontal: 5.gw),
    );
  }

  Widget _buildTabContent(BuildContext context) {
    if (tabTypes.isEmpty || tabIndex >= tabTypes.length) {
      return const SizedBox.shrink();
    }

    final tabType = tabTypes[tabIndex];

    return switch (tabType) {
      MarketSectionTab.stockIndex => HomeIndexCardsYhxt(isFromHome: true),
      MarketSectionTab.stocks => Padding(
          padding: EdgeInsets.only(right: 16.gw),
          child: const MarketDataTable(limit: 5, isHome: true),
        ),
      MarketSectionTab.cnFutures => BlocProvider<CNFuturesListCubit>(
          key: const ValueKey('yhxt_home_cn_futures'),
          create: (_) => CNFuturesListCubit(FTradeListRepository(), showInHomePage: true),
          child: Padding(
            padding: EdgeInsets.only(right: 16.gw),
            child: const FTradeListScreen(
              scope: FuturesScope.cn,
              showInHomePage: true,
              showInCard: true,
            ),
          ),
        ),
      MarketSectionTab.globalFutures => BlocProvider<GlobalFuturesListCubit>(
          key: const ValueKey('yhxt_home_global_futures'),
          create: (_) => GlobalFuturesListCubit(FTradeListRepository(), showInHomePage: true),
          child: Padding(
            padding: EdgeInsets.only(right: 16.gw),
            child: const FTradeListScreen(
              scope: FuturesScope.global,
              showInHomePage: true,
              showInCard: true,
            ),
          ),
        ),
      MarketSectionTab.watchList => Padding(
          padding: EdgeInsets.only(right: 16.gw),
          child: const WishListDataTable(
            limit: 5,
            isFromHome: true,
            showInCard: null,
          ),
        ),
    };
  }
}
