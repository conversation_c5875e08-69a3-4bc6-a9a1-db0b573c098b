part of 'contract_apply_records_cubit.dart';

/// 合约申请记录列表状态
class ContractApplyRecordsState extends Equatable {
  final DataStatus fetchStatus;
  final DataStatus paginationStatus;
  final List<ContractAppleyRecord> records;
  final bool hasNext;
  final bool hasReachedMax;
  final int pageNum;

  const ContractApplyRecordsState({
    this.fetchStatus = DataStatus.idle,
    this.paginationStatus = DataStatus.idle,
    this.records = const [],
    this.hasNext = false,
    this.hasReachedMax = false,
    this.pageNum = 1,
  });

  @override
  List<Object?> get props => [
        fetchStatus,
        paginationStatus,
        records,
        hasNext,
        hasReachedMax,
        pageNum,
      ];

  ContractApplyRecordsState copyWith({
    DataStatus? fetchStatus,
    DataStatus? paginationStatus,
    List<ContractAppleyRecord>? records,
    bool? hasNext,
    bool? hasReachedMax,
    int? pageNum,
  }) {
    return ContractApplyRecordsState(
      fetchStatus: fetchStatus ?? this.fetchStatus,
      paginationStatus: paginationStatus ?? this.paginationStatus,
      records: records ?? this.records,
      hasNext: hasNext ?? this.hasNext,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      pageNum: pageNum ?? this.pageNum,
    );
  }
}
