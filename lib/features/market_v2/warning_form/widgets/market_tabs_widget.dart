import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/market_v2/warning_list/warning_list_cubit.dart';
import 'package:gp_stock_app/features/market_v2/warning_list/warning_list_state.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class MarketTabsWidget extends StatelessWidget {
  final Function(MarketCategory) onTabSelected;

  const MarketTabsWidget({
    super.key,
    required this.onTabSelected,
  });

  @override
  Widget build(BuildContext context) {
    return BlocSelector<WarningListCubit, WarningListState, MarketCategory>(
        selector: (state) => state.selectedMarketCategory,
        builder: (context, selectedCategory) {
          final tabs = MarketCategory.values.where((e) => e.securityType == '1').toList();

          // [
          //   (title: 'a_shares'.tr(), category: MarketCategory.cnStocks),
          //   (title: 'hk_shares'.tr(), category: MarketCategory.hkStocks),
          //   (title: 'us_shares'.tr(), category: MarketCategory.usStocks),
          // ];

          final tabData = tabs.map((tab) => tr(tab.nameKey)).toList();
          final currentIndex = tabs.indexWhere((tab) => tab == selectedCategory);

          return CommonTabBar.withAutoKey(
            tabData,
            currentIndex: currentIndex.clamp(0, tabData.length - 1),
            onTap: (index) => onTabSelected(tabs[index]),
            style: CommonTabBarStyle.line,
            isScrollable: false,
          );
        });
  }
}
