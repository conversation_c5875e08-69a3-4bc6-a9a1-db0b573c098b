import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/tab_bar_style_config.dart';

class AuthStyle {
  static double? get buttonBorderRadius => switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kZangGolden => 20.gw,
        _ => null,
      };

  static double get headerHeight => switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kZangGolden => 0.33.gsh,
        _ => 0.45.gsh,
      };

  static Widget headerWrapper({required Widget appheader, required Widget loginForm}) {
    return switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kZangGolden => Stack(
          children: [
            appheader,
            Positioned(
              child: Column(
                children: [
                  SizedBox(
                    height: headerHeight - 0.03.gsh,
                  ),
                  loginForm,
                ],
              ),
            )
          ],
        ),
      _ => Column(
          mainAxisSize: MainAxisSize.min,
          children: [appheader, loginForm],
        ),
    };
  }

  static BorderRadiusGeometry? get loginFormBorder => switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kZangGolden => BorderRadius.circular(20.gw),
        _ => null,
      };

  static TabBarStyleConfig Function(TabBarStyleConfig)? styleOverrides(BuildContext context) =>
      (config) => switch (AppConfig.instance.skinStyle) {
            AppSkinStyle.kZangGolden => config.copyWith(
                selectedStyle: config.selectedStyle?.copyWith(color: context.theme.primaryColor),
              ),
            _ => config,
          };
}
