import 'dart:ui';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/apis/rank.dart';
import 'package:gp_stock_app/features/ranking/models/rank_model.dart';
import 'package:gp_stock_app/features/ranking/models/rank_tab.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'ranking_state.dart';

class RankingCubit extends Cubit<RankingState> {
  RankingCubit() : super(const RankingState());

  List<RankViewModel> getRankViewModel() {
    return [
      RankViewModel(
        rank: 2,
        gradientColor: Color(0XFFFFEBD4),
        borderColor: Color(0XFFF8E9D9),
      ),
      RankViewModel(
        rank: 1,
        gradientColor: Color(0XFFFFD4D4),
        borderColor: Color(0XFFFFD4D4),
      ),
      RankViewModel(
        rank: 3,
        gradientColor: Color(0XFFFFEBAA),
        borderColor: Color(0XFFFFEBAA),
      ),
    ];
  }

  void switchRankTab(RankTabType tabType, {bool onlyFirstFive = true}) {
    if (isClosed) return;
    if (state.currentRankTabType == tabType) return;

    emit(state.copyWith(currentRankTabType: tabType));

    // 如果该 tab 的数据为空，则获取数据
    if (state.rankList.isEmpty) {
      fetchRankList();
    }
  }

  /// 获取排行榜列表
  Future<void> fetchRankList() async {
    if (isClosed) return;
    emit(state.copyWith(rankStatus: DataStatus.loading));
    final res = await RankApi.fetchRankList(type: state.currentRankTabType.code);
    if (res != null) {
      final sortedList = res.list..sort((a, b) => a.rankingNo.compareTo(b.rankingNo));
      emit(state.copyWith(selfRank: res.self, rankStatus: DataStatus.success, rankList: sortedList));
    }
  }
}
