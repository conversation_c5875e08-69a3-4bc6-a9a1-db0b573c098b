import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/features/account/domain/models/deposit_channel/deposit_channel_model.dart';
import 'package:gp_stock_app/features/account/domain/models/proxy_pay_channel/proxy_pay_channel_model.dart';
import 'package:gp_stock_app/features/account/domain/repository/bank_repository.dart';
import 'package:injectable/injectable.dart';

import '../models/deposit_records/deposit_record.dart';
import '../models/pay_order/pay_order_response.dart';
import '../models/withdrawal_records/withdrawal_record.dart';

@Singleton(as: BankRepository)
class BankService implements BankRepository {

  @override
  Future<ResponseResult<bool>> pay({
    required int payAmount, 
    int? userBankId,
    String? orderNumber,
    required int sysBankCardId,
    int? type,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'payAmount': payAmount,
        if (userBankId != null) 'userBankId': userBankId,
        'sysBankCardId': sysBankCardId,
      }..putIfAbsent('type', () => type);

      if (orderNumber != null && orderNumber.isNotEmpty) {
        data['chargeNo'] = orderNumber;
      }

      final Response response = await NetworkProvider().post(
        ApiEndpoints.getUserBankInfo,
        data: data,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<WithdrawalRecordResponse>> getWithdrawalRecords(int pageNumber, int pageSize) async {
    try {
      final Response response = await NetworkProvider().get(
        "${ApiEndpoints.withdrawalRecords}?pageNumber=$pageNumber&pageSize=$pageSize",
        isAuthRequired: true,
      );

      if (response.statusCode == 200 && response.data['code'] == 0) {
        return ResponseResult(
          data: WithdrawalRecordResponse.fromJson(response.data['data']),
        );
      } else {
        return ResponseResult(
          error: response.data['msg'] ?? 'error'.tr(),
        );
      }
    } on DioException catch (e) {
      return ResponseResult(
        error: e.message ?? 'error'.tr(),
      );
    } catch (e) {
      return ResponseResult(
        error: e.toString(),
      );
    }
  }

  // getDepositRecords
  @override
  Future<ResponseResult<DepositRecordResponse>> getDepositRecords(int pageNumber, int pageSize) async {
    try {
      final Response response = await NetworkProvider().get(
        "${ApiEndpoints.depositRecords}?pageNumber=$pageNumber&pageSize=$pageSize",
        isAuthRequired: true,
      );
      if (response.statusCode == 200 && response.data['code'] == 0) {
        return ResponseResult(
          data: DepositRecordResponse.fromJson(response.data['data']),
        );
      } else {
        return ResponseResult(
          error: response.data['msg'] ?? 'error'.tr(),
        );
      }
    } on DioException catch (e) {
      return ResponseResult(
        error: e.message ?? 'error'.tr(),
      );
    } catch (e) {
      return ResponseResult(
        error: e.toString(),
      );
    }
  }

  @override
  Future<ResponseResult<List<DepositChannelModel>>> getChannelList() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getDepositChannelsList,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final List<dynamic> records = response.data['data'] ?? [];
          return ResponseResult(
            data: records.map((e) => DepositChannelModel.fromJson(e)).toList(),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<List<ProxyPayChannelModel>>> getProxyPayChannelList() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.listProxyPayChannel,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: List<ProxyPayChannelModel>.from(response.data['data'].map((e) => ProxyPayChannelModel.fromJson(e))),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'error'.tr());
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ResponseResult<PayOrderResponse>> getPayOrders({
    required int pageNumber,
    required int pageSize,
    String? createDateStart,
    String? createDateEnd,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParams = {
        'pageNumber': pageNumber.toString(),
        'pageSize': pageSize.toString(),
      };

      // Add optional parameters if provided
      if (createDateStart != null) {
        queryParams['createDateStart'] = createDateStart;
      }
      if (createDateEnd != null) {
        queryParams['createDateEnd'] = createDateEnd;
      }

      // Convert query parameters to URL query string
      final queryString = queryParams.entries.map((e) => '${e.key}=${e.value}').join('&');

      final Response response = await NetworkProvider().get(
        "${ApiEndpoints.payOrder}?$queryString",
        isAuthRequired: true,
      );

      if (response.statusCode == 200 && response.data['code'] == 0) {
        return ResponseResult(
          data: PayOrderResponse.fromJson(response.data['data']),
        );
      } else {
        return ResponseResult(
          error: response.data['msg'] ?? 'error'.tr(),
        );
      }
    } on DioException catch (e) {
      return ResponseResult(
        error: e.message ?? 'error'.tr(),
      );
    } catch (e) {
      return ResponseResult(
        error: e.toString(),
      );
    }
  }
}
