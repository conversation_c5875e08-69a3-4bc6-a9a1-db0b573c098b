import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/apis/home.dart';
import 'package:gp_stock_app/features/home_v2/ai_chat/ai_chat_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AIChatCubit extends Cubit<AIChatState> {
  AIChatCubit() : super(const AIChatState());

  Future<void> sendMessage(String message) async {
    emit(state.copyWith(aiChatStatus: DataStatus.loading));
    try {
      final response = await HomeApi.queryAIAnswer(question: message);
      if (response != null) {
        emit(state.copyWith(
          aiChatStatus: DataStatus.success,
          aiChatData: [
            ...state.aiChatData,
            response,
          ],
        ));
      } else {
        emit(state.copyWith(aiChatStatus: DataStatus.failed));
      }
    } catch (e) {
      emit(state.copyWith(aiChatStatus: DataStatus.failed));
    }
  }
}
