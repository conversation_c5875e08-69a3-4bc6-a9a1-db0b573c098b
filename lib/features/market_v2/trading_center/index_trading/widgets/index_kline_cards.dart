import 'dart:math' show max, min;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/home_v2/home/<USER>/market/charts/index_kline_chart.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/core/models/entities/market/index_stock_card_model.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

/// 股指K线卡片 / Index kline cards
class IndexKlineCards extends StatefulWidget {
  const IndexKlineCards({super.key});

  @override
  State<IndexKlineCards> createState() => _IndexKlineCardsState();
}

class _IndexKlineCardsState extends State<IndexKlineCards> with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  int _firstVisibleItemIndex = 0;
  int _currentPage = 0; // 指示器当前选中（跟随 selectedIndex）
  static const double _cardHeight = 170.0;
  static const double _graphHeight = 70.0;
  static const double _graphWidth = 120.0;
  final double _itemSpacing = 5.gw;

  // Variables for snapping behavior
  bool _isScrolling = false;
  int _targetPage = 0;
  final int _itemsPerPage = 3; // We want to show 3 cards per page
  late AnimationController _animationController;
  late Animation<double> _animation;
  final PageController _pageController = PageController(
    viewportFraction: 0.33,
    initialPage: 0,
  );

  // 计算宽度：与交易页 11.gw 的左右边距对齐，3 卡一页
  double _itemWidth(BuildContext context) {
    final horizontalPadding = 11.gw * 2;
    final availableWidth = MediaQuery.of(context).size.width - horizontalPadding;
    return (availableWidth - _itemSpacing * (_itemsPerPage - 1)) / _itemsPerPage;
  }

  double _totalItemWidth(BuildContext context) => _itemWidth(context) + _itemSpacing;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller for smooth scrolling
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Add listeners for scroll events
    _scrollController.addListener(_onScroll);

    // Add listener for when scrolling ends to snap to the nearest page
    _scrollController.addListener(_onScrollEnd);

    // Add listener for when page changes
    _pageController.addListener(_onPageChanged);

    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        context.read<IndexTradeCubit>().updateAnimate();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.removeListener(_onScrollEnd);
    _scrollController.dispose();
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onPageChanged() {
    // Sync the PageController with our ScrollController
    if (_scrollController.hasClients && _scrollController.position.maxScrollExtent > 0) {
      _scrollController.position.notifyListeners();
    }
  }

  // This method handles the scrolling logic and updates the current visible index
  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final state = context.read<IndexTradeCubit>().state;
    final itemCount = state.indexStocks.length;
    if (itemCount == 0) return;

    final viewportWidth = _scrollController.position.viewportDimension;
    final scrollOffset = _scrollController.offset;

    // Calculate the current page based on scroll offset

    // Calculate the first visible item index
    final viewportCenter = scrollOffset + (viewportWidth / 2);
    final calculatedIndex = (viewportCenter / _totalItemWidth(context)).floor();
    final safeIndex = max(0, min(calculatedIndex, itemCount - 1));

    if (safeIndex != _firstVisibleItemIndex) {
      setState(() => _firstVisibleItemIndex = safeIndex);
    }
  }

  // This method detects when scrolling ends and snaps to the nearest page
  void _onScrollEnd() {
    if (!_scrollController.hasClients) return;

    // Check if the scroll is idle (not being dragged)
    if (!_scrollController.position.isScrollingNotifier.value) {
      if (_isScrolling) {
        _isScrolling = false;
        _snapToPage();
      }
    } else {
      _isScrolling = true;
    }
  }

  // Snap to the nearest page when scrolling ends
  void _snapToPage() {
    if (!_scrollController.hasClients) return;

    final viewportWidth = _scrollController.position.viewportDimension;
    final scrollOffset = _scrollController.offset;

    // Calculate the target page based on current scroll position
    final itemCount = context.read<IndexTradeCubit>().state.indexStocks.length;
    final maxPages = (itemCount / _itemsPerPage).ceil();

    // Calculate which page we're closest to
    final page = (scrollOffset / viewportWidth).round();
    _targetPage = max(0, min(page, maxPages - 1));

    // Calculate the target offset for the page
    final targetOffset = _targetPage * viewportWidth;

    // Only animate if we're not already at the target
    if ((targetOffset - scrollOffset).abs() > 0.5) {
      // Set up the animation
      _animation = Tween<double>(
        begin: scrollOffset,
        end: targetOffset,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ));

      // Add listener to update scroll position during animation
      _animation.addListener(() {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(_animation.value);
        }
      });

      // Reset and start the animation
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<IndexTradeCubit>();

    if (cubit.state.indexStockConfigList.isEmpty) return SizedBox.shrink();
    return BlocListener<IndexTradeCubit, IndexTradeState>(
      listenWhen: (previous, current) => previous.animate != current.animate,
      listener: (context, state) {
        if (state.animate) {
          _pageController.animateToPage(
            state.selectedIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          context.read<IndexTradeCubit>().updateAnimate(animate: false);
          setState(() {
            _firstVisibleItemIndex = state.selectedIndex;
            _currentPage = state.selectedIndex;
          });
        }
      },
      child: BlocBuilder<IndexTradeCubit, IndexTradeState>(
        bloc: getIt<IndexTradeCubit>(),
        builder: (context, state) {
          int itemCount = state.indexStocks.length;
          if (state.status == DataStatus.loading) {
            return _buildLoading();
          }

          if (state.status.isFailed || itemCount == 0) {
            return const Center(child: TableEmptyWidget());
          }

          final pageCount = itemCount; // 指示器与数据条数一致
          final itemWidth = _itemWidth(context);

          return Column(
            children: [
              Container(
                height: _cardHeight.gw,
                padding: EdgeInsets.symmetric(horizontal: 11.gw, vertical: 5.gw),
                child: PageView.builder(
                  key: PageStorageKey('index_kline_cards'),
                  controller: _pageController,
                itemCount: itemCount,
                onPageChanged: (index) {
                  setState(() {
                      _firstVisibleItemIndex = index;
                    });
                  },
                  pageSnapping: true,
                  padEnds: false,
                  // physics: const CustomPageViewScrollPhysics(),
                  itemBuilder: (context, i) => Padding(
                    padding: EdgeInsets.symmetric(horizontal: _itemSpacing / 2),
                    child: _buildStockCard(context, state, i, itemWidth),
                  ),
                ),
              ),
              if (pageCount > 1) _buildIndicators(pageCount, context),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStockCard(BuildContext context, IndexTradeState state, int i, double itemWidth) {
    final data = state.indexStocks;
    final isSelected = i == state.selectedIndex;
    final stockInfoData = data[i].stockInfo;

    final marketCategory = MarketCategory.fromSecurity(stockInfoData.market, '1');
    final market = marketCategory.marketType ?? '';
    final stockData = IndexStockCardModel.fromStockInfoWithSpots(context, data[i], market);

    return GestureDetector(
      onTap: () => _handleCardTap(i),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        width: itemWidth,
        padding: EdgeInsets.symmetric(vertical: 5.gw),
        margin: _calculateCardMargin(isSelected, i),
        decoration: _buildCardDecoration(context, isSelected),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          spacing: 4,
          children: [
            SizedBox(height: 3.gw),
            // Stock name
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.gw),
              child: Text(
                stockData.name,
                style: context.textTheme.primary.w700.copyWith(color: stockData.textColor),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
            // Graph
            IndexKlineChart(
              spots: stockData.spots ?? [],
              textColor: stockData.textColor,
              lineColor: stockData.lineColor ?? stockData.textColor,
              width: _graphWidth,
              height: _graphHeight,
              market: market,
            ),
            SizedBox(height: 3.gw),
            // Price
            FlipText(
              stockData.price,
              style: context.textTheme.primary.w600.copyWith(
                color: stockData.textColor,
              ),
              fractionDigits: 3,
            ),
            // Change and percentage
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              spacing: 4,
              children: [
                // Price change
                FlipText(
                  stockData.change,
                  prefix: stockData.change >= 0 ? '+' : '',
                  style: context.textTheme.primary.fs11.copyWith(
                    color: stockData.textColor,
                  ),
                ),
                // Percentage change
                FlipText(
                  stockData.gainPercentage,
                  prefix: stockData.gainPercentage >= 0 ? '+' : '',
                  suffix: '%',
                  style: context.textTheme.primary.fs11.w500.copyWith(
                    color: stockData.textColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 计算卡片边距 / Calculate card margin based on selection state and position
  EdgeInsets _calculateCardMargin(bool isSelected, int index) {
    return EdgeInsets.only(
      right: isSelected ? 0.gw : 1.gw,
      left: index == 0 ? 4.gw : 1.gw,
      bottom: isSelected ? 0 : 2.gw,
      top: isSelected ? 0 : 2.gw,
    );
  }

  /// 构建卡片装饰 / Build card decoration with proper styling
  BoxDecoration _buildCardDecoration(BuildContext context, bool isSelected) {
    return BoxDecoration(
      color: context.theme.cardColor,
      borderRadius: BorderRadius.circular(12),
      border: isSelected ? Border.all(color: context.theme.primaryColor, width: 2.gw) : null,
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.15),
          blurRadius: 2,
          offset: const Offset(0, 1),
        ),
      ],
    );
  }

  void _handleCardTap(int i) {
    final maxPage = context.read<IndexTradeCubit>().state.indexStocks.length - 1;
    if (i > maxPage) return;

    final lastPageStart = max(0, maxPage - (_itemsPerPage - 1));

    // 最后一页的卡片点击不触发滑动动画，直接同步状态
    if (i >= lastPageStart) {
      setState(() {
        _firstVisibleItemIndex = i;
        _currentPage = i;
      });
      context.read<IndexTradeCubit>().updateSelectedIndex(i);
      return;
    }

    // 不再自动滚动页面，保持当前可视位置，仅同步选中状态和指示器
    setState(() {
      _firstVisibleItemIndex = i;
      _currentPage = i;
    });
    context.read<IndexTradeCubit>().updateSelectedIndex(i);
  }

  Widget _buildIndicators(int pageCount, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          pageCount,
          (index) => AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: index == _currentPage ? 12 : 6,
            height: 6,
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(3),
              color: index == _currentPage ? Theme.of(context).primaryColor : Colors.grey.withNewOpacity(0.3),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoading() {
    final double itemWidth = 0.32.gsw - 4.gw;
    return SizedBox(
      height: 180.gw,
      child: ListView.separated(
        itemCount: 3,
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 6),
        scrollDirection: Axis.horizontal,
        separatorBuilder: (_, __) => const SizedBox(width: 8),
        itemBuilder: (_, __) => ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: ShimmerWidget(
            height: 190.gw,
            width: itemWidth,
          ),
        ),
      ),
    );
  }
}
