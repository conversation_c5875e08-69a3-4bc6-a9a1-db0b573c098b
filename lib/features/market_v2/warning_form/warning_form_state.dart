import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/market/warn_response_entity.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class WarningFormState extends Equatable {
  /// 详情获取状态（编辑时使用）/ Fetch status (for editing)
  final DataStatus fetchStatus;

  /// 添加或更新状态 / Add or update status
  final DataStatus addUpdateStatus;

  /// 预警数据 / Warning data
  final WarnResponse? warnData;

  /// 错误信息 / Error message
  final String error;

  const WarningFormState({
    this.fetchStatus = DataStatus.idle,
    this.addUpdateStatus = DataStatus.idle,
    this.warnData,
    this.error = '',
  });

  WarningFormState copyWith({
    DataStatus? fetchStatus,
    DataStatus? addUpdateStatus,
    WarnResponse? Function()? warnData,
    String? error,
  }) {
    return WarningFormState(
      fetchStatus: fetchStatus ?? this.fetchStatus,
      addUpdateStatus: addUpdateStatus ?? this.addUpdateStatus,
      warnData: warnData != null ? warnData() : this.warnData,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
        fetchStatus,
        addUpdateStatus,
        warnData,
        error,
      ];

  @override
  String toString() {
    return 'WarningFormState('
        'fetchStatus: $fetchStatus, '
        'addUpdateStatus: $addUpdateStatus, '
        'warnData: $warnData, '
        'error: $error'
        ')';
  }
}
