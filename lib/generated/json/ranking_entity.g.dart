import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';

RankingEntityList $RankingEntityListFromJson(Map<String, dynamic> json) {
  final RankingEntity? self = jsonConvert.convert<RankingEntity>(json['self']);
  final List<RankingEntity>? list =
      (json['list'] as List<dynamic>?)?.map((e) => jsonConvert.convert<RankingEntity>(e) as RankingEntity).toList();
  return RankingEntityList(
    self: self,
    list: list ?? [],
  );
}

Map<String, dynamic> $RankingEntityListToJson(RankingEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['self'] = entity.self?.toJson();
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension RankingEntityListExtension on RankingEntityList {
  RankingEntityList copyWith({
    RankingEntity? self,
    List<RankingEntity>? list,
  }) {
    return RankingEntityList(
      self: self ?? this.self,
      list: list ?? this.list,
    );
  }
}

RankingEntity $RankingEntityFromJson(Map<String, dynamic> json) {
  final double? amount = jsonConvert.convert<double>(json['amount']);
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  final bool? isSelf = jsonConvert.convert<bool>(json['isSelf']);
  final String? nickName = jsonConvert.convert<String>(json['nickName']);
  final double? profitRate = jsonConvert.convert<double>(json['profitRate']);
  final int? rankingNo = jsonConvert.convert<int>(json['rankingNo']);
  final int? userId = jsonConvert.convert<int>(json['userId']);
  return RankingEntity(
    amount: amount ?? 0.0,
    avatar: avatar ?? '',
    isSelf: isSelf ?? false,
    nickName: nickName ?? '',
    profitRate: profitRate ?? 0.0,
    rankingNo: rankingNo ?? 0,
    userId: userId ?? 0,
  );
}

Map<String, dynamic> $RankingEntityToJson(RankingEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['amount'] = entity.amount;
  data['avatar'] = entity.avatar;
  data['isSelf'] = entity.isSelf;
  data['nickName'] = entity.nickName;
  data['profitRate'] = entity.profitRate;
  data['rankingNo'] = entity.rankingNo;
  data['userId'] = entity.userId;
  return data;
}

extension RankingEntityExtension on RankingEntity {
  RankingEntity copyWith({
    double? amount,
    String? avatar,
    bool? isSelf,
    String? nickName,
    double? profitRate,
    int? rankingNo,
    int? userId,
  }) {
    return RankingEntity(
      amount: amount ?? this.amount,
      avatar: avatar ?? this.avatar,
      isSelf: isSelf ?? this.isSelf,
      nickName: nickName ?? this.nickName,
      profitRate: profitRate ?? this.profitRate,
      rankingNo: rankingNo ?? this.rankingNo,
      userId: userId ?? this.userId,
    );
  }
}
