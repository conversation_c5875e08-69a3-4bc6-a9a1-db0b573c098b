import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/features/ranking/index.dart';
import 'package:gp_stock_app/features/ranking/logic/ranking_cubit.dart';
import 'package:gp_stock_app/features/ranking/logic/ranking_state.dart';
import 'package:gp_stock_app/features/ranking/widgets/rank_card_shimmer.dart';
import 'package:gp_stock_app/features/ranking/widgets/rank_tile_shimmer.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class RankingView extends StatelessWidget {
  const RankingView({super.key});

  @override
  Widget build(BuildContext context) {
    final rankViewModel = context.read<RankingCubit>().getRankViewModel();
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.fromLTRB(8.gw, 20.gw, 8.gw, 20.gw),
        child: BlocSelector<
            RankingCubit,
            RankingState,
            ({
              List<RankingEntity> rankingList,
              DataStatus status,
              RankTabType currentRankTabType,
              RankingEntity? selfRank
            })>(
          selector: (state) => (
            rankingList: state.rankList,
            status: state.rankStatus,
            currentRankTabType: state.currentRankTabType,
            selfRank: state.selfRank,
          ),
          builder: (context, state) {
            return AnimationLimiter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 18.gw,
                children: AnimationConfiguration.toStaggeredList(
                  duration: const Duration(milliseconds: 375),
                  childAnimationBuilder: (widget) => SlideAnimation(
                    verticalOffset: 50.0,
                    child: FadeInAnimation(
                      child: widget,
                    ),
                  ),
                  children: [
                    firstThreeRankSection(
                      rankViewModel,
                      rankingList: state.rankingList.take(3).toList(),
                      status: state.status,
                      currentRankTabType: state.currentRankTabType,
                    ),
                    myRankSection(
                      context,
                      selfRank: state.selfRank,
                      currentRankTabType: state.currentRankTabType,
                      status: state.status,
                    ),
                    otherRankSection(
                      context,
                      currentRankTabType: state.currentRankTabType,
                      rankingList: state.rankingList.skip(3).toList(),
                      status: state.status,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Row firstThreeRankSection(
    List<RankViewModel> rankViewModel, {
    required List<RankingEntity> rankingList,
    required DataStatus status,
    required RankTabType currentRankTabType,
  }) {
    return Row(
      children: rankViewModel
          .map(
            (e) => Expanded(
              flex: e.rank == 1 ? 7 : 6,
              child: status.isLoading
                  ? RankCardShimmer(topMargin: e.rank != 1 ? 55.gw : null)
                  : RankCard(
                      topMargin: e.rank != 1 ? 55.gw : null,
                      rankModel: e.copyWith(label: currentRankTabType.podiumLabel),
                      rankingEntity: rankingList[e.rank - 1],
                    ),
            ),
          )
          .toList(),
    );
  }

  Widget myRankSection(
    BuildContext context, {
    RankingEntity? selfRank,
    required RankTabType currentRankTabType,
    required DataStatus status,
  }) {
    return Padding(
      padding: EdgeInsets.only(left: 5.gw, right: 5.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8.gw,
        children: [
          Text(
            "${'rank.myRanking'.tr()} (${currentRankTabType.label.tr()})",
            style: context.textTheme.title.w600,
          ),
          status.isLoading
              ? RankTileShimmer()
              : RankTile(
                  rankingEntity: selfRank ??
                      RankingEntity(
                        nickName: getIt<UserCubit>().currentUser?.nickname ?? '',
                        avatar: getIt<UserCubit>().currentUser?.avatar ?? '1',
                      ),
                  label: '',
                ),
        ],
      ),
    );
  }

  Widget otherRankSection(
    BuildContext context, {
    required RankTabType currentRankTabType,
    required List<RankingEntity> rankingList,
    required DataStatus status,
  }) {
    return Padding(
      padding: EdgeInsets.only(left: 5.gw, right: 5.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8.gw,
        children: [
          Text("${'rank.otherRankings'.tr()} (${currentRankTabType.label.tr()})", style: context.textTheme.title.w600),
          ...status.isLoading
              ? List.generate(5, (index) => const RankTileShimmer())
              : rankingList.map((e) => RankTile(rankingEntity: e, label: currentRankTabType.podiumLabel)).toList(),
        ],
      ),
    );
  }
}
