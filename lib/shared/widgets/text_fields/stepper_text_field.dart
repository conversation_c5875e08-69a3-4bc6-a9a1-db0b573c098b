import 'package:extra_hittest_area/extra_hittest_area.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/keyboard_config.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:keyboard_actions/keyboard_actions.dart';

import '../text_input_formatter/comma_to_dot_formatter.dart';
import '../text_input_formatter/decimal_text_input_formatter.dart';

/// 步进文本输入框 / Stepper text field widget
///
/// 布局：[-] [输入框] [+]
/// Layout: [-] [TextField] [+]
///
/// 用于价格或数量输入
/// Used for price or quantity input
class StepperTextField extends StatefulWidget {
  /// 当前显示的数值
  /// Current displayed value
  final String displayValue;

  /// 是否可以递减
  /// Whether decrement is allowed
  final bool canDecrement;

  /// 是否可以递增
  /// Whether increment is allowed
  final bool canIncrement;

  /// 是否启用整个控件
  /// Whether the entire widget is enabled
  final bool enabled;

  /// 递增按钮回调
  /// Callback for increment button
  final VoidCallback? onIncrement;

  /// 递减按钮回调
  /// Callback for decrement button
  final VoidCallback? onDecrement;

  /// 数值改变回调（用户手动输入时）
  /// Callback when value changes (manual input)
  final ValueChanged<String> onValueChanged;

  /// 控件高度
  /// Widget height
  final double? height;

  /// 提示文本
  /// Hint text
  final String? hintText;

  /// 小数位数限制（0 = 仅整数，>0 = 指定小数位数）
  /// Decimal digits limit (0 = integers only, >0 = specified decimal places)
  final int decimalDigits;

  /// 失焦或提交时的回调 / Focus commit callback
  /// 返回需要展示的最终文本（若为 null 则保持当前输入）/ Return corrected display text (null to keep current)
  final String? Function(String value)? onFocusChanged;

  const StepperTextField({
    super.key,
    required this.displayValue,
    this.canDecrement = true,
    this.canIncrement = true,
    this.enabled = true,
    this.onIncrement,
    this.onDecrement,
    required this.onValueChanged,
    this.height,
    this.hintText,
    this.decimalDigits = 0,
    required this.onFocusChanged,
  });

  @override
  State<StepperTextField> createState() => _StepperTextFieldState();
}

class _StepperTextFieldState extends State<StepperTextField> {
  final double _buttonSize = 20.gw;
  static const double _buttonRadius = 4.0;
  final double _iconSize = 16.gw;

  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _skipNextBlurCommit = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.displayValue);
    _focusNode = FocusNode();
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void didUpdateWidget(StepperTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当外部displayValue改变时更新TextField
    // Update TextField when external displayValue changes
    if (widget.displayValue != oldWidget.displayValue) {
      // 如果当前有焦点且用户正在编辑，不覆盖用户输入
      // 除非新值和当前输入完全不同（说明是点击了加减按钮）
      // Don't override user input if focused and editing,
      // unless the new value is completely different (indicating increment/decrement was clicked)
      if (_focusNode.hasFocus) {
        // 检查是否是用户手动输入导致的变化（通过 onValueChanged）
        // 还是外部按钮操作导致的变化
        final currentValue = _controller.text;
        final newValue = widget.displayValue;

        // 如果当前输入为空或者新值与当前值差异较大，说明是按钮操作，强制更新
        // If current input is empty or new value differs significantly, it's a button operation, force update
        if (currentValue.isEmpty || _isSignificantChange(currentValue, newValue)) {
          _controller.text = newValue;
        }
      } else {
        // 没有焦点时直接更新
        // Update directly when not focused
        _controller.text = widget.displayValue;
      }
    }
  }

  /// 判断是否是显著变化（非用户输入导致）
  /// Check if it's a significant change (not caused by user input)
  bool _isSignificantChange(String current, String newValue) {
    final currentNum = double.tryParse(current);
    final newNum = double.tryParse(newValue);

    if (currentNum == null || newNum == null) {
      return true; // 无法解析，认为是显著变化
    }

    // 如果数值变化了，说明是按钮操作
    // If the numeric value changed, it's a button operation
    return currentNum != newNum;
  }

  void _handleFocusChange() {
    if (!_focusNode.hasFocus) {
      if (_skipNextBlurCommit) {
        _skipNextBlurCommit = false;
        return;
      }
      _commitCurrentValue();
    } else {
      _skipNextBlurCommit = false;
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 获取输入格式限制器 / Get input formatters
  List<TextInputFormatter> _getInputFormatters() {
    if (widget.decimalDigits == 0) {
      // 仅整数 / Integers only
      return [
        FilteringTextInputFormatter.digitsOnly,
      ];
    } else {
      // 限制小数位数 / Limit decimal digits
      return [
        FilteringTextInputFormatter.allow(RegExp(r'[\d\.,]')), // 允许数字、点、逗号
        CommaToDeFormatter(), // 逗号转点号
        DecimalTextInputFormatter(decimalDigits: widget.decimalDigits),
      ];
    }
  }

  /// 处理点击输入框 - 全选文本 / Handle text field tap - select all
  void _handleTextFieldTap() {
    _controller.selection = TextSelection(
      baseOffset: 0,
      extentOffset: _controller.text.length,
    );
  }

  /// 获取键盘类型 / Get keyboard type
  TextInputType _getKeyboardType() {
    if (widget.decimalDigits == 0) {
      return TextInputType.number; // 纯数字键盘
    } else {
      return const TextInputType.numberWithOptions(decimal: true); // 带小数点的数字键盘
    }
  }

  @override
  Widget build(BuildContext context) {
    // 同步 displayValue 到 controller（没有焦点时）
    if (_controller.text != widget.displayValue && !_focusNode.hasFocus) {
      _controller.text = widget.displayValue;
    }

    return Container(
      height: widget.height ?? 32.gw,
      padding: EdgeInsets.symmetric(horizontal: 8.gw),
      decoration: BoxDecoration(
        color: context.theme.inputDecorationTheme.fillColor,
        borderRadius: BorderRadius.circular(5.gr),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        spacing: 8.gw,
        children: [
          // 减号按钮
          _buildStepButton(
            icon: Icons.remove,
            onPressed: widget.canDecrement && widget.enabled ? widget.onDecrement : null,
          ),

          // 数值输入框 / Value input field
          Expanded(
            child: KeyboardActions(
              config: KeyboardConfig.buildTradeConfig(context, _focusNode),
              autoScroll: false,
              disableScroll: true,
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                enabled: widget.enabled,
                textAlign: TextAlign.center,
                textAlignVertical: TextAlignVertical.center,
                textInputAction: TextInputAction.done,
                keyboardType: _getKeyboardType(),
                inputFormatters: _getInputFormatters(),
                onTap: _handleTextFieldTap,
                style: context.textTheme.primary,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  hintText: widget.hintText,
                  hintStyle: context.textTheme.regular.fs13,
                  contentPadding: EdgeInsets.zero,
                  isDense: true,
                ),
                onChanged: (value) {
                  _skipNextBlurCommit = false;
                  widget.onValueChanged(value);
                },
                onSubmitted: (value) {
                  _skipNextBlurCommit = true;
                  _commitCurrentValue();
                },
              ),
            ),
          ),

          // 加号按钮
          _buildStepButton(
            icon: Icons.add,
            onPressed: widget.canIncrement && widget.enabled ? widget.onIncrement : null,
          ),
        ],
      ),
    );
  }

  void _commitCurrentValue() {
    final corrected = widget.onFocusChanged?.call(_controller.text);
    _applyCommitResult(corrected);
  }

  /// 构建步进按钮（+/-）
  /// Build step button (+/-)
  Widget _buildStepButton({
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    return GestureDetectorHitTestWithoutSizeLimit(
      extraHitTestArea: EdgeInsets.all(8.gw),
      behavior: HitTestBehavior.opaque,
      onTap: onPressed,
      child: Container(
        height: _buttonSize,
        width: _buttonSize,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(_buttonRadius.gr),
          color: context.theme.cardColor,
        ),
        child: Center(
          child: Icon(
            icon,
            size: _iconSize,
            color: onPressed == null ? context.theme.dividerColor : context.colorTheme.textPrimary,
          ),
        ),
      ),
    );
  }

  /// 根据外部回调返回结果更新输入框 / Apply corrected text returned from callback
  void _applyCommitResult(String? corrected) {
    if (corrected == null || corrected == _controller.text) return;
    _controller
      ..text = corrected
      ..selection = TextSelection.fromPosition(TextPosition(offset: corrected.length));
  }
}
