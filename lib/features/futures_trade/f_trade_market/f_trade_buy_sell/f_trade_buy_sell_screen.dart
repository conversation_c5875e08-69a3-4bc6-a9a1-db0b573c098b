import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/models/entities/trade/depth_quote.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/f_trade_acct_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/logic/f_trade_center_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/logic/f_trade_buy_sell_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/f_trade_buy_sell_Dialog.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/subviews.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class FTradeBuySellScreen extends StatefulWidget {
  final FuturesMarketType type;
  final FTradeListItemModel data;
  final void Function(int) onChangeAllInfoScreenTitlesAction;

  const FTradeBuySellScreen({
    super.key,
    required this.data,
    required this.onChangeAllInfoScreenTitlesAction,
    required this.type,
  });

  @override
  State<FTradeBuySellScreen> createState() => _FTradeBuySellScreenState();
}

class _FTradeBuySellScreenState extends State<FTradeBuySellScreen> {
  late String instrument;
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    instrument = widget.data.makeInstrument();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startAfterBuild();
    });
  }

  Future<void> _startAfterBuild() async {
    context.read<FTradeKLineCubit>().fetchTradeState(market: widget.data.market, productCode: widget.data.productCode);
    context.read<FTradeBuySellCubit>().fetchUsableCash();
    context.read<FTradeBuySellCubit>().startUsableCashPolling();
    context.read<FTradeKLineCubit>().fetchFQuotationSubData(instrument: instrument);
    context.read<FTradeKLineCubit>().startPolling(allOrSub: false);
    await context.read<FTradeBuySellCubit>().fetchConfigData(
      instrument,
      market: widget.data.market,
      productCode: widget.data.productCode,
    );
    if (!mounted) return;
  }

  @override
  void didUpdateWidget(FTradeBuySellScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    final newInstrument = widget.data.makeInstrument();

    // 如果标的发生变化，更新本地状态以保持一致性
    // If instrument changed, update local state for consistency
    if (instrument != newInstrument) {
      setState(() {
        instrument = newInstrument;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double bottomPadding = MediaQuery.of(context).padding.bottom;

    return BlocListener<FTradeCenterCubit, FTradeCenterState>(
      listenWhen: (previous, current) {
        final prevInstrument = previous.data.makeInstrument();
        final currInstrument = current.data.makeInstrument();
        final instrumentChanged = prevInstrument != currInstrument;
        final tabBecameTrade =
            previous.tabType != current.tabType && current.tabType == TradeTabType.Trading;
        return instrumentChanged || tabBecameTrade;
      },
      listener: (context, state) {
        if (!_scrollController.hasClients) return;
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      },
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 8.gw),
        child: AnimationLimiter(
          child: Column(
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 600),
              childAnimationBuilder: (widget) => SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(child: widget),
              ),
              children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 12.gw),
                decoration: BoxDecoration(
                  color: context.theme.cardColor,
                  borderRadius: BorderRadius.circular(10.gr),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildTopHeader(),
                    8.verticalSpace,
                    _buildActionsView(),
                  ],
                ),
              ),
              16.verticalSpace,
              BlocProvider(
                create: (context) => AccountScreenCubitV2(),
                child: FTradeAcctScreen(
                  type: widget.type,
                  data: widget.data,
                  onChangeAllInfoScreenTitlesAction: widget.onChangeAllInfoScreenTitlesAction,
                ),
              ),
                SizedBox(height: bottomPadding),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopHeader() {
    final cubit = context.read<FTradeBuySellCubit>();
    return BlocSelector<FTradeKLineCubit, FTradeKLineState,
        ({FTradeInfoModel infoModel, DepthQuote? depthModel})>(
      selector: (state) =>
          (infoModel: state.fTradeInfoModel ?? widget.data.toFTradeInfoModel(), depthModel: state.fTradeDepthModel),
      builder: (context, state) {
        return FTradeBuySellHeaderView(
          data: state.infoModel,
          type: widget.type,
          depthData: state.depthModel,
          onTapDepthOrder: (item) {
            if (item.price == 0) return;
            // 点击深度档位，设置为半仓
            cubit.handleUserInputControllerChanged(cubit.state.userInputController.copyWith(
              orderFraction: OrderFraction.oneHalf,
              priceType: OrderPriceType.limit,
              price: item.price,
              inputNumberEnum: FTradeBuySellUserInputNumberEnum.selected,
            ));
          },
        );
      },
    );
  }

  Widget _buildActionsView() {
    return BlocBuilder<FTradeBuySellCubit, FTradeBuySellState>(
      builder: (BuildContext context, state) {
        return FTradeBuySellInputView(
          accountUsableCash: state.accountUsableCash,
          latestPrice: state.fTradeLatestPrice ?? 0.0,
          fTradeConfigModel: state.fTradeConfigModel,
          userInputState: state.userInputController,
          onUserInputControllerChanged: (oldInputCtrl) {
            context.read<FTradeBuySellCubit>().handleUserInputControllerChanged(oldInputCtrl);
          },
          buyActionsController: state.longActionsController.copyWith(
            onConfirmPressed: () async {
              FocusScope.of(context).requestFocus(FocusNode());
              await Future.delayed(Duration(milliseconds: 150));
              if (!context.mounted) return;

              showDialog(
                context: context,
                builder: (_) {
                  return BlocProvider.value(
                    value: context.read<FTradeBuySellCubit>(),
                    child: FTradeBuySellDialog(
                      longOrShort: true,
                      submit: () {
                        context.read<FTradeBuySellCubit>().createOrder('buy');
                      },
                    ),
                  );
                },
              );
            },
          ),
          sellActionsController: state.shortActionsController.copyWith(
            onConfirmPressed: () async {
              FocusScope.of(context).requestFocus(FocusNode());
              await Future.delayed(Duration(milliseconds: 150));
              if (!context.mounted) return;

              showDialog(
                context: context,
                builder: (_) {
                  return BlocProvider.value(
                    value: context.read<FTradeBuySellCubit>(),
                    child: FTradeBuySellDialog(
                      longOrShort: false,
                      submit: () {
                        context.read<FTradeBuySellCubit>().createOrder('sell');
                      },
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}
