import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/ranking/models/rank_tab.dart';
import 'package:gp_stock_app/shared/widgets/buttons/gstext_image_button.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import 'home_ranking_cell.dart';

class HomeRankingSection extends StatelessWidget {
  final Map<RankTabType, List<RankingEntity>> rankMap;
  final RankTabType currentRankTabType;
  final ValueChanged<RankTabType> onTabChanged;
  final VoidCallback onMoreTapped;
  final EdgeInsetsGeometry? margin;

  const HomeRankingSection({
    super.key,
    required this.rankMap,
    required this.currentRankTabType,
    required this.onTabChanged,
    required this.onMoreTapped,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: EdgeInsets.fromLTRB(8.gw, 2.gw, 8.gw, 12.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.gw),
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFFED2D2), Color(0xFFFFFFFF)],
          stops: [0.0096, 0.2575],
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTabBar(context),
          SizedBox(height: 16.gw),
          _buildListView(context),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    final tabs = RankTabType.values.map((e) => e.label.tr()).toList();
    final currentIndex = RankTabType.values.indexOf(currentRankTabType);

    return Row(
      children: [
        Image.asset("assets/icons/rank_home_title.png", width: 28.gw, height: 28.gw),
        SizedBox(width: 14.gw),
        Expanded(
          child: CommonTabBar.withAutoKey(tabs,
              currentIndex: currentIndex,
              onTap: (index) {
                onTabChanged(RankTabType.values[index]);
              },
              styleOverrides: (config) => config.withIndicator(
                    width: 10.gw,
                    bottom: -2.gw,
                  )),
        ),
        GSTextImageButton(
          text: "more".tr(),
          imageAssets: "assets/svg/arrow_right.svg",
          imageSize: Size(15.gw, 10.gw),
          interval: 3.gw,
          position: GSTextImageButtonPosition.right,
          onPressed: onMoreTapped,
          textStyle: TextStyle(
            fontSize: 12.gsp,
            color: Color(0xFFA3A3A3),
          ),
        ),
      ],
    );
  }

  Widget _buildListView(BuildContext context) {
    final currentList = rankMap[currentRankTabType] ?? [];

    if (currentList.isEmpty) {
      return Container(
        padding: EdgeInsets.symmetric(vertical: 40.gw),
        child: Center(
          child: Text(
            "no_data".tr(),
            style: TextStyle(
              color: context.colorTheme.textRegular,
              fontSize: 14.gsp,
            ),
          ),
        ),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return HomeRankingCell(
          model: currentList[index],
          rankType: currentRankTabType,
        );
      },
      separatorBuilder: (_, __) => SizedBox(height: 9.5.gw),
      itemCount: currentList.length,
    );
  }
}
