import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/apis/market_alert.dart';
import 'package:gp_stock_app/core/models/entities/market/warn_response_entity.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'warning_list_state.dart';

class WarningListCubit extends Cubit<WarningListState> {
  WarningListCubit() : super(const WarningListState());

  /// 获取预警列表 / Get warning list
  ///
  /// [loadMore] 是否为加载更多 / Whether to load more
  /// [market] 市场代码 / Market code
  Future<void> getWarnList({bool loadMore = false, required String market}) async {
    // 防止重复加载 / Prevent duplicate loading
    if (loadMore && !state.hasMore) return;
    if (loadMore && state.loadMoreStatus.isLoading) return;
    if (state.listFetchStatus.isLoading) return;

    emit(
      state.copyWith(
        listFetchStatus: DataStatus.loading,
        currentPage: loadMore ? state.warnings.current + 1 : 1,
        isLoadMore: loadMore,
        loadMoreStatus: loadMore ? DataStatus.loading : DataStatus.success,
      ),
    );

    final result = await MarketAlertApi.getWarnList(
      market: market,
      pageNumber: state.currentPage.toString(),
      pageSize: '20',
    );

    if (result != null) {
      // 合并或替换列表 / Merge or replace list
      final newWarnList = loadMore ? [...state.warnings.records, ...result.records] : result.records;

      emit(
        state.copyWith(
          listFetchStatus: DataStatus.success,
          loadMoreStatus: DataStatus.success,
          warnings: state.warnings.copyWith(
            records: newWarnList,
            total: result.total,
            current: result.current,
            pages: result.pages,
            size: result.size,
          ),
          hasMore: (newWarnList.length < (result.total)),
        ),
      );
    } else {
      emit(state.copyWith(
        listFetchStatus: DataStatus.failed,
        loadMoreStatus: DataStatus.failed,
      ));
    }
  }

  /// 更新选中的市场分类 / Update selected market category
  ///
  /// [category] 市场分类 / Market category
  void updateMarket(MarketCategory category) {
    emit(state.copyWith(selectedMarketCategory: category));
  }

  /// 删除预警 / Delete warning
  ///
  /// [id] 预警ID / Warning ID
  ///
  /// 删除成功后会自动从列表中移除该项
  /// After successful deletion, the item will be automatically removed from the list
  Future<void> deleteWarn(String id) async {
    emit(state.copyWith(deleteStatus: DataStatus.loading));

    final success = await MarketAlertApi.deleteWarn(id);

    if (success) {
      // 从列表中移除已删除的项 / Remove deleted item from list
      final newWarnList = state.warnings.records.where((element) => element.id.toString() != id).toList();

      emit(state.copyWith(
        deleteStatus: DataStatus.success,
        warnings: state.warnings.copyWith(
          records: newWarnList,
          total: state.warnings.total - 1,
        ),
      ));
    } else {
      emit(state.copyWith(
        deleteStatus: DataStatus.failed,
      ));
    }
  }
}
