import 'dart:ui';

class RankViewModel {
  final int rank;
  final Color gradientColor;
  final Color borderColor;
  final String label;

  const RankViewModel({
    required this.rank,
    required this.gradientColor,
    required this.borderColor,
    this.label = '',
  });

  RankViewModel copyWith({
    String? label,
  }) {
    return RankViewModel(
      rank: rank,
      gradientColor: gradientColor,
      borderColor: borderColor,
      label: label ?? this.label,
    );
  }
}
