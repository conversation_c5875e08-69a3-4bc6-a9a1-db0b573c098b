import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/home_v2/home/<USER>/market/market_data_table.dart';
import 'package:gp_stock_app/shared/mixins/locale_aware_mixins.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import '../../shared/constants/enums.dart';
import '../../shared/mixin/animation.dart';
import '../../shared/widgets/pagination/pagination_widget.dart';
import 'logic/market/market_cubit.dart';
import 'widgets/general_section.dart';

import 'widgets/today_section.dart';
import 'widgets/ui/wr_market.dart';

class StockScreen extends StatefulWidget {
  final bool showBackButton;
  final ScrollController? scrollController;

  const StockScreen({
    super.key,
    this.showBackButton = false,
    this.scrollController,
  });

  @override
  State<StockScreen> createState() => _StockScreenState();
}

class _StockScreenState extends State<StockScreen> with StaggeredAnimation, LocaleAwareScreenMixin {
  Timer? _marketPollingTimer;
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    _initializeMarketData();
  }

  @override
  void onLocaleChanged(String locale) => _initializeMarketData();

  void _initializeMarketData() => context.read<MarketCubit>().init();

  Future<void> _handleRefresh() async {
    try {
      context.read<MarketCubit>().init(isRefresh: true);
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('refresh_failed'.tr())),
        );
      }
    }
  }

  @override
  void dispose() {
    _marketPollingTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<MarketCubit, MarketState>(
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 16.gw, right: 16.gw, bottom: 4.gw),
                child: _TodaySection(),
              ),
              Expanded(
                child: RefreshIndicator.adaptive(
                  backgroundColor: context.theme.cardColor,
                  key: _refreshIndicatorKey,
                  onRefresh: _handleRefresh,
                  child: PaginationWidget(
                    isPaginating: state.tableFetchStatus == DataStatus.loading && state.isPaginating,
                    next: _hasMoreData(state),
                    onPagination: (notification) => _handlePagination(state),
                    child: _buildScrollableContent(state),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  bool _hasMoreData(MarketState state) {
    return (state.tableData?.list.length ?? 0) <= (state.tableData?.totalNum ?? 0);
  }

  bool _handlePagination(MarketState state) {
    if (_isMaxDataReached(state)) return false;
    _loadNextPage(state);

    return true;
  }

  bool _isMaxDataReached(MarketState state) {
    return state.tableData?.list.length == (state.tableData?.totalNum ?? 0);
  }

  bool _isLoadingNextPage = false;

  void _loadNextPage(MarketState state) async {
    if (_isLoadingNextPage) return;

    _isLoadingNextPage = true;
    try {
      await context.read<MarketCubit>().fetchTableData(isLoadMore: true);
    } finally {
      _isLoadingNextPage = false;
    }
  }

  Widget _buildScrollableContent(MarketState state) {
    return SingleChildScrollView(
      controller: widget.scrollController,
      physics: const AlwaysScrollableScrollPhysics(),
      child: Padding(
        padding: EdgeInsets.only(left: 16.gw, right: 16.gw, top: 0),
        child: AnimationLimiter(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 300),
              childAnimationBuilder: (widget) => SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: [
                Column(
                  children: [
                    const WRMarket(
                      child: TodaySection(),
                    ),
                    BlocBuilder<MarketCubit, MarketState>(
                      builder: (context, state) => WRMarket(
                        child: MarketGeneralSection(marketCategory: state.selectedMarketCategory),
                      ),
                    ),
                    const WRMarket(
                      child: MarketDataTable(),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _TodaySection extends StatelessWidget {
  const _TodaySection();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MarketCubit, MarketState>(
      builder: (context, state) {
        return AnimationLimiter(
          child: AnimationConfiguration.synchronized(
            child: SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildMarketTabs(context, state.selectedMarketCategory),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMarketTabs(BuildContext context, MarketCategory selectedCategory) {
    // 动态获取所有股票类型的市场 / Dynamically get all stock markets
    final tabs = MarketCategory.values.where((e) => e.securityType == '1').toList();

    return SizedBox(
      width: double.infinity,
      child: CommonTabBar.withAutoKey(
        tabs.map((tab) => tr(tab.nameKey)).toList(),
        currentIndex: tabs.indexWhere((tab) => tab == selectedCategory),
        onTap: (index) => context.read<MarketCubit>().updateMarketCategory(tabs[index]),
        style: CommonTabBarStyle.line,
        isScrollable: true,
        backgroundColor: Colors.transparent,
        height: 40.gw,
      ),
    );
  }
}
