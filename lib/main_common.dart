import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/connectivity_util.dart';
import 'package:gp_stock_app/core/utils/host_util/host_util.dart';
import 'package:gp_stock_app/core/utils/language_util.dart';
import 'package:gp_stock_app/features/chat/logic/chat/chat_cubit.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/features/notifications/logic/notifications_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/app_info/app_info_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/auth_n/auth_n_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/profile/profile_cubit.dart';
import 'package:gp_stock_app/my_app.dart';

import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';

import 'core/dependency_injection/injectable.dart';
import 'core/services/user/user_cubit.dart';
import 'core/utils/app_lifecycle_service.dart';
import 'features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'features/activity/activity_cubit.dart';
import 'features/main/logic/main/main_cubit.dart';
import 'features/market/logic/market/market_cubit.dart';
import 'features/profile/logic/mission_center/mission_activity_cubit.dart';
import 'features/profile/logic/vip/vip_cubit.dart';
import 'features/sign_in/logic/sign_in/sign_in_cubit.dart';
import 'shared/logic/sort_color/sort_color_cubit.dart';
import 'shared/logic/theme/theme_cubit.dart';
import 'shared/routes/routers.dart';

Future<void> mainCommon() async {
  WidgetsFlutterBinding.ensureInitialized();
  AppLifecycleService.instance.init();
  ConnectivityUtil().initialize();
  await EasyLocalization.ensureInitialized();

  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: await getApplicationDocumentsDirectory(),
  );
  configureDependencies();

  await HostUtil().fetchAndApplyHost();

  // 初始化路由
  Routes.initRoutes();

  runApp(_Translation());
}

class _Translation extends StatelessWidget {
  const _Translation();

  @override
  Widget build(BuildContext context) {
    // Get system default locale, fallback to English if not supported
    final systemLocale = LanguageUtil.getSystemDefaultLocale();

    return EasyLocalization(
      supportedLocales: LanguageUtil.supportedLocales,
      path: 'assets/translations',
      fallbackLocale: LanguageUtil.fallbackLocale,
      startLocale: systemLocale,
      child: const _MultiBlocWrapper(),
    );
  }
}

class _MultiBlocWrapper extends StatelessWidget {
  const _MultiBlocWrapper();

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => ThemeCubit()),
        BlocProvider(create: (context) => getIt<SignInCubit>()),
        BlocProvider(create: (context) => getIt<UserCubit>()),
        BlocProvider(create: (context) => ProfileCubit()),
        BlocProvider(create: (context) => getIt<MarketStatusCubit>()..init(), lazy: false),

        BlocProvider(
          create: (context) => getIt<AccountScreenCubitV2>(),
          lazy: false,
        ),
        BlocProvider(
          create: (context) => getIt<MarketCubit>()
            ..fetchTableData(isHome: true)
            ..fetchTableData(isHome: true),
        ),
        BlocProvider(create: (context) => getIt<MainCubit>()),
        BlocProvider(create: (context) => getIt<ActivityCubit>()..getTasks()),
        BlocProvider(create: (context) => getIt<AuthNCubit>()),
        BlocProvider(create: (context) => getIt<SortColorCubit>()),
        BlocProvider(create: (context) => getIt<NotificationsCubit>()..getNotificationCount()),
        BlocProvider(create: (context) => getIt<IndexTradeCubit>()..init(), lazy: false),
        BlocProvider(create: (context) => getIt<ExchangeRateCubit>()..fetchExchangeRate(), lazy: false),
        BlocProvider(create: (context) => SysSettingsCubit()..fetchSysSettings(), lazy: false),
        BlocProvider(create: (context) => getIt<WatchListCubit>()),
        BlocProvider(create: (context) => getIt<ChatCubit>()),
        BlocProvider(
            create: (context) => getIt<MissionActivityCubit>()
              ..getSignInLog()
              ..getServerDate(),
            lazy: false),
        BlocProvider(create: (context) => VipCubit()..getUserLevelConfig()),
        BlocProvider(create: (context) => getIt<AppInfoCubit>()..getAppInfoList(), lazy: false),
      ],
      child: MyApp(), // Pass routeObserver to MyApp
    );
  }
}
