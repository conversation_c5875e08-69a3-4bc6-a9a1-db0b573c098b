// forgot_screen.dart
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/utilities/auth_style.dart';
import 'package:gp_stock_app/shared/constants/enums/signup_type.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import '../../../core/utils/validators.dart';
import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/constants/assets.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/widgets/app_header.dart';
import '../../../shared/widgets/otp_field.dart';
import '../../../shared/widgets/text_fields/text_field_widget.dart';
import '../../account/logic/otp/otp_cubit.dart';
import '../../account/logic/otp/otp_state.dart';
import '../logic/forgot/forgot_cubit.dart';
import '../logic/forgot/forgot_state.dart';

class ForgotScreen extends StatelessWidget with AppHeaderMixin {
  const ForgotScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        body: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: AuthStyle.headerWrapper(
            appheader: buildAppHeader(context, showHeaderImage: true),
            loginForm: _ForgotForm(),
          ),
        ),
      ),
    );
  }
}

class _ForgotForm extends StatefulWidget {
  @override
  State<_ForgotForm> createState() => _ForgotFormState();
}

class _ForgotFormState extends State<_ForgotForm> {
  final TextEditingController phoneEmailController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  final OtpCubit otpCubit = OtpCubit();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    phoneEmailController.dispose();
    codeController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    otpCubit.close();
    super.dispose();
  }

  void _handleSendVerificationCode(SignUpType signUpType) {
    // Validate only the phone field
    if (phoneEmailController.text.isEmpty ||
        (Validators.validateMobile(phoneEmailController.text) != null && signUpType == SignUpType.phone ||
            Validators.validateEmail(phoneEmailController.text) != null && signUpType == SignUpType.email)) {
      formKey.currentState?.validate();
      return;
    }

    otpCubit.verifyAndSendOtp(mobileOrEmail: phoneEmailController.text, type: signUpType.otpTypeUpdatePassword);
  }

  void _handleReset(BuildContext context) {
    // Validate all form fields
    if (!formKey.currentState!.validate()) {
      return;
    }

    context.read<ForgotCubit>().resetPassword(
          mobileOrEmail: phoneEmailController.text,
          password: passwordController.text,
          code: codeController.text,
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => otpCubit,
      child: MultiBlocListener(
        listeners: [
          BlocListener<OtpCubit, OtpState>(
              listenWhen: (previous, current) => previous.sendStatus != current.sendStatus,
              listener: (context, state) {
                if (state.sendStatus == DataStatus.loading) {
                  GPEasyLoading.showLoading(message: 'sendingCode'.tr());
                } else if (state.sendStatus == DataStatus.success && state.isSent) {
                  GPEasyLoading.showSuccess(message: 'verificationCodeSent'.tr());
                }
              }),
          BlocListener<ForgotCubit, ForgotState>(listener: (context, state) {
            if (state.status == DataStatus.success) {
              Navigator.pop(context);
            }
          }),
        ],
        child: AnimationConfiguration.synchronized(
          duration: const Duration(milliseconds: 800),
          child: SlideAnimation(
            verticalOffset: 100.0,
            child: FadeInAnimation(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: context.theme.cardColor,
                  borderRadius: AuthStyle.loginFormBorder,
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.gw),
                  child: AnimationLimiter(
                    child: BlocSelector<ForgotCubit, ForgotState, SignUpType>(
                      selector: (state) => state.signUpType,
                      builder: (context, signUpType) {
                        return Form(
                          key: formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: AnimationConfiguration.toStaggeredList(
                              duration: const Duration(milliseconds: 600),
                              childAnimationBuilder: (widget) => SlideAnimation(
                                verticalOffset: 50.0,
                                child: FadeInAnimation(
                                  child: widget,
                                ),
                              ),
                              children: [
                                12.verticalSpace,
                                Text(
                                  'forgotPasswordTitle'.tr(),
                                  style: context.textTheme.primary.fs18.w600.copyWith(
                                    color: context.theme.primaryColor,
                                  ),
                                ),
                                12.verticalSpace,
                                CommonTabBar(
                                  data: ['signUpMobile'.tr(), 'signUpEmail'.tr()],
                                  onTap: (index) => context.read<ForgotCubit>().selectTab(SignUpType.values[index]),
                                  currentIndex: signUpType.index,
                                  style: switch (AppConfig.instance.skinStyle) {
                                    AppSkinStyle.kGP => CommonTabBarStyle.round,
                                    AppSkinStyle.kTemplateD => CommonTabBarStyle.trapezoid,
                                    _ => CommonTabBarStyle.line,
                                  },
                                  backgroundColor: Colors.transparent,
                                  isScrollable: false,
                                  styleOverrides: AuthStyle.styleOverrides(context),
                                ),
                                12.verticalSpace,
                                BlocBuilder<OtpCubit, OtpState>(
                                  builder: (context, otpState) {
                                    return OtpField(
                                      mobileController: phoneEmailController,
                                      codeController: codeController,
                                      otpState: otpState,
                                      onSendCode: () => _handleSendVerificationCode(signUpType),
                                      mobileField: TextFieldWidget(
                                        controller: phoneEmailController,
                                        hintText: signUpType == SignUpType.phone
                                            ? 'loginPhonePlaceholder'.tr()
                                            : 'loginEmailPlaceholder'.tr(),
                                        textInputType: signUpType == SignUpType.phone
                                            ? TextInputType.phone
                                            : TextInputType.emailAddress,
                                        maxLength: signUpType == SignUpType.phone ? 11 : null,
                                        counterText: '',
                                        validator: (value) => signUpType == SignUpType.phone
                                            ? Validators.validateMobile(value)
                                            : Validators.validateEmail(value),
                                        prefixIcon: SvgPicture.asset(
                                          signUpType == SignUpType.phone ? Assets.mobileIcon : Assets.mailIcon,
                                          fit: BoxFit.scaleDown,
                                          width: 18.gw,
                                          height: 18.gw,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                                15.verticalSpace,
                                TextFieldWidget(
                                  hintText: 'registerPasswordHint'.tr(),
                                  controller: passwordController,
                                  textInputType: TextInputType.visiblePassword,
                                  prefixIcon: SvgPicture.asset(
                                    Assets.lockIcon,
                                    fit: BoxFit.scaleDown,
                                    width: 18.gw,
                                    height: 18.gw,
                                  ),
                                  obscureText: true,
                                  passwordIcon: true,
                                  validator: (value) => Validators.validatePassword(value),
                                ),
                                15.verticalSpace,
                                TextFieldWidget(
                                  hintText: 'registerConfirmPassword'.tr(),
                                  controller: confirmPasswordController,
                                  textInputType: TextInputType.visiblePassword,
                                  prefixIcon: SvgPicture.asset(
                                    Assets.lockIcon,
                                    fit: BoxFit.scaleDown,
                                    width: 18.gw,
                                    height: 18.gw,
                                  ),
                                  obscureText: true,
                                  passwordIcon: true,
                                  validator: (value) =>
                                      value != passwordController.text ? 'passwordsDoNotMatch'.tr() : null,
                                ),
                                24.verticalSpace,
                                AnimationConfiguration.synchronized(
                                  duration: const Duration(milliseconds: 300),
                                  child: ScaleAnimation(
                                    scale: 0.95,
                                    child: CommonButton(
                                      title: 'reset'.tr(),
                                      onPressed: () => _handleReset(context),
                                      radius: AuthStyle.buttonBorderRadius,
                                    ),
                                  ),
                                ),
                                8.verticalSpace,
                                Center(
                                  child: AnimationConfiguration.synchronized(
                                    duration: const Duration(milliseconds: 300),
                                    child: ScaleAnimation(
                                      scale: 0.95,
                                      child: TextButton(
                                        onPressed: () => Navigator.pop(context),
                                        style: TextButton.styleFrom(
                                          foregroundColor: context.theme.primaryColor,
                                          minimumSize: Size.zero,
                                          padding: EdgeInsets.all(8.gr),
                                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                        ),
                                        child: Text("backToLogin".tr()),
                                      ),
                                    ),
                                  ),
                                ),
                                switch (AppConfig.instance.skinStyle) {
                                  AppSkinStyle.kZangGolden => SizedBox(height: 130.gw),
                                  _ => SizedBox(height: 80.gw),
                                }
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
