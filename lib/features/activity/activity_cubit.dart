import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/home.dart';
import 'package:gp_stock_app/core/models/apis/task.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:injectable/injectable.dart';

import '../../shared/app/extension/helper.dart';
import '../../shared/constants/enums.dart';

part 'activity_state.dart';

@injectable
class ActivityCubit extends AuthAwareCubit<ActivityState> {
  ActivityCubit() : super(const ActivityState());

  @override
  void onLoggedIn() => getTasks();

  @override
  void onLoggedOut() => emit(const ActivityState());

  void updateTab(int index) => emit(state.copyWith(selectedTab: index));

  void updateSelectedTab(int index) => emit(state.copyWith(selectedEventIndex: index));

  Future<void> getTasks() async {
    emit(state.copyWith(tasksFetchStatus: DataStatus.loading));
    TaskApi.fetchTaskCenterData();

    final res = await TaskApi.fetchTaskCenterData();
    if (res == null) {
      emit(state.copyWith(tasksFetchStatus: DataStatus.failed));
      return;
    }

    emit(state.copyWith(
      tasksFetchStatus: DataStatus.success,
      newUserTasks: res.newUser,
      tradeTasks: res.trade,
      dailyTasks: res.daily,
      signInRules: res.signInRules,
      error: null,
    ));
  }

  /// 获取热门活动通知 / Fetch hot events notifications
  Future<void> fetchNotifications() async {
    if (isClosed) return;
    emit(state.copyWith(notificationStatus: DataStatus.loading));

    final result = await HomeApi.fetchNotificationList();
    if (isClosed) return;

    if (result == null) {
      emit(state.copyWith(notificationStatus: DataStatus.failed));
      return;
    }

    // 过滤 type == 1 的通知（热门活动）/ Filter notifications with type == 1 (hot events)
    final hotEvents = result.where((e) => e.type == 1).toList();
    emit(state.copyWith(
      notifications: hotEvents,
      notificationStatus: DataStatus.success,
    ));
  }

  Future<void> collectReward(TaskEntity task) async {
    if (task.isCompleted != true || task.isReceived == true) return;
    if (task.id == 0) return;

    emit(state.copyWith(rewardCollectionStatus: DataStatus.loading));
    final result = await TaskApi.collectReward(task.id);

    if (result == null) {
      emit(state.copyWith(rewardCollectionStatus: DataStatus.failed));
      return;
    }

    Helper.showFlutterToast('rewardCollected'.tr());
    await getTasks();
    emit(state.copyWith(rewardCollectionStatus: DataStatus.success));
  }
}
