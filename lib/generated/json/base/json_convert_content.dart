// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:gp_stock_app/core/models/entities/account/account.dart';
import 'package:gp_stock_app/core/models/entities/activity/sign_in_day_entity.dart';
import 'package:gp_stock_app/core/models/entities/chat/chat_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/common/app_info_entity.dart';
import 'package:gp_stock_app/core/models/entities/common/exchange_rate_entity.dart';
import 'package:gp_stock_app/core/models/entities/common/file_upload_entity.dart';
import 'package:gp_stock_app/core/models/entities/common/sys_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/common/sys_settings_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_normal_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_trial_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract_appley_record_list.dart';
import 'package:gp_stock_app/core/models/entities/fund_account/fund_pay_way.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/models/entities/invite/invite_detail_entity.dart';
import 'package:gp_stock_app/core/models/entities/invite/invite_info_entity.dart';
import 'package:gp_stock_app/core/models/entities/invite/rebate_paginated_response_entity.dart';
import 'package:gp_stock_app/core/models/entities/invite/subordinate_paginated_response_entity.dart';
import 'package:gp_stock_app/core/models/entities/market/dist_and_flow.dart';
import 'package:gp_stock_app/core/models/entities/market/gain_distribution.dart';
import 'package:gp_stock_app/core/models/entities/market/index_stock_config.dart';
import 'package:gp_stock_app/core/models/entities/market/market_plate_list.dart';
import 'package:gp_stock_app/core/models/entities/market/market_search_result.dart';
import 'package:gp_stock_app/core/models/entities/market/market_status_info.dart';
import 'package:gp_stock_app/core/models/entities/market/plate_info_list.dart';
import 'package:gp_stock_app/core/models/entities/market/quotation_broker.dart';
import 'package:gp_stock_app/core/models/entities/market/stock_company_info.dart';
import 'package:gp_stock_app/core/models/entities/market/stock_kline_entity.dart';
import 'package:gp_stock_app/core/models/entities/market/stock_news_entity.dart';
import 'package:gp_stock_app/core/models/entities/market/warn_response_entity.dart';
import 'package:gp_stock_app/core/models/entities/member/auth_n_info_entity.dart';
import 'package:gp_stock_app/core/models/entities/member/next_user_level_entity.dart';
import 'package:gp_stock_app/core/models/entities/member/vip_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/notification/notification_paginated_response_entity.dart';
import 'package:gp_stock_app/core/models/entities/ranking/rank_config.dart';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';
import 'package:gp_stock_app/core/models/entities/system/app_version_entity.dart';
import 'package:gp_stock_app/core/models/entities/system/help_category_entity.dart';
import 'package:gp_stock_app/core/models/entities/task/reward_entity.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';
import 'package:gp_stock_app/core/models/entities/trade/depth_quote.dart';
import 'package:gp_stock_app/core/models/entities/trade/index_store_order.dart';
import 'package:gp_stock_app/core/models/entities/trade/order_detail.dart';
import 'package:gp_stock_app/core/models/entities/trade/stock_info_v2.dart';
import 'package:gp_stock_app/core/models/entities/trade/tick_response.dart';
import 'package:gp_stock_app/core/models/entities/trade/trade_handling_fee_config.dart';
import 'package:gp_stock_app/core/models/entities/user/user.dart';
import 'package:gp_stock_app/core/models/entities/wallet/alipay_account_entity.dart';
import 'package:gp_stock_app/core/models/entities/wallet/bank_entity_list.dart';
import 'package:gp_stock_app/core/models/entities/wallet/funds_record_list.dart';
import 'package:gp_stock_app/core/models/entities/wallet/third_party_channel_entity_list_entity.dart';
import 'package:gp_stock_app/core/models/entities/wallet/third_party_success_response_entity.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_channel.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_bank_entity_list.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_wallet_entity_list.dart';
import 'package:gp_stock_app/core/models/entities/wallet/withdraw_channel_entity.dart';
import 'package:gp_stock_app/core/models/entities/wallet/withdraw_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_coupon/interest_coupon_model.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_records/interest_records_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown_v2.dart';

JsonConvert jsonConvert = JsonConvert();
typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(Object error, StackTrace stackTrace);

extension MapSafeExt<K, V> on Map<K, V> {
  T? getOrNull<T>(K? key) {
    if (!containsKey(key) || key == null) {
      return null;
    } else {
      return this[key] as T?;
    }
  }
}

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
         onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value.map((dynamic e) => _asT<T>(e,enumConvert: enumConvert)).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
         onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) => _asT<T>(e,enumConvert: enumConvert)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
         onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value, {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        var covertFunc = convertFuncMap[type]!;
        if(covertFunc is Map<String, dynamic>) {
          return covertFunc(value as Map<String, dynamic>) as T;
        }else{
          return covertFunc(Map<String, dynamic>.from(value)) as T;
        }
      } else {
        throw UnimplementedError('$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if(<AccountInfo>[] is M){
      return data.map<AccountInfo>((Map<String, dynamic> e) => AccountInfo.fromJson(e)).toList() as M;
    }
    if(<PositionEntity>[] is M){
      return data.map<PositionEntity>((Map<String, dynamic> e) => PositionEntity.fromJson(e)).toList() as M;
    }
    if(<SignInDayListEntity>[] is M){
      return data.map<SignInDayListEntity>((Map<String, dynamic> e) => SignInDayListEntity.fromJson(e)).toList() as M;
    }
    if(<SignInDayEntity>[] is M){
      return data.map<SignInDayEntity>((Map<String, dynamic> e) => SignInDayEntity.fromJson(e)).toList() as M;
    }
    if(<ChatConfigEntity>[] is M){
      return data.map<ChatConfigEntity>((Map<String, dynamic> e) => ChatConfigEntity.fromJson(e)).toList() as M;
    }
    if(<ChatServiceAccountConfig>[] is M){
      return data.map<ChatServiceAccountConfig>((Map<String, dynamic> e) => ChatServiceAccountConfig.fromJson(e)).toList() as M;
    }
    if(<AppInfoListEntity>[] is M){
      return data.map<AppInfoListEntity>((Map<String, dynamic> e) => AppInfoListEntity.fromJson(e)).toList() as M;
    }
    if(<AppInfoEntity>[] is M){
      return data.map<AppInfoEntity>((Map<String, dynamic> e) => AppInfoEntity.fromJson(e)).toList() as M;
    }
    if(<ExchangeRateEntity>[] is M){
      return data.map<ExchangeRateEntity>((Map<String, dynamic> e) => ExchangeRateEntity.fromJson(e)).toList() as M;
    }
    if(<ExchangeRate>[] is M){
      return data.map<ExchangeRate>((Map<String, dynamic> e) => ExchangeRate.fromJson(e)).toList() as M;
    }
    if(<FileUploadEntity>[] is M){
      return data.map<FileUploadEntity>((Map<String, dynamic> e) => FileUploadEntity.fromJson(e)).toList() as M;
    }
    if(<SysConfigEntity>[] is M){
      return data.map<SysConfigEntity>((Map<String, dynamic> e) => SysConfigEntity.fromJson(e)).toList() as M;
    }
    if(<SysSettingsEntity>[] is M){
      return data.map<SysSettingsEntity>((Map<String, dynamic> e) => SysSettingsEntity.fromJson(e)).toList() as M;
    }
    if(<MultiLanguageTextEntity>[] is M){
      return data.map<MultiLanguageTextEntity>((Map<String, dynamic> e) => MultiLanguageTextEntity.fromJson(e)).toList() as M;
    }
    if(<ApplyNormalContractConfigEntity>[] is M){
      return data.map<ApplyNormalContractConfigEntity>((Map<String, dynamic> e) => ApplyNormalContractConfigEntity.fromJson(e)).toList() as M;
    }
    if(<ApplyNormalContractConfigAmountList>[] is M){
      return data.map<ApplyNormalContractConfigAmountList>((Map<String, dynamic> e) => ApplyNormalContractConfigAmountList.fromJson(e)).toList() as M;
    }
    if(<ApplyNormalContractConfigContractConfigMap>[] is M){
      return data.map<ApplyNormalContractConfigContractConfigMap>((Map<String, dynamic> e) => ApplyNormalContractConfigContractConfigMap.fromJson(e)).toList() as M;
    }
    if(<ApplyNormalContractConfigContractConfigMapConfigList>[] is M){
      return data.map<ApplyNormalContractConfigContractConfigMapConfigList>((Map<String, dynamic> e) => ApplyNormalContractConfigContractConfigMapConfigList.fromJson(e)).toList() as M;
    }
    if(<ApplyNormalContractConfigRuleMap>[] is M){
      return data.map<ApplyNormalContractConfigRuleMap>((Map<String, dynamic> e) => ApplyNormalContractConfigRuleMap.fromJson(e)).toList() as M;
    }
    if(<ApplyTrialContractConfigEntity>[] is M){
      return data.map<ApplyTrialContractConfigEntity>((Map<String, dynamic> e) => ApplyTrialContractConfigEntity.fromJson(e)).toList() as M;
    }
    if(<ApplyTrialContractConfigActivityRiskMap>[] is M){
      return data.map<ApplyTrialContractConfigActivityRiskMap>((Map<String, dynamic> e) => ApplyTrialContractConfigActivityRiskMap.fromJson(e)).toList() as M;
    }
    if(<ContractListEntity>[] is M){
      return data.map<ContractListEntity>((Map<String, dynamic> e) => ContractListEntity.fromJson(e)).toList() as M;
    }
    if(<Contract>[] is M){
      return data.map<Contract>((Map<String, dynamic> e) => Contract.fromJson(e)).toList() as M;
    }
    if(<ContractMarginEntity>[] is M){
      return data.map<ContractMarginEntity>((Map<String, dynamic> e) => ContractMarginEntity.fromJson(e)).toList() as M;
    }
    if(<ContractMarginAmountList>[] is M){
      return data.map<ContractMarginAmountList>((Map<String, dynamic> e) => ContractMarginAmountList.fromJson(e)).toList() as M;
    }
    if(<ProfitWithdrawalConfigEntity>[] is M){
      return data.map<ProfitWithdrawalConfigEntity>((Map<String, dynamic> e) => ProfitWithdrawalConfigEntity.fromJson(e)).toList() as M;
    }
    if(<ContractApplyAmountEntity>[] is M){
      return data.map<ContractApplyAmountEntity>((Map<String, dynamic> e) => ContractApplyAmountEntity.fromJson(e)).toList() as M;
    }
    if(<ContractAppleyRecordList>[] is M){
      return data.map<ContractAppleyRecordList>((Map<String, dynamic> e) => ContractAppleyRecordList.fromJson(e)).toList() as M;
    }
    if(<ContractAppleyRecord>[] is M){
      return data.map<ContractAppleyRecord>((Map<String, dynamic> e) => ContractAppleyRecord.fromJson(e)).toList() as M;
    }
    if(<ContractAvailableType>[] is M){
      return data.map<ContractAvailableType>((Map<String, dynamic> e) => ContractAvailableType.fromJson(e)).toList() as M;
    }
    if(<FundPayWayListEntity>[] is M){
      return data.map<FundPayWayListEntity>((Map<String, dynamic> e) => FundPayWayListEntity.fromJson(e)).toList() as M;
    }
    if(<FundPayWay>[] is M){
      return data.map<FundPayWay>((Map<String, dynamic> e) => FundPayWay.fromJson(e)).toList() as M;
    }
    if(<AIAnswer>[] is M){
      return data.map<AIAnswer>((Map<String, dynamic> e) => AIAnswer.fromJson(e)).toList() as M;
    }
    if(<BannerEntityList>[] is M){
      return data.map<BannerEntityList>((Map<String, dynamic> e) => BannerEntityList.fromJson(e)).toList() as M;
    }
    if(<BannerEntity>[] is M){
      return data.map<BannerEntity>((Map<String, dynamic> e) => BannerEntity.fromJson(e)).toList() as M;
    }
    if(<CompanyNewsEntity>[] is M){
      return data.map<CompanyNewsEntity>((Map<String, dynamic> e) => CompanyNewsEntity.fromJson(e)).toList() as M;
    }
    if(<NewsRecordsList>[] is M){
      return data.map<NewsRecordsList>((Map<String, dynamic> e) => NewsRecordsList.fromJson(e)).toList() as M;
    }
    if(<NewsRecords>[] is M){
      return data.map<NewsRecords>((Map<String, dynamic> e) => NewsRecords.fromJson(e)).toList() as M;
    }
    if(<NotificationEntityList>[] is M){
      return data.map<NotificationEntityList>((Map<String, dynamic> e) => NotificationEntityList.fromJson(e)).toList() as M;
    }
    if(<NotificationEntity>[] is M){
      return data.map<NotificationEntity>((Map<String, dynamic> e) => NotificationEntity.fromJson(e)).toList() as M;
    }
    if(<InviteDetailEntity>[] is M){
      return data.map<InviteDetailEntity>((Map<String, dynamic> e) => InviteDetailEntity.fromJson(e)).toList() as M;
    }
    if(<VipLevelTableEntity>[] is M){
      return data.map<VipLevelTableEntity>((Map<String, dynamic> e) => VipLevelTableEntity.fromJson(e)).toList() as M;
    }
    if(<InviteInfoEntity>[] is M){
      return data.map<InviteInfoEntity>((Map<String, dynamic> e) => InviteInfoEntity.fromJson(e)).toList() as M;
    }
    if(<RebatePaginatedResponseEntity>[] is M){
      return data.map<RebatePaginatedResponseEntity>((Map<String, dynamic> e) => RebatePaginatedResponseEntity.fromJson(e)).toList() as M;
    }
    if(<RebateDetailEntity>[] is M){
      return data.map<RebateDetailEntity>((Map<String, dynamic> e) => RebateDetailEntity.fromJson(e)).toList() as M;
    }
    if(<SubordinatePaginatedResponseEntity>[] is M){
      return data.map<SubordinatePaginatedResponseEntity>((Map<String, dynamic> e) => SubordinatePaginatedResponseEntity.fromJson(e)).toList() as M;
    }
    if(<SubordinateEntity>[] is M){
      return data.map<SubordinateEntity>((Map<String, dynamic> e) => SubordinateEntity.fromJson(e)).toList() as M;
    }
    if(<SubordinateDetailEntity>[] is M){
      return data.map<SubordinateDetailEntity>((Map<String, dynamic> e) => SubordinateDetailEntity.fromJson(e)).toList() as M;
    }
    if(<DistAndFlow>[] is M){
      return data.map<DistAndFlow>((Map<String, dynamic> e) => DistAndFlow.fromJson(e)).toList() as M;
    }
    if(<Flow>[] is M){
      return data.map<Flow>((Map<String, dynamic> e) => Flow.fromJson(e)).toList() as M;
    }
    if(<Dist>[] is M){
      return data.map<Dist>((Map<String, dynamic> e) => Dist.fromJson(e)).toList() as M;
    }
    if(<DistData>[] is M){
      return data.map<DistData>((Map<String, dynamic> e) => DistData.fromJson(e)).toList() as M;
    }
    if(<GainDistribution>[] is M){
      return data.map<GainDistribution>((Map<String, dynamic> e) => GainDistribution.fromJson(e)).toList() as M;
    }
    if(<IndexStockConfigList>[] is M){
      return data.map<IndexStockConfigList>((Map<String, dynamic> e) => IndexStockConfigList.fromJson(e)).toList() as M;
    }
    if(<IndexStockConfig>[] is M){
      return data.map<IndexStockConfig>((Map<String, dynamic> e) => IndexStockConfig.fromJson(e)).toList() as M;
    }
    if(<MarketPlateList>[] is M){
      return data.map<MarketPlateList>((Map<String, dynamic> e) => MarketPlateList.fromJson(e)).toList() as M;
    }
    if(<SecurityInfo>[] is M){
      return data.map<SecurityInfo>((Map<String, dynamic> e) => SecurityInfo.fromJson(e)).toList() as M;
    }
    if(<MarketSearchResultList>[] is M){
      return data.map<MarketSearchResultList>((Map<String, dynamic> e) => MarketSearchResultList.fromJson(e)).toList() as M;
    }
    if(<MarketSearchResult>[] is M){
      return data.map<MarketSearchResult>((Map<String, dynamic> e) => MarketSearchResult.fromJson(e)).toList() as M;
    }
    if(<MarketStatusInfoList>[] is M){
      return data.map<MarketStatusInfoList>((Map<String, dynamic> e) => MarketStatusInfoList.fromJson(e)).toList() as M;
    }
    if(<MarketStatusInfo>[] is M){
      return data.map<MarketStatusInfo>((Map<String, dynamic> e) => MarketStatusInfo.fromJson(e)).toList() as M;
    }
    if(<MarketStatus>[] is M){
      return data.map<MarketStatus>((Map<String, dynamic> e) => MarketStatus.fromJson(e)).toList() as M;
    }
    if(<PlateInfoList>[] is M){
      return data.map<PlateInfoList>((Map<String, dynamic> e) => PlateInfoList.fromJson(e)).toList() as M;
    }
    if(<PlateInfo>[] is M){
      return data.map<PlateInfo>((Map<String, dynamic> e) => PlateInfo.fromJson(e)).toList() as M;
    }
    if(<PlateInfoLeadUp>[] is M){
      return data.map<PlateInfoLeadUp>((Map<String, dynamic> e) => PlateInfoLeadUp.fromJson(e)).toList() as M;
    }
    if(<QuotationBroker>[] is M){
      return data.map<QuotationBroker>((Map<String, dynamic> e) => QuotationBroker.fromJson(e)).toList() as M;
    }
    if(<BrokerLevel>[] is M){
      return data.map<BrokerLevel>((Map<String, dynamic> e) => BrokerLevel.fromJson(e)).toList() as M;
    }
    if(<BrokerInfo>[] is M){
      return data.map<BrokerInfo>((Map<String, dynamic> e) => BrokerInfo.fromJson(e)).toList() as M;
    }
    if(<StockCompanyInfo>[] is M){
      return data.map<StockCompanyInfo>((Map<String, dynamic> e) => StockCompanyInfo.fromJson(e)).toList() as M;
    }
    if(<StockKlineEntity>[] is M){
      return data.map<StockKlineEntity>((Map<String, dynamic> e) => StockKlineEntity.fromJson(e)).toList() as M;
    }
    if(<StockKlineDetail>[] is M){
      return data.map<StockKlineDetail>((Map<String, dynamic> e) => StockKlineDetail.fromJson(e)).toList() as M;
    }
    if(<StockKlineItem>[] is M){
      return data.map<StockKlineItem>((Map<String, dynamic> e) => StockKlineItem.fromJson(e)).toList() as M;
    }
    if(<StockNewsEntity>[] is M){
      return data.map<StockNewsEntity>((Map<String, dynamic> e) => StockNewsEntity.fromJson(e)).toList() as M;
    }
    if(<StockNews>[] is M){
      return data.map<StockNews>((Map<String, dynamic> e) => StockNews.fromJson(e)).toList() as M;
    }
    if(<WarnResponseEntity>[] is M){
      return data.map<WarnResponseEntity>((Map<String, dynamic> e) => WarnResponseEntity.fromJson(e)).toList() as M;
    }
    if(<WarnResponse>[] is M){
      return data.map<WarnResponse>((Map<String, dynamic> e) => WarnResponse.fromJson(e)).toList() as M;
    }
    if(<AuthNInfoEntity>[] is M){
      return data.map<AuthNInfoEntity>((Map<String, dynamic> e) => AuthNInfoEntity.fromJson(e)).toList() as M;
    }
    if(<NextUserLevelEntity>[] is M){
      return data.map<NextUserLevelEntity>((Map<String, dynamic> e) => NextUserLevelEntity.fromJson(e)).toList() as M;
    }
    if(<VipConfigEntity>[] is M){
      return data.map<VipConfigEntity>((Map<String, dynamic> e) => VipConfigEntity.fromJson(e)).toList() as M;
    }
    if(<NotificationData>[] is M){
      return data.map<NotificationData>((Map<String, dynamic> e) => NotificationData.fromJson(e)).toList() as M;
    }
    if(<NotificationRecordEntity>[] is M){
      return data.map<NotificationRecordEntity>((Map<String, dynamic> e) => NotificationRecordEntity.fromJson(e)).toList() as M;
    }
    if(<RankConfig>[] is M){
      return data.map<RankConfig>((Map<String, dynamic> e) => RankConfig.fromJson(e)).toList() as M;
    }
    if(<RankingEntityList>[] is M){
      return data.map<RankingEntityList>((Map<String, dynamic> e) => RankingEntityList.fromJson(e)).toList() as M;
    }
    if(<RankingEntity>[] is M){
      return data.map<RankingEntity>((Map<String, dynamic> e) => RankingEntity.fromJson(e)).toList() as M;
    }
    if(<AppVersionEntityList>[] is M){
      return data.map<AppVersionEntityList>((Map<String, dynamic> e) => AppVersionEntityList.fromJson(e)).toList() as M;
    }
    if(<AppVersionEntity>[] is M){
      return data.map<AppVersionEntity>((Map<String, dynamic> e) => AppVersionEntity.fromJson(e)).toList() as M;
    }
    if(<HelpCategoryEntityList>[] is M){
      return data.map<HelpCategoryEntityList>((Map<String, dynamic> e) => HelpCategoryEntityList.fromJson(e)).toList() as M;
    }
    if(<HelpCategoryEntity>[] is M){
      return data.map<HelpCategoryEntity>((Map<String, dynamic> e) => HelpCategoryEntity.fromJson(e)).toList() as M;
    }
    if(<HelpQuestionList>[] is M){
      return data.map<HelpQuestionList>((Map<String, dynamic> e) => HelpQuestionList.fromJson(e)).toList() as M;
    }
    if(<HelpQuestion>[] is M){
      return data.map<HelpQuestion>((Map<String, dynamic> e) => HelpQuestion.fromJson(e)).toList() as M;
    }
    if(<RewardEntity>[] is M){
      return data.map<RewardEntity>((Map<String, dynamic> e) => RewardEntity.fromJson(e)).toList() as M;
    }
    if(<TaskCenterResponseEntity>[] is M){
      return data.map<TaskCenterResponseEntity>((Map<String, dynamic> e) => TaskCenterResponseEntity.fromJson(e)).toList() as M;
    }
    if(<TaskEntity>[] is M){
      return data.map<TaskEntity>((Map<String, dynamic> e) => TaskEntity.fromJson(e)).toList() as M;
    }
    if(<DepthQuote>[] is M){
      return data.map<DepthQuote>((Map<String, dynamic> e) => DepthQuote.fromJson(e)).toList() as M;
    }
    if(<DepthQuoteBidAsk>[] is M){
      return data.map<DepthQuoteBidAsk>((Map<String, dynamic> e) => DepthQuoteBidAsk.fromJson(e)).toList() as M;
    }
    if(<IndexStoreOrderList>[] is M){
      return data.map<IndexStoreOrderList>((Map<String, dynamic> e) => IndexStoreOrderList.fromJson(e)).toList() as M;
    }
    if(<IndexStoreOrder>[] is M){
      return data.map<IndexStoreOrder>((Map<String, dynamic> e) => IndexStoreOrder.fromJson(e)).toList() as M;
    }
    if(<OrderDetail>[] is M){
      return data.map<OrderDetail>((Map<String, dynamic> e) => OrderDetail.fromJson(e)).toList() as M;
    }
    if(<OrderDetailChargeDetailList>[] is M){
      return data.map<OrderDetailChargeDetailList>((Map<String, dynamic> e) => OrderDetailChargeDetailList.fromJson(e)).toList() as M;
    }
    if(<StockInfoV2>[] is M){
      return data.map<StockInfoV2>((Map<String, dynamic> e) => StockInfoV2.fromJson(e)).toList() as M;
    }
    if(<TickResponse>[] is M){
      return data.map<TickResponse>((Map<String, dynamic> e) => TickResponse.fromJson(e)).toList() as M;
    }
    if(<TickRecord>[] is M){
      return data.map<TickRecord>((Map<String, dynamic> e) => TickRecord.fromJson(e)).toList() as M;
    }
    if(<TradeHandlingFeeConfigList>[] is M){
      return data.map<TradeHandlingFeeConfigList>((Map<String, dynamic> e) => TradeHandlingFeeConfigList.fromJson(e)).toList() as M;
    }
    if(<TradeHandlingFeeConfig>[] is M){
      return data.map<TradeHandlingFeeConfig>((Map<String, dynamic> e) => TradeHandlingFeeConfig.fromJson(e)).toList() as M;
    }
    if(<UserModel>[] is M){
      return data.map<UserModel>((Map<String, dynamic> e) => UserModel.fromJson(e)).toList() as M;
    }
    if(<AlipayAccountEntityList>[] is M){
      return data.map<AlipayAccountEntityList>((Map<String, dynamic> e) => AlipayAccountEntityList.fromJson(e)).toList() as M;
    }
    if(<AlipayAccountEntity>[] is M){
      return data.map<AlipayAccountEntity>((Map<String, dynamic> e) => AlipayAccountEntity.fromJson(e)).toList() as M;
    }
    if(<AlipayAccountEntityCustomizeGiveRate>[] is M){
      return data.map<AlipayAccountEntityCustomizeGiveRate>((Map<String, dynamic> e) => AlipayAccountEntityCustomizeGiveRate.fromJson(e)).toList() as M;
    }
    if(<BankEntityList>[] is M){
      return data.map<BankEntityList>((Map<String, dynamic> e) => BankEntityList.fromJson(e)).toList() as M;
    }
    if(<BankEntity>[] is M){
      return data.map<BankEntity>((Map<String, dynamic> e) => BankEntity.fromJson(e)).toList() as M;
    }
    if(<FundsRecordList>[] is M){
      return data.map<FundsRecordList>((Map<String, dynamic> e) => FundsRecordList.fromJson(e)).toList() as M;
    }
    if(<FundsRecord>[] is M){
      return data.map<FundsRecord>((Map<String, dynamic> e) => FundsRecord.fromJson(e)).toList() as M;
    }
    if(<ThirdPartyChannelListEntity>[] is M){
      return data.map<ThirdPartyChannelListEntity>((Map<String, dynamic> e) => ThirdPartyChannelListEntity.fromJson(e)).toList() as M;
    }
    if(<ThirdPartyChannelEntity>[] is M){
      return data.map<ThirdPartyChannelEntity>((Map<String, dynamic> e) => ThirdPartyChannelEntity.fromJson(e)).toList() as M;
    }
    if(<ThirdPartyChannelPayType>[] is M){
      return data.map<ThirdPartyChannelPayType>((Map<String, dynamic> e) => ThirdPartyChannelPayType.fromJson(e)).toList() as M;
    }
    if(<ThirdPartySuccessEntity>[] is M){
      return data.map<ThirdPartySuccessEntity>((Map<String, dynamic> e) => ThirdPartySuccessEntity.fromJson(e)).toList() as M;
    }
    if(<USDTDepositChannelListEntity>[] is M){
      return data.map<USDTDepositChannelListEntity>((Map<String, dynamic> e) => USDTDepositChannelListEntity.fromJson(e)).toList() as M;
    }
    if(<USDTDepositChannel>[] is M){
      return data.map<USDTDepositChannel>((Map<String, dynamic> e) => USDTDepositChannel.fromJson(e)).toList() as M;
    }
    if(<UsdtRechargeOrder>[] is M){
      return data.map<UsdtRechargeOrder>((Map<String, dynamic> e) => UsdtRechargeOrder.fromJson(e)).toList() as M;
    }
    if(<USDTWalletList>[] is M){
      return data.map<USDTWalletList>((Map<String, dynamic> e) => USDTWalletList.fromJson(e)).toList() as M;
    }
    if(<USDTWallet>[] is M){
      return data.map<USDTWallet>((Map<String, dynamic> e) => USDTWallet.fromJson(e)).toList() as M;
    }
    if(<USDTNetworkTypeList>[] is M){
      return data.map<USDTNetworkTypeList>((Map<String, dynamic> e) => USDTNetworkTypeList.fromJson(e)).toList() as M;
    }
    if(<USDTNetworkType>[] is M){
      return data.map<USDTNetworkType>((Map<String, dynamic> e) => USDTNetworkType.fromJson(e)).toList() as M;
    }
    if(<UserBankEntityList>[] is M){
      return data.map<UserBankEntityList>((Map<String, dynamic> e) => UserBankEntityList.fromJson(e)).toList() as M;
    }
    if(<UserBankEntity>[] is M){
      return data.map<UserBankEntity>((Map<String, dynamic> e) => UserBankEntity.fromJson(e)).toList() as M;
    }
    if(<UserWalletEntityList>[] is M){
      return data.map<UserWalletEntityList>((Map<String, dynamic> e) => UserWalletEntityList.fromJson(e)).toList() as M;
    }
    if(<UserWalletEntity>[] is M){
      return data.map<UserWalletEntity>((Map<String, dynamic> e) => UserWalletEntity.fromJson(e)).toList() as M;
    }
    if(<WithdrawChannelListEntity>[] is M){
      return data.map<WithdrawChannelListEntity>((Map<String, dynamic> e) => WithdrawChannelListEntity.fromJson(e)).toList() as M;
    }
    if(<WithdrawChannel>[] is M){
      return data.map<WithdrawChannel>((Map<String, dynamic> e) => WithdrawChannel.fromJson(e)).toList() as M;
    }
    if(<WithdrawConfigEntity>[] is M){
      return data.map<WithdrawConfigEntity>((Map<String, dynamic> e) => WithdrawConfigEntity.fromJson(e)).toList() as M;
    }
    if(<WatchlistListEntity>[] is M){
      return data.map<WatchlistListEntity>((Map<String, dynamic> e) => WatchlistListEntity.fromJson(e)).toList() as M;
    }
    if(<WatchlistItemEntity>[] is M){
      return data.map<WatchlistItemEntity>((Map<String, dynamic> e) => WatchlistItemEntity.fromJson(e)).toList() as M;
    }
    if(<InterestCouponModel>[] is M){
      return data.map<InterestCouponModel>((Map<String, dynamic> e) => InterestCouponModel.fromJson(e)).toList() as M;
    }
    if(<InterestCouponListModel>[] is M){
      return data.map<InterestCouponListModel>((Map<String, dynamic> e) => InterestCouponListModel.fromJson(e)).toList() as M;
    }
    if(<InterestRecordsModel>[] is M){
      return data.map<InterestRecordsModel>((Map<String, dynamic> e) => InterestRecordsModel.fromJson(e)).toList() as M;
    }
    if(<InterestRecordModel>[] is M){
      return data.map<InterestRecordModel>((Map<String, dynamic> e) => InterestRecordModel.fromJson(e)).toList() as M;
    }
    if(<FTradeAcctOrderModel>[] is M){
      return data.map<FTradeAcctOrderModel>((Map<String, dynamic> e) => FTradeAcctOrderModel.fromJson(e)).toList() as M;
    }
    if(<FTradeAcctOrderRecordsList>[] is M){
      return data.map<FTradeAcctOrderRecordsList>((Map<String, dynamic> e) => FTradeAcctOrderRecordsList.fromJson(e)).toList() as M;
    }
    if(<FTradeAcctOrderRecords>[] is M){
      return data.map<FTradeAcctOrderRecords>((Map<String, dynamic> e) => FTradeAcctOrderRecords.fromJson(e)).toList() as M;
    }
    if(<FTradeConfigModel>[] is M){
      return data.map<FTradeConfigModel>((Map<String, dynamic> e) => FTradeConfigModel.fromJson(e)).toList() as M;
    }
    if(<FTradeStateModel>[] is M){
      return data.map<FTradeStateModel>((Map<String, dynamic> e) => FTradeStateModel.fromJson(e)).toList() as M;
    }
    if(<FTradeInfoModel>[] is M){
      return data.map<FTradeInfoModel>((Map<String, dynamic> e) => FTradeInfoModel.fromJson(e)).toList() as M;
    }
    if(<FTradeListModel>[] is M){
      return data.map<FTradeListModel>((Map<String, dynamic> e) => FTradeListModel.fromJson(e)).toList() as M;
    }
    if(<FTradeListItemModel>[] is M){
      return data.map<FTradeListItemModel>((Map<String, dynamic> e) => FTradeListItemModel.fromJson(e)).toList() as M;
    }
    if(<DropDownValueV2>[] is M){
      return data.map<DropDownValueV2>((Map<String, dynamic> e) => DropDownValueV2.fromJson(e)).toList() as M;
    }
    debugPrint("$M not found");
    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (AccountInfo).toString(): AccountInfo.fromJson,
    (PositionEntity).toString(): PositionEntity.fromJson,
    (SignInDayListEntity).toString(): SignInDayListEntity.fromJson,
    (SignInDayEntity).toString(): SignInDayEntity.fromJson,
    (ChatConfigEntity).toString(): ChatConfigEntity.fromJson,
    (ChatServiceAccountConfig).toString(): ChatServiceAccountConfig.fromJson,
    (AppInfoListEntity).toString(): AppInfoListEntity.fromJson,
    (AppInfoEntity).toString(): AppInfoEntity.fromJson,
    (ExchangeRateEntity).toString(): ExchangeRateEntity.fromJson,
    (ExchangeRate).toString(): ExchangeRate.fromJson,
    (FileUploadEntity).toString(): FileUploadEntity.fromJson,
    (SysConfigEntity).toString(): SysConfigEntity.fromJson,
    (SysSettingsEntity).toString(): SysSettingsEntity.fromJson,
    (MultiLanguageTextEntity).toString(): MultiLanguageTextEntity.fromJson,
    (ApplyNormalContractConfigEntity).toString(): ApplyNormalContractConfigEntity.fromJson,
    (ApplyNormalContractConfigAmountList).toString(): ApplyNormalContractConfigAmountList.fromJson,
    (ApplyNormalContractConfigContractConfigMap).toString(): ApplyNormalContractConfigContractConfigMap.fromJson,
    (ApplyNormalContractConfigContractConfigMapConfigList).toString(): ApplyNormalContractConfigContractConfigMapConfigList.fromJson,
    (ApplyNormalContractConfigRuleMap).toString(): ApplyNormalContractConfigRuleMap.fromJson,
    (ApplyTrialContractConfigEntity).toString(): ApplyTrialContractConfigEntity.fromJson,
    (ApplyTrialContractConfigActivityRiskMap).toString(): ApplyTrialContractConfigActivityRiskMap.fromJson,
    (ContractListEntity).toString(): ContractListEntity.fromJson,
    (Contract).toString(): Contract.fromJson,
    (ContractMarginEntity).toString(): ContractMarginEntity.fromJson,
    (ContractMarginAmountList).toString(): ContractMarginAmountList.fromJson,
    (ProfitWithdrawalConfigEntity).toString(): ProfitWithdrawalConfigEntity.fromJson,
    (ContractApplyAmountEntity).toString(): ContractApplyAmountEntity.fromJson,
    (ContractAppleyRecordList).toString(): ContractAppleyRecordList.fromJson,
    (ContractAppleyRecord).toString(): ContractAppleyRecord.fromJson,
    (ContractAvailableType).toString(): ContractAvailableType.fromJson,
    (FundPayWayListEntity).toString(): FundPayWayListEntity.fromJson,
    (FundPayWay).toString(): FundPayWay.fromJson,
    (AIAnswer).toString(): AIAnswer.fromJson,
    (BannerEntityList).toString(): BannerEntityList.fromJson,
    (BannerEntity).toString(): BannerEntity.fromJson,
    (CompanyNewsEntity).toString(): CompanyNewsEntity.fromJson,
    (NewsRecordsList).toString(): NewsRecordsList.fromJson,
    (NewsRecords).toString(): NewsRecords.fromJson,
    (NotificationEntityList).toString(): NotificationEntityList.fromJson,
    (NotificationEntity).toString(): NotificationEntity.fromJson,
    (InviteDetailEntity).toString(): InviteDetailEntity.fromJson,
    (VipLevelTableEntity).toString(): VipLevelTableEntity.fromJson,
    (InviteInfoEntity).toString(): InviteInfoEntity.fromJson,
    (RebatePaginatedResponseEntity).toString(): RebatePaginatedResponseEntity.fromJson,
    (RebateDetailEntity).toString(): RebateDetailEntity.fromJson,
    (SubordinatePaginatedResponseEntity).toString(): SubordinatePaginatedResponseEntity.fromJson,
    (SubordinateEntity).toString(): SubordinateEntity.fromJson,
    (SubordinateDetailEntity).toString(): SubordinateDetailEntity.fromJson,
    (DistAndFlow).toString(): DistAndFlow.fromJson,
    (Flow).toString(): Flow.fromJson,
    (Dist).toString(): Dist.fromJson,
    (DistData).toString(): DistData.fromJson,
    (GainDistribution).toString(): GainDistribution.fromJson,
    (IndexStockConfigList).toString(): IndexStockConfigList.fromJson,
    (IndexStockConfig).toString(): IndexStockConfig.fromJson,
    (MarketPlateList).toString(): MarketPlateList.fromJson,
    (SecurityInfo).toString(): SecurityInfo.fromJson,
    (MarketSearchResultList).toString(): MarketSearchResultList.fromJson,
    (MarketSearchResult).toString(): MarketSearchResult.fromJson,
    (MarketStatusInfoList).toString(): MarketStatusInfoList.fromJson,
    (MarketStatusInfo).toString(): MarketStatusInfo.fromJson,
    (MarketStatus).toString(): MarketStatus.fromJson,
    (PlateInfoList).toString(): PlateInfoList.fromJson,
    (PlateInfo).toString(): PlateInfo.fromJson,
    (PlateInfoLeadUp).toString(): PlateInfoLeadUp.fromJson,
    (QuotationBroker).toString(): QuotationBroker.fromJson,
    (BrokerLevel).toString(): BrokerLevel.fromJson,
    (BrokerInfo).toString(): BrokerInfo.fromJson,
    (StockCompanyInfo).toString(): StockCompanyInfo.fromJson,
    (StockKlineEntity).toString(): StockKlineEntity.fromJson,
    (StockKlineDetail).toString(): StockKlineDetail.fromJson,
    (StockKlineItem).toString(): StockKlineItem.fromJson,
    (StockNewsEntity).toString(): StockNewsEntity.fromJson,
    (StockNews).toString(): StockNews.fromJson,
    (WarnResponseEntity).toString(): WarnResponseEntity.fromJson,
    (WarnResponse).toString(): WarnResponse.fromJson,
    (AuthNInfoEntity).toString(): AuthNInfoEntity.fromJson,
    (NextUserLevelEntity).toString(): NextUserLevelEntity.fromJson,
    (VipConfigEntity).toString(): VipConfigEntity.fromJson,
    (NotificationData).toString(): NotificationData.fromJson,
    (NotificationRecordEntity).toString(): NotificationRecordEntity.fromJson,
    (RankConfig).toString(): RankConfig.fromJson,
    (RankingEntityList).toString(): RankingEntityList.fromJson,
    (RankingEntity).toString(): RankingEntity.fromJson,
    (AppVersionEntityList).toString(): AppVersionEntityList.fromJson,
    (AppVersionEntity).toString(): AppVersionEntity.fromJson,
    (HelpCategoryEntityList).toString(): HelpCategoryEntityList.fromJson,
    (HelpCategoryEntity).toString(): HelpCategoryEntity.fromJson,
    (HelpQuestionList).toString(): HelpQuestionList.fromJson,
    (HelpQuestion).toString(): HelpQuestion.fromJson,
    (RewardEntity).toString(): RewardEntity.fromJson,
    (TaskCenterResponseEntity).toString(): TaskCenterResponseEntity.fromJson,
    (TaskEntity).toString(): TaskEntity.fromJson,
    (DepthQuote).toString(): DepthQuote.fromJson,
    (DepthQuoteBidAsk).toString(): DepthQuoteBidAsk.fromJson,
    (IndexStoreOrderList).toString(): IndexStoreOrderList.fromJson,
    (IndexStoreOrder).toString(): IndexStoreOrder.fromJson,
    (OrderDetail).toString(): OrderDetail.fromJson,
    (OrderDetailChargeDetailList).toString(): OrderDetailChargeDetailList.fromJson,
    (StockInfoV2).toString(): StockInfoV2.fromJson,
    (TickResponse).toString(): TickResponse.fromJson,
    (TickRecord).toString(): TickRecord.fromJson,
    (TradeHandlingFeeConfigList).toString(): TradeHandlingFeeConfigList.fromJson,
    (TradeHandlingFeeConfig).toString(): TradeHandlingFeeConfig.fromJson,
    (UserModel).toString(): UserModel.fromJson,
    (AlipayAccountEntityList).toString(): AlipayAccountEntityList.fromJson,
    (AlipayAccountEntity).toString(): AlipayAccountEntity.fromJson,
    (AlipayAccountEntityCustomizeGiveRate).toString(): AlipayAccountEntityCustomizeGiveRate.fromJson,
    (BankEntityList).toString(): BankEntityList.fromJson,
    (BankEntity).toString(): BankEntity.fromJson,
    (FundsRecordList).toString(): FundsRecordList.fromJson,
    (FundsRecord).toString(): FundsRecord.fromJson,
    (ThirdPartyChannelListEntity).toString(): ThirdPartyChannelListEntity.fromJson,
    (ThirdPartyChannelEntity).toString(): ThirdPartyChannelEntity.fromJson,
    (ThirdPartyChannelPayType).toString(): ThirdPartyChannelPayType.fromJson,
    (ThirdPartySuccessEntity).toString(): ThirdPartySuccessEntity.fromJson,
    (USDTDepositChannelListEntity).toString(): USDTDepositChannelListEntity.fromJson,
    (USDTDepositChannel).toString(): USDTDepositChannel.fromJson,
    (UsdtRechargeOrder).toString(): UsdtRechargeOrder.fromJson,
    (USDTWalletList).toString(): USDTWalletList.fromJson,
    (USDTWallet).toString(): USDTWallet.fromJson,
    (USDTNetworkTypeList).toString(): USDTNetworkTypeList.fromJson,
    (USDTNetworkType).toString(): USDTNetworkType.fromJson,
    (UserBankEntityList).toString(): UserBankEntityList.fromJson,
    (UserBankEntity).toString(): UserBankEntity.fromJson,
    (UserWalletEntityList).toString(): UserWalletEntityList.fromJson,
    (UserWalletEntity).toString(): UserWalletEntity.fromJson,
    (WithdrawChannelListEntity).toString(): WithdrawChannelListEntity.fromJson,
    (WithdrawChannel).toString(): WithdrawChannel.fromJson,
    (WithdrawConfigEntity).toString(): WithdrawConfigEntity.fromJson,
    (WatchlistListEntity).toString(): WatchlistListEntity.fromJson,
    (WatchlistItemEntity).toString(): WatchlistItemEntity.fromJson,
    (InterestCouponModel).toString(): InterestCouponModel.fromJson,
    (InterestCouponListModel).toString(): InterestCouponListModel.fromJson,
    (InterestRecordsModel).toString(): InterestRecordsModel.fromJson,
    (InterestRecordModel).toString(): InterestRecordModel.fromJson,
    (FTradeAcctOrderModel).toString(): FTradeAcctOrderModel.fromJson,
    (FTradeAcctOrderRecordsList).toString(): FTradeAcctOrderRecordsList.fromJson,
    (FTradeAcctOrderRecords).toString(): FTradeAcctOrderRecords.fromJson,
    (FTradeConfigModel).toString(): FTradeConfigModel.fromJson,
    (FTradeStateModel).toString(): FTradeStateModel.fromJson,
    (FTradeInfoModel).toString(): FTradeInfoModel.fromJson,
    (FTradeListModel).toString(): FTradeListModel.fromJson,
    (FTradeListItemModel).toString(): FTradeListItemModel.fromJson,
    (DropDownValueV2).toString(): DropDownValueV2.fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}