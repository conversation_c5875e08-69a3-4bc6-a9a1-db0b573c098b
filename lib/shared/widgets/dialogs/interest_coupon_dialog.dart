import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_coupon/interest_coupon_model.dart';
import 'package:gp_stock_app/shared/logic/interest_coupon/interest_coupon_cubit.dart';
import 'package:gp_stock_app/shared/logic/interest_coupon/interest_coupon_state.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import '../../../core/utils/icon_helper.dart';
import '../../constants/assets.dart';

class InterestCouponDialog {
  /// Show interest coupon dialog
  static Future<void> show(BuildContext context) async {
    if (context.mounted) {
      await showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => BlocProvider(
          create: (context) => InterestCouponCubit(),
          child: const _InterestCouponDialogContent(),
        ),
      );
    }
  }
}

/// Dialog content widget
class _InterestCouponDialogContent extends StatefulWidget {
  const _InterestCouponDialogContent();

  @override
  State<_InterestCouponDialogContent> createState() => _InterestCouponDialogContentState();
}

class _InterestCouponDialogContentState extends State<_InterestCouponDialogContent>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);
    context.read<InterestCouponCubit>().loadUnusedCoupons();
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    context.read<InterestCouponCubit>().reset();
    super.dispose();
  }

  void _onTabChanged() {
    final cubit = context.read<InterestCouponCubit>();
    final state = cubit.state;

    if (_tabController.index == 0 && !state.unusedCouponsLoaded && !state.isLoadingUnused) {
      cubit.loadUnusedCoupons();
    } else if (_tabController.index == 1 && !state.usedAndExpiredCouponsLoaded && !state.isLoadingUsedExpired) {
      cubit.loadUsedAndExpiredCoupons();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<InterestCouponCubit, InterestCouponState>(
      builder: (context, state) {
        return Dialog(
          insetPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.gr),
          ),
          child: Container(
            constraints: BoxConstraints(
              maxHeight: 0.5.gsh,
              maxWidth: 1.gsw - (2 * 14.gw),
            ),
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(12.gr),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(context),
                _buildDivider(context),
                _buildTabBar(context),
                _buildDivider(context),
                Flexible(
                  child: _buildTabBarView(context, state),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.gw, horizontal: 16.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(width: 24.gw),
          Text(
            'interestCoupon'.tr(),
            style: context.textTheme.title.copyWith(fontWeight: FontWeight.bold),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: IconHelper.loadAsset(
              Assets.closeIcon,
              width: 20.gw,
              height: 20.gw,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      height: 0.5.gw,
      color: const Color(0xFFDEDEDE),
      margin: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    final tabKeys = [
      'interestCouponUnused',
      'interestCouponUsedExpired',
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: CommonTabBar(
            data: tabKeys.map((key) => key.tr()).toList(),
            currentIndex: _tabController.index,
            onTap: (index) => _tabController.animateTo(index),
            backgroundColor: Colors.transparent,
            height: 40.gw,
            style: CommonTabBarStyle.line,
          ),
        ),
        SizedBox(width: 50.gw),
        _buildUseButton(context),
        SizedBox(width: 14.gw),
      ],
    );
  }

  Widget _buildTabBarView(BuildContext context, InterestCouponState state) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildCouponTab(context, state.unusedCoupons, state.isLoadingUnused),
        _buildCouponTab(context, state.usedAndExpiredCoupons, state.isLoadingUsedExpired),
      ],
    );
  }

  Widget _buildCouponTab(BuildContext context, List<InterestCouponModel> coupons, bool isLoading) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    return _buildCouponList(context, coupons);
  }

  Widget _buildCouponList(BuildContext context, List<InterestCouponModel> coupons) {
    if (coupons.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(32.gw),
          child: Text(
            'noCouponsAvailable'.tr(), // 暂无利息券
            style: context.textTheme.regular.copyWith(
              color: context.textTheme.regular.color?.withValues(alpha: 0.6),
            ),
          ),
        ),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 8.gw),
      itemCount: coupons.length,
      separatorBuilder: (_, __) => SizedBox(height: 12.gw),
      itemBuilder: (context, index) {
        return _buildCouponItem(context, coupons[index]);
      },
    );
  }

  Widget _buildCouponItem(BuildContext context, InterestCouponModel coupon) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                // 利息券
                '${'interestCoupon'.tr()} ¥ ${coupon.amount.formattedMoney}',
                style: context.textTheme.title.copyWith(fontWeight: FontWeight.bold),
              ),
              SizedBox(width: 8.gw),
              Text(
                coupon.couponStatus.translationKey.tr(),
                style: context.textTheme.regular.copyWith(
                  color: _getCouponStatusColor(context, coupon),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.gw),
          Text(
            // 有效期
            '${'validityPeriod'.tr()}：${coupon.validityPeriod}',
            style: context.textTheme.tertiary,
          ),
        ],
      ),
    );
  }

  Color _getCouponStatusColor(BuildContext context, InterestCouponModel coupon) {
    switch (coupon.couponStatus) {
      case InterestCouponStatus.unused:
        return context.colorTheme.stockGreen; // Green for unused
      case InterestCouponStatus.partiallyUsed:
      case InterestCouponStatus.completelyUsed:
        return const Color(0xFF3A5BCD); // Blue for used
      case InterestCouponStatus.expired:
        return context.colorTheme.stockRed; // Red for expired
    }
  }

  Widget _buildUseButton(BuildContext context) {
    return CommonButton(
      style: CommonButtonStyle.primary,
      title: 'interestRecord'.tr(), // 利息券
      onPressed: () {
        Navigator.of(context).pop();
        getIt<NavigatorService>().push(AppRouter.routeInterestRecord);
      },
      width: 74.gw,
      height: 30.gw,
      fontSize: 14.gsp,
    );
  }
}
