import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_bank_entity_list.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import '../../domain/models/deposit_records/deposit_record.dart';

part 'deposit_state.freezed.dart';

@freezed
class DepositState with _$DepositState {
  const factory DepositState({
    @Default(DataStatus.idle) DataStatus depositStatus,
    @Default(DataStatus.idle) DataStatus depositRecordsFetchStatus, // Added for deposit records
    String? error,
    String? depositAmount,
    UserBankEntity? selectedBank,
    String? orderNumber,
    bool? isFormValid,
    List<DepositRecord>? depositRecords, // Added for deposit records
    int? depositCurrent, // Added for pagination
    int? depositTotal, // Added for pagination
  }) = _DepositState;
}
