import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/trade/depth_quote.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/quotes_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/quotes_state.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_state.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/widgets/bottom_widget/bottom_widget.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/widgets/broker_queue_section/broker_queue_section.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/widgets/company_info_section/company_info_section.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/widgets/dist_section/dist_section.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/widgets/kline_section/kline_section.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/widgets/news_section/news_section.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/widgets/stock_info_section/stock_info_section.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trade/stock_order_book/stock_order_book_section.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

/// 行情页面 / Quote Page
///
/// 显示股票行情信息、K线图、资金流向等
/// Displays stock quotes, K-line charts, capital flow, etc.
class QuotePage extends StatefulWidget {
  const QuotePage({super.key});

  @override
  State<QuotePage> createState() => _QuotePageState();
}

class _QuotePageState extends State<QuotePage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 初始化时加载所有 tab 的数据 / Load all tab data on initialization
      context.read<QuotesCubit>().refreshAllData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TradingCenterV2Cubit, TradingCenterV2State>(
      listenWhen: (previous, current) => previous.security.instrument != current.security.instrument,
      listener: (context, state) {
        final security = state.security;
        final cubit = context.read<QuotesCubit>();

        // Security 变化时，重置到第一个 tab
        cubit.selectTab(QuoteTabType.quotes);

        // 清空旧数据并更新 security / Clear old data and update security
        cubit.resetData(security);

        // 刷新所有 tab 的数据 / Refresh all tab data
        cubit.refreshAllData();
      },
      child: BlocBuilder<QuotesCubit, QuotesState>(
        builder: (context, quotesState) {
          final security = quotesState.security;
          if (security == null) return const SizedBox.shrink();

          final isIndexTrading = security.isIndexTrading;

          // 根据股票类型动态确定 tabs
          final tabs = isIndexTrading ? QuoteTabType.indexTradingTabs : QuoteTabType.normalTabs;
          final selectedTab = quotesState.selectedTab;
          final selectedIndex = tabs.indexOf(selectedTab);

          // 判断是否显示底部组件
          final isBottomWidgetVisible = (selectedTab == QuoteTabType.quotes);

          // 生成 tab 标题
          final tabData = tabs.map((tab) => tab.name.tr()).toList();

          return Column(
            children: [
              // Tab Bar
              Container(
                height: 34,
                decoration: BoxDecoration(color: context.theme.scaffoldBackgroundColor),
                child: CommonTabBar.withAutoKey(
                  tabData,
                  currentIndex: selectedIndex,
                  onTap: (index) {
                    final newTab = tabs[index];
                    final cubit = context.read<QuotesCubit>();

                    // 切换 tab
                    cubit.selectTab(newTab);

                    // 除了行情tab（WebSocket实时更新），其他tab点击时刷新数据
                    if (newTab != QuoteTabType.quotes) {
                      cubit.refreshCurrentTabData();
                    }
                  },
                  style: CommonTabBarStyle.line,
                  isScrollable: false,
                ),
              ),
              // Stock Info Header
              const StockInfoSection(),
              // Tab Content
              Expanded(
                child: IndexedStack(
                  index: selectedIndex,
                  children: _buildTabContent(tabs, isIndexTrading, security),
                ),
              ),
              if (isBottomWidgetVisible)
                QuoteBottomWidget(
                  instrument: security,
                  isIndexTrading: isIndexTrading,
                ),
            ],
          );
        },
      ),
    );
  }



  /// 动态生成Tab内容 / Build tab content dynamically
  List<Widget> _buildTabContent(List<QuoteTabType> tabs, bool isIndexTrading, Security security) {
    return tabs.map((tab) {
      switch (tab) {
        case QuoteTabType.quotes:
          return _buildQuotesTab(isIndexTrading, security);
        case QuoteTabType.distribution:
          return const DistSection();
        case QuoteTabType.news:
          return const NewsSection();
        case QuoteTabType.companyInfo:
          return const CompanyInfoSection();
      }
    }).toList();
  }

  /// 构建行情标签内容 / Build quotes tab content
  Widget _buildQuotesTab(bool isIndexTrading, Security security) {
    // Determine market category from security
    final marketCategory = MarketCategory.fromSecurity(security.market, security.securityType);

    return SingleChildScrollView(
      child: Column(
        spacing: 14,
        children: [

          StockInfoBody(),
          // K-line Chart
          const KlineSection(),
          // Order Book (only for normal stocks)
          if (!isIndexTrading)
            BlocSelector<TradingCenterV2Cubit, TradingCenterV2State,
                ({DepthQuote? depthQuote, DataStatus status, StockOrderLevelType levelType, String? currency})>(
              selector: (state) => (
                depthQuote: state.depthQuote,
                status: state.depthQuoteStatus,
                levelType: state.stockOrderLevelType,
                currency: state.stockInfo?.currency,
              ),
              builder: (context, state) {
                final cubit = context.read<TradingCenterV2Cubit>();
                final enableLevelSelector = marketCategory.code < 3;

                return StockOrderBookSection(
                  marketCategory: marketCategory,
                  currency: state.currency ?? '',
                  depthQuote: state.depthQuote,
                  status: state.status,
                  levelType: state.levelType,
                  enableLevelSelector: enableLevelSelector,
                  onLevelTypeChanged: enableLevelSelector ? cubit.onChangeStockOrderLevelType : null,
                  padding: const EdgeInsets.all(20),
                );
              },
            ),
          // Broker Queue (only for normal stocks)
          if (!isIndexTrading) const BrokerQueueSection(),
        ],
      ),
    );
  }
}
