import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/deposit/deposit_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/screens/deposit_screen.dart';
import 'package:gp_stock_app/features/profile/logic/third_party_channel/third_party_channel_cubit.dart';
import 'package:gp_stock_app/features/profile/screens/alipay_deposit/alipay_deposit_cubit.dart';
import 'package:gp_stock_app/features/profile/screens/alipay_deposit/alipay_deposit_view.dart';
import 'package:gp_stock_app/features/profile/screens/third_party_channel/third_party_channel_screen.dart';
import 'package:gp_stock_app/features/profile/screens/usdt_deposit/usdt_deposit_cubit.dart';
import 'package:gp_stock_app/features/profile/screens/usdt_deposit/usdt_deposit_view.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class DepositMainScreen extends StatefulWidget {
  const DepositMainScreen({super.key, this.depositType = DepositType.bank});
  final DepositType depositType;

  @override
  State<DepositMainScreen> createState() => _DepositMainScreenState();
}

class _DepositMainScreenState extends State<DepositMainScreen> {
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _selectedTabIndex = widget.depositType.index;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tabData = [
      'bankCard'.tr(),
      'thirdPartyDeposit'.tr(),
      'usdt'.tr(),
      'alipay'.tr(),
    ];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        title: Text('deposit'.tr()),
        actions: [
          IconButton(
            icon: Icon(LucideIcons.notepad_text),
            onPressed: () {
              getIt<NavigatorService>().push(AppRouter.routePayOrder);
            },
          ),
          // IconButton(
          //   icon: Icon(LucideIcons.notepad_text_dashed),
          //   onPressed: () {
          //     getIt<NavigatorService>().push(AppRouter.routeDepositRecords);
          //   },
          // ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(50),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gw),
            color: context.theme.cardColor,
            child: CommonTabBar.withAutoKey(
              tabData,
              currentIndex: _selectedTabIndex,
              onTap: (index) => setState(() => _selectedTabIndex = index),
              style: CommonTabBarStyle.line,
              isScrollable: true,
            ),
          ),
        ),
      ),
      body: IndexedStack(
        index: _selectedTabIndex,
        children: [
          // First tab - Bank Deposit
          MultiBlocProvider(
            providers: [
              BlocProvider<DepositCubit>.value(value: context.read<DepositCubit>()),
              BlocProvider<UserBankListCubit>.value(value: context.read<UserBankListCubit>()),
              BlocProvider<BankListCubit>.value(value: context.read<BankListCubit>()),
            ],
            child: const DepositScreen(),
          ),
          // Second tab - Third Party Channel
          BlocProvider<ThirdPartyChannelCubit>(
            create: (context) => ThirdPartyChannelCubit(),
            child: ThirdPartyChannelScreen(showAppBar: false),
          ),
          BlocProvider<UsdtDepositCubit>(
            create: (context) => UsdtDepositCubit(),
            child: UsdtDepositPage(),
          ),
          BlocProvider(
            create: (context) => AlipayDepositCubit(),
            child: AlipayDepositView(),
          ),
        ],
      ),
    );
  }
}
