import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account/widgets/table_loading.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/order_list_state.dart';
import 'package:gp_stock_app/features/account_v2/spot/utils/account_cell_builder.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:scrollview_observer/scrollview_observer.dart';

class FTradeAcctEntrustList extends StatefulWidget {
  final DataStatus dataStatus;
  final FTradeAcctOrderModel? orderModel;
  final RefreshController refreshCtrl;
  final Function(List<int> visibleIds)? onVisibleRecordsChanged; // 可见记录 ID 变化回调

  const FTradeAcctEntrustList({
    super.key,
    required this.dataStatus,
    required this.orderModel,
    required this.refreshCtrl,
    this.onVisibleRecordsChanged,
  });

  @override
  State<FTradeAcctEntrustList> createState() => _FTradeAcctEntrustListState();
}

class _FTradeAcctEntrustListState extends State<FTradeAcctEntrustList> {
  final ListObserverController _observerController = ListObserverController();

  @override
  void dispose() {
    _observerController.controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final headerTitles = [
      '${'name'.tr()} |${'code'.tr()}',
      'order_price'.tr(),
      '${'completed'.tr()}|${'total'.tr()}',
      '${'direction'.tr()}|${'status'.tr()}',
      ('operate'.tr()),
    ];
    final flexValues = [3, 2, 2, 2, 2];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 12.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(10.gr),
      ),
      child: Column(
        spacing: 10,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              for (var i = 0; i < headerTitles.length; i++) ...[
                if (i > 0 && i < 5) 5.horizontalSpace,
                Expanded(
                  flex: flexValues[i],
                  child: Tooltip(
                    message: headerTitles[i],
                    child: Text(
                      headerTitles[i],
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      textAlign: i == 0 ? TextAlign.left : TextAlign.center,
                      style: context.textTheme.regular.fs12,
                    ),
                  ),
                ),
              ],
            ],
          ),
          Divider(
            color: context.theme.scaffoldBackgroundColor,
            height: 1,
          ),
          _buildContent(context),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final status = widget.dataStatus;
    final orderModel = widget.orderModel;

    if (status == DataStatus.loading) {
      return const TableLoadingState();
    }

    if (orderModel == null || orderModel.records.isEmpty) {
      return const TableEmptyWidget();
    }

    // 由于使用 shrinkWrap，取前5个作为可见项（或全部如果少于5个）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.onVisibleRecordsChanged != null && orderModel.records.isNotEmpty) {
        final visibleIds = orderModel.records
            .take(5)
            .map((e) => e.id)
            .toList();
        widget.onVisibleRecordsChanged?.call(visibleIds);
        LogD("📋 [期货委托] 可见ID: $visibleIds 总数: ${orderModel.records.length}");
      }
    });

    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.only(bottom: 0.gw),
      itemCount: orderModel.records.length,
      separatorBuilder: (_, __) => Divider(
        color: context.theme.dividerColor,
        height: 1,
      ),
      itemBuilder: (_, index) {
        final isLast = index == orderModel.records.length - 1;
        final item = orderModel.records[index];

        return AccountCellBuilder.buildCell(
          context,
          category: MarketCategory.fromSecurity(item.market, item.securityType),
          item: item,
          index: index,
          isLast: isLast,
          isIndexTrading: false,
          orderType: OrderListType.orders,
        );

      },
    );
  }
}
