import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/ranking/rank_config.dart';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

class RankApi {
  /// 获取排行榜列表
  /// rankingType	排行类型 1日 2周 3月
  /// https://h5.gpworld.cc/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E6%8E%92%E8%A1%8C%E6%A6%9C/getAppRankingListUsingGET_1
  static Future<RankingEntityList?> fetchRankList({
    required int type,
  }) async {
    final res = await Http().request<RankingEntityList>(
      ApiEndpoints.getRankList,
      method: HttpMethod.get,
      queryParameters: {
        "rankingType": type,
      },
      needShowToast: false,
      needSignIn: false,
    );
    return res.data;
  }

  /// 获取排行榜设置
  /// https://h5.gpworld.cc/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E7%AB%99%E7%82%B9%E9%85%8D%E7%BD%AE/getRankingListConfigUsingGET_1
  static Future<bool> fetchRankAvailable() async {
    final res = await Http().request<RankConfig>(
      ApiEndpoints.getRankConfig,
      method: HttpMethod.get,
      needShowToast: false,
      needSignIn: false,
    );
    return res.data?.isShow == 'true';
  }
}
