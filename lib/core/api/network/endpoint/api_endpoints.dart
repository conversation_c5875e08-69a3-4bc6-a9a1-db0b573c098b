// ignore_for_file: constant_identifier_names

class ApiEndpoints {
  //auth
  static const String login = '/auth/login';
  static const String register = '/auth/register';

  //member
  static const String logout = '/member/logout';
  static const String userSig = '/tc/userSig';
  static const String getChatServiceAccount = '/tc/getUserKefu';

  //home
  static const String bannerList = '/banner/page';

  //news
  static const String news = '/news/page';

  //market
  static const String getGainDistribution = '/market/gain/distribution';
  static const String getMarketStatusCombine = '/market/status/combine';
  static const String getMarketStatus = '/market/status';
  static const String getSectorList = '/market/plate/list'; // 获取板块数据列表（大板块概览）
  static const String getSectorStockList = '/market/plateInfo'; // 获取某板块的股票列表
  static const String getMarketPlate = '/market/getMarketPlate'; // 获取市场板块数据
  static const String getStockInfo = '/market/stockInfo';
  static const String getMarketDepth = '/market/depth/l2';
  static const String getTimeLineData = '/market/timeLine';
  static const String getKlineData = '/market/kline';
  static const String setExpireTime = '/position/setExpireTime';
  static const String indexStockOrderList = '/order/indexPage';
  static const String getTickList = '/market/tick/list';
  static const String getMarketOpenAsset = '/site/getMarketOpenAsset';
  static const String getDistFlow = '/market/distFlow'; // 资金分布

  // 期货 futures trade
  static const String getFuturesGetConfig = '/futures/getConfig'; // 获取期货配置
  static const String getFuturesMarketStatus = '/futures/market/getFutureOpenTime'; // 获取期货市场交易状态
  static const String getFuturesMarketGetMarketPlate = '/futures/market/getMarketPlate'; // 获取市场板块数据
  static const String getFuturesMarketStockInfo = '/futures/market/stockInfo'; // 获取期货详情
  static const String getFuturesMarketKLine = '/futures/market/kline'; // 获取期货K线数据
  static const String getFuturesMarketTimeLine = '/futures/market/timeLine'; // 获取期货分时数据
  static const String getFuturesMarketTickList = '/futures/market/tick/list'; // 获取期货逐笔成交数据
  static const String getFuturesMarketDepthL2 = '/futures/market/depth/l2'; // 获取期货深度报价
  static const String setFutureStopLine = '/position/setStopLine'; // 设置期货止盈止损线
  static const String addFutureMargin = '/position/addMargin'; // 期货追加保证金
  static const String getPositionDetail = '/position/get'; // 获取持仓详情
  static const String postFuturesCreateOrder = '/order/createFuturesOrder'; // 期货订单买入
  // static const String getFuturesMarketDepthL2 = '/order/cancelOrder'; // 撤销委托订单
  // static const String getFuturesMarketDepthL2 = '/futures/market/depth/l2'; // 获取期货深度报价

  // 国际期货 Global Futures
  static const String getGlobalFuturesMarketGetMarketPlate = '/futures/market/foreign/getMarketPlate'; // 获取国际市场板块数据
  static const String getGlobalFuturesMarketStockInfo = '/futures/market/foreign/stockInfo'; // 获取国际期货详情
  static const String getGlobalFuturesMarketDepthL2 = '/futures/market/foreign/depth/l2'; // 获取国际期货深度报价
  static const String getGlobalFuturesMarketStatus = '/futures/market/foreign/getFutureOpenTime'; // 获取期货市场交易状态
  static const String getGlobalFuturesMarketKLine = '/futures/market/foreign/kline'; // 获取期货K线数据
  static const String getGlobalFuturesMarketTickList = '/futures/market/foreign/tick/list'; // 获取期货逐笔成交数据
  static const String getGlobalFuturesMarketTimeLine = '/futures/market/foreign/timeLine'; // 获取期货分时数据

  //account
  static const String getAccountInfo = '/account/getAccountInfo';
  static const String getAssetRecord = '/account/asset/record/page'; // 资金记录
  static const String getContractAccountRecord = '/contract/account/record/page'; // 合约资金记录
  static const String getInterestRecord = '/interest/page'; // 利息卷分页查询
  static const String getInterestCouponList = '/asset/detail/page'; // Interest coupon list
  static const String updatePushToken = '/jpush/setDeviceToken'; // 设置推送token

  static const String getOrderList = '/order/page';
  static const String getOrderListByIds = '/order/getListByIds';
  static const String getOrderById = '/order/getById';
  static const String getPositionList = '/position/page';
  static const String getPositionListByIds = '/position/getListByIds';
  static const String getDepositChannels = '/recharge/channel/page';
  static const String getDepositChannelsList = '/recharge/channel/list';
  static const String depositRecords = '/payments/page';
  static const String payOrder = '/payOrder/page';
  static const String getBankList = '/bank/list';
  static const String getUserBankList = '/bank/userBank/list';
  static const String getUserWalletList = '/user_wallet/getUserWalletList';
  static const String supportFundChannel = '/user_wallet/getCanBindPayWayList'; // 获取可绑定的支付方式
  static const String bindUserWallet = '/user_wallet/bind';
  static const String addUserBank = '/bank/addUserBank';
  static const String deleteUserBank = '/bank/delUserBank';
  static const String getUserBankInfo = '/payments/addPay';
  static const String withdrawApi = '/withdrawal/withdraw';
  static const String withdrawUSDT = '/withdrawal/withdrawUSDT';
  static const String withdrawalRecords = '/withdrawal/list';
  static const String withdrawalConfig = '/withdrawalConfig/get';
  static const String listProxyPayChannel = '/withdrawal/listProxyPayChannel';
  static const String createOrder = '/order/createOrder';
  static const String cancelOrder = '/order/cancelOrder';
  static const String getRateInfo = '/rate/info';
  // alipay withdraw
  static const String alipayWithdrawConfig = '/withdrawalConfig/getALiPay';
  static const String listALiPay = '/recharge/channel/listALiPay';
  static const String unbindUserWallet = '/user_wallet/unbind';

  //auth
  static const String authNApply = '/member/auth/apply';
  static const String authNInfo = '/member/auth/info';

  //upload
  static const String uploadImage = '/upload/IM_IMAGE';

  //contract calculation
  static const String getOrdinaryApplyAmount = '/contract/account/getOrdinaryApplyAmount'; // 普通-期货合约支付费用查询
  static const String getBonusContractAmount = '/contract/account/getBonusContractAmount';

  //profile
  static const String updateUserInfo = '/member/updateUserInfo';
  static const String changePassword = '/member/updatePassword';
  static const String updateMobile = '/member/updateMobile';
  static const String updateEmail = '/member/updateEmail';
  static const String getUserInfo = '/member/getUserInfo';

  //contract
  static const String getContractSummaryPage = '/contract/account/getContractSummaryPage'; // 合约列表
  static const String getContractSummary = '/contract/account/getContractSummary'; // 合约详情
  static const String getContractApplyRecords = '/contract/account/page'; // 合约申请历史记录
  static const String getOrdinaryConfigInfo = '/contract/config/getOrdinaryConfigInfo'; // 普通合约的申请页面配置
  static const String getContractActivity = '/contract/config/getContractActivityConfig';
  static const String applyOrdinaryContract = '/contract/account/applyOrdinaryContract';
  static const String endContract = '/contract/account/endContract';
  static const String getContractAccountDetail = '/contract/account/getDetail'; // 合约详情
  static const String addExpandMargin = '/contract/account/addExpandMargin';
  static const String renewalContract = '/contract/account/renewalContract';
  static const String applyExperienceContract = '/contract/account/applyExperienceContract';
  static const String applyBonusContract = '/contract/account/applyBonusContract';
  static const String contractWithdraw = '/contract/account/withdraw';
  static const String getWithdrawAmount = '/contract/account/getWithdrawAmount';
  static const String getOpenContractType = '/contract/config/getOpenContractType';

  // search
  static const String search = '/symbol/search';

  //notifications
  static const String notificationCount = '/message/unreadCount';
  static const String notificationList = '/notification/list';
  static const String notificationPage = '/message/page';
  static const String notificationRead = '/message/markAsRead';
    static const String notificationReadAll = '/message/markAllAsRead';

  // activity
  static const String signIn = '/activity/signIn';
  static const String signInLog = '/activity/signIn/log';
  static const String tasks = '/activity/tasks';
  static const String collectReward = '/activity/receiveReward';
  static const String getUserLevelConfig = '/member/getUserLevelConfig';
  static const String getNextUserLevel = '/member/getNextUserLevel';

  // news
  static const String companyNews = '/market/company/news';
  static const String companyNewsInfo = '/market/company/news/info';
  static const String companyInfo = '/market/companyInfo';

  //watch list
  static const String getWatchList = '/symbol/watchlist/list';
  static const String addToWatchList = '/symbol/watchlist/add';
  static const String removeFromWatchList = '/symbol/watchlist/delete';
  static const String removeFromWatchListBySymbol = '/symbol/watchlist/deleteBySymbol';
  static const String getWatchListDetail = '/symbol/watchlist/get';
  static const String getWatchListBySymbol = '/symbol/watchlist/getBySymbol';

  // Warnings

  // Broker Queue
  static const String brokerQueue = '/market/broker/queue';
  static const String updateWarn = '/symbol/warn/update';
  static const String getWarnBySymbol = '/symbol/warn/getBySymbol';
  static const String getWarnList = '/symbol/warn/list';
  static const String addWarn = '/symbol/warn/add';
  static const String deleteWarn = '/symbol/warn/delete';

  static const String getCharge = '/charge/getByMarket'; // 获取收费项

  //sms
  static const String sendMsg = '/sms/sendMsg';
  static const String sendEmailCode = '/email/sendEmailCode';

  //stock
  static const String getIndexStockConfig = '/stock/index/getConfig';

  // Invite details
  static const String inviteDetail = '/invite/detail';

  // Custom invite link
  static const String getInviteLink = '/invite/getInviteLink';

  // Invite rebate details
  static const String inviteRebateDetail = '/invite/rebateList';

  // Rebate commission details
  static const String rebateCommission = '/invite/refundCommission';

  // Subordinate list
  static const String subordinateList = '/invite/subordinateList';

  // Exchange rate
  static const String exchangeRate = '/rate/info';

  // help
  static const String helpList = '/help/list';
  static const String helpQuestionDetail = '/help/question/detail';
  static const String helpQuestionSearch = '/help/question/search';
  static const String questionFeedbackSolved = '/question/feedback/solved';
  static const String questionFeedbackUnsolved = '/question/feedback/unsolved';

  // yidun 网易验证码模块
  static const String wangYiCaptcha = "/yidun/getEnable";

  // usdt 钱包管理
  static const String getUsdtNetworkType = '/recharge/channel/getNetworkTypeList';
  static const String getUsdtWalletAddressList = "/user/wallet/list";
  static const String addUsdtWalletAddress = "/user/wallet/add";
  static const String editUsdtWalletAddress = "/user/wallet/update";
  static const String deleteUsdtWalletAddress = "/user/wallet/delete/";

  /// System settings
  static const String systemSettings = '/site/sysSettings';
  static const String systemConfig = '/site/sysConfig';
  static const String currentDate = '/site/getCurrentDate';

  /// App info list
  static const String appInfoList = '/appInfo/list';

  /// AI chat
  static const String aiChat = '/ai/chat';

  /// Third-party payment channels
  static const String thirdPartyChannelList = '/onlinePay/channel/list';
  static const String doPay = '/onlinePay/doPay';

  /// USDT payment channels
  static const String getDepositUsdtList = '/recharge_usdt/list';
  static const String getWithdrawUsdtList = '/withdrawal_usdt/list';
  static const String applyUsdtDeposit = '/payments/applyUSDT';
  static const String confirmUsdtPaid = '/payments/update';

  /// Rank 排行榜
  static const String getRankList = '/rankingList/get';
  static const String getRankConfig = '/site/rankingListConfig';

  /// App - update
  static const String appUpdate = '/app/getLatestVersion';
}
