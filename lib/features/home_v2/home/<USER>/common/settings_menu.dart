import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home_v2/home/<USER>';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/sort_color/sort_color_cubit.dart';
import 'package:gp_stock_app/shared/logic/theme/theme_cubit.dart';
import 'package:gp_stock_app/features/notifications/logic/notifications_cubit.dart';

class SettingsMenu extends StatelessWidget {
  const SettingsMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 160.gw,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          BlocSelector<NotificationsCubit, NotificationsState, int?>(
            selector: (state) => state.notificationCount,
            builder: (context, notificationCount) {
              return _MenuItem(
                icon: Assets.notificationIcon,
                title: 'myNotifications'.tr(),
                badge: notificationCount,
                onTap: () {
                  Navigator.pop(context);
                  AuthUtils.verifyAuth(() async {
                    getIt<NavigatorService>().push(AppRouter.routeNotificationList).then((value) {
                      getIt<NotificationsCubit>().getNotificationCount();
                    });
                  });

                },
              );
            },
          ),
          _Divider(),
          _MenuItem(
            icon: Assets.languageIcon,
            title: 'language'.tr(),
            onTap: () => _showSubMenu(
              context: context,
              child: LanguageOptions(),
            ),
          ),
          _Divider(),
          _MenuItem(
            icon: Assets.themeIcon,
            title: 'theme'.tr(),
            onTap: () => _showSubMenu(
              context: context,
              child: _ThemeOptions(),
            ),
          ),
          _Divider(),
          _MenuItem(
            icon: Assets.stockIcon,
            title: 'priceColor'.tr(),
            onTap: () => _showSubMenu(
              context: context,
              child: _PriceColorOptions(),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showSubMenu({
    required BuildContext context,
    required Widget child,
  }) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final buttonPosition = button.localToGlobal(Offset.zero);

    await showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        buttonPosition.dx,
        buttonPosition.dy + 80.gw,
        buttonPosition.dx - 1.gw,
        0,
      ),
      elevation: 0,
      color: Colors.transparent,
      constraints: BoxConstraints(maxWidth: 120.gw),
      items: [
        PopupMenuItem(
          enabled: false,
          padding: EdgeInsets.zero,
          child: Container(
            width: 160.gw,
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(12.gr),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withNewOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: child,
          ),
        ),
      ],
    );
  }
}

class _ThemeOptions extends StatelessWidget {
  const _ThemeOptions();

  @override
  Widget build(BuildContext context) {
    return BlocSelector<ThemeCubit, ThemeState, ThemeMode>(
      selector: (state) => state.themeMode,
      builder: (context, themeMode) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _MenuItem(
              icon: Assets.settingsIcon,
              title: 'systemTheme'.tr(),
              onTap: () {
                context.read<ThemeCubit>().changeTheme(ThemeMode.system);
                Navigator.pop(context);
              },
              value: themeMode == ThemeMode.system ? '✓' : null,
            ),
            _Divider(),
            _MenuItem(
              icon: Assets.themeIcon,
              title: 'lightTheme'.tr(),
              onTap: () {
                context.read<ThemeCubit>().changeTheme(ThemeMode.light);
                Navigator.pop(context);
              },
              value: themeMode == ThemeMode.light ? '✓' : null,
            ),
            _Divider(),
            _MenuItem(
              icon: Assets.darkThemeIcon,
              title: 'darkTheme'.tr(),
              onTap: () {
                context.read<ThemeCubit>().changeTheme(ThemeMode.dark);
                Navigator.pop(context);
              },
              value: themeMode == ThemeMode.dark ? '✓' : null,
            ),
          ],
        );
      },
    );
  }
}

class LanguageOptions extends StatelessWidget {
  const LanguageOptions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (final locale in context.supportedLocales) ...[
          _MenuItem(
            icon: Helper().getLanguageIcon(locale),
            title: Helper().getLanguageName(locale),
            onTap: () async {
              HapticFeedback.lightImpact();
              try {
                if (context.supportedLocales.contains(locale)) {
                  await context.setLocale(locale);
                  getIt<HomeV2Cubit>().fetchBannerList();
                  if (context.mounted) {
                    context.read<MainCubit>().sendLocale(locale);
                    getIt<UserCubit>().setCurrentLocale('${locale.languageCode}-${locale.countryCode}');
                    Navigator.pop(context);
                  }
                }
              } catch (e) {
                debugPrint('Error setting locale: $e');
              }
            },
            value: context.locale == locale ? '✓' : null,
          ),
          if (locale != context.supportedLocales.last) _Divider(),
        ],
      ],
    );
  }
}

class _PriceColorOptions extends StatelessWidget {
  const _PriceColorOptions();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SortColorCubit, SortColorState>(
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _MenuItem(
              icon: Assets.greenDownIcon,
              title: 'redUpGreenDown'.tr(),
              onTap: () {
                context.read<SortColorCubit>().toggleMarketColor(
                      MarketColor.redUpGreenDown,
                    );
                Navigator.pop(context);
              },
              value: state.marketColor == MarketColor.redUpGreenDown ? '✓' : null,
            ),
            _Divider(),
            _MenuItem(
              icon: Assets.redDownIcon,
              title: 'greenUpRedDown'.tr(),
              onTap: () {
                context.read<SortColorCubit>().toggleMarketColor(
                      MarketColor.greenUpRedDown,
                    );
                Navigator.pop(context);
              },
              value: state.marketColor == MarketColor.greenUpRedDown ? '✓' : null,
            ),
          ],
        );
      },
    );
  }
}

class _MenuItem extends StatelessWidget {
  final String title, icon;
  final String? value;
  final int? badge;
  final VoidCallback onTap;

  const _MenuItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.badge,
    this.value,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 12.gw),
        child: Row(
          children: [
            SvgPicture.asset(
              icon,
              fit: BoxFit.scaleDown,
              width: 20.gw,
              height: 20.gw,
              colorFilter: ColorFilter.mode(context.theme.primaryColor, BlendMode.srcIn),
            ),
            12.horizontalSpace,
            Expanded(
              child: Text(
                title,
                style: context.textTheme.regular.w500.copyWith(
                  color: context.colorTheme.textPrimary,
                ),
              ),
            ),
            if (value != null) ...[
              Text(
                value!,
                style: context.textTheme.regular.fs12.copyWith(
                  color: context.colorTheme.textRegular,
                ),
              ),
            ],
            if (badge != null && badge! > 0) ...[
              Container(
                height: 20.gw,
                padding: EdgeInsets.symmetric(horizontal: 6.gw),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10.gr),
                ),
                alignment: Alignment.center,
                child: Text(
                  badge.toString(),
                  style: context.textTheme.regular.fs12.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _Divider extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Divider(
      height: 1,
      color: Colors.grey.withNewOpacity(0.1),
    );
  }
}
