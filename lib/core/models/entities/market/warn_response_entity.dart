import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/warn_response_entity.g.dart';
import 'dart:convert';

import 'package:gp_stock_app/shared/models/instrument/security.dart';
export 'package:gp_stock_app/generated/json/warn_response_entity.g.dart';

@JsonSerializable()
class WarnResponseEntity {
  final int current;
  final int total;
  final int pages;
  final int size;
  final bool hasNext;
  final List<WarnResponse> records;

  const WarnResponseEntity({
    this.current = 0,
    this.total = 0,
    this.pages = 0,
    this.size = 0,
    this.hasNext = false,
    this.records = const [],
  });

  factory WarnResponseEntity.fromJson(Map<String, dynamic> json) => $WarnResponseEntityFromJson(json);

  Map<String, dynamic> toJson() => $WarnResponseEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WarnResponse {
  final String createTime;
  final int id;
  final String market;
  final String name;
  final String securityType;
  final String symbol;
  final double targetDownGain;
  final double targetDownPrice;
  final double targetUpGain;
  final double targetUpPrice;
  final String updateTime;
  final int userId;

  const WarnResponse({
    this.createTime = "",
    this.id = 0,
    this.market = "",
    this.name = "",
    this.securityType = "",
    this.symbol = "",
    this.targetDownGain = 0,
    this.targetDownPrice = 0,
    this.targetUpGain = 0,
    this.targetUpPrice = 0,
    this.updateTime = "",
    this.userId = 0,
  });

  factory WarnResponse.fromJson(Map<String, dynamic> json) => $WarnResponseFromJson(json);

  Map<String, dynamic> toJson() => $WarnResponseToJson(this);

  String get instrument => '$market|$securityType|$symbol';
  Security get security => Security(instrument: instrument);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
