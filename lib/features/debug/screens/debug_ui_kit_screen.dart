import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/theme/constant/gp.dart';
import 'package:gp_stock_app/core/theme/constant/template_a.dart';
import 'package:gp_stock_app/core/theme/constant/template_b.dart';
import 'package:gp_stock_app/core/theme/constant/template_c.dart';
import 'package:gp_stock_app/core/theme/constant/template_d.dart';
import 'package:gp_stock_app/core/theme/constant/zang_golden.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/logic/theme/theme_cubit.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import '../widgets/debug_widget_showcase.dart';

class DebugUIKitScreen extends StatefulWidget {
  const DebugUIKitScreen({super.key});

  @override
  State<DebugUIKitScreen> createState() => _DebugUIKitScreenState();
}

class _DebugUIKitScreenState extends State<DebugUIKitScreen> {
  int _currentSkinIndex = 0;

  final List<AppSkinStyle> _skinStyles = [
    AppSkinStyle.kGP,
    AppSkinStyle.kTemplateA,
    AppSkinStyle.kTemplateB,
    AppSkinStyle.kTemplateC,
    AppSkinStyle.kTemplateD,
  ];

  final List<String> _skinNames = [
    'GP',
    'Template A',
    'Template B',
    'Template C',
    'Template D',
  ];

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode) {
      return Scaffold(
        body: Center(
          child: Text(
            'Debug UI Kit is only available in debug mode',
            style: context.textTheme.primary.fs16,
          ),
        ),
      );
    }

    return _DebugThemeProvider(
      skinStyle: _skinStyles[_currentSkinIndex],
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (themedContext, themeState) {
          return Scaffold(
            appBar: _buildAppBar(themedContext, themeState),
            body: Column(
              children: [
                _buildSkinSelector(themedContext),
                _buildWidgetShowcase(themedContext),
              ],
            ),
          );
        },
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context, ThemeState themeState) {
    return AppBar(
      title: Text(
        'UIKit',
        style: context.textTheme.primary.w600,
      ),
      backgroundColor: context.theme.scaffoldBackgroundColor,
      foregroundColor: context.colorTheme.textPrimary,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () {
            final newTheme = themeState.themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
            context.read<ThemeCubit>().changeTheme(newTheme);
          },
          icon: Icon(
            themeState.themeMode == ThemeMode.light ? Icons.dark_mode_outlined : Icons.light_mode_outlined,
            color: context.colorTheme.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildSkinSelector(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 8.gw),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: CommonTabBar(
          data: _skinNames,
          currentIndex: _currentSkinIndex,
          onTap: (index) {
            setState(() {
              _currentSkinIndex = index;
            });
          },
          style: CommonTabBarStyle.line,
          backgroundColor: context.theme.scaffoldBackgroundColor,
          height: 40.gw,
        ),
      ),
    );
  }

  Widget _buildWidgetShowcase(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        child: DebugWidgetShowcase(
          skinStyle: _skinStyles[_currentSkinIndex],
        ),
      ),
    );
  }
}

/// A widget that provides theme overrides for debug purposes
class _DebugThemeProvider extends StatelessWidget {
  final AppSkinStyle skinStyle;
  final Widget child;

  const _DebugThemeProvider({
    required this.skinStyle,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        // Create theme data based on the selected skin style
        final themeData = _getThemeForSkinStyle(skinStyle, themeState.themeMode);
        final colorTheme = _getColorThemeForSkinStyle(skinStyle, themeState.themeMode);

        return Theme(
          data: themeData.copyWith(
            extensions: [
              colorTheme,
              ...themeData.extensions.values.where((ext) => ext is! CustomColorTheme),
            ],
          ),
          child: child,
        );
      },
    );
  }

  ThemeData _getThemeForSkinStyle(AppSkinStyle skinStyle, ThemeMode themeMode) {
    // Create a temporary theme instance with the desired skin style
    final tempAppTheme = _TempAppTheme(skinStyle);

    return themeMode == ThemeMode.light ? tempAppTheme.getThemeLight() : tempAppTheme.getThemeDark();
  }

  CustomColorTheme _getColorThemeForSkinStyle(AppSkinStyle skinStyle, ThemeMode themeMode) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return themeMode == ThemeMode.light
            ? CustomColorThemeConstantGP.lightDefault
            : CustomColorThemeConstantGP.darkDefault;
      case AppSkinStyle.kTemplateA:
        return themeMode == ThemeMode.light
            ? CustomColorThemeConstantTemplateA.lightDefault
            : CustomColorThemeConstantTemplateA.darkDefault;
      case AppSkinStyle.kTemplateB:
        return themeMode == ThemeMode.light
            ? CustomColorThemeConstantTemplateB.lightDefault
            : CustomColorThemeConstantTemplateB.darkDefault;
      case AppSkinStyle.kTemplateC:
        return themeMode == ThemeMode.light
            ? CustomColorThemeConstantTemplateC.lightDefault
            : CustomColorThemeConstantTemplateC.darkDefault;
      case AppSkinStyle.kTemplateD:
        return themeMode == ThemeMode.light
            ? CustomColorThemeConstantTemplateD.lightDefault
            : CustomColorThemeConstantTemplateD.darkDefault;
      case AppSkinStyle.kZangGolden:
        return themeMode == ThemeMode.light
            ? CustomColorThemeConstantZangGolden.lightDefault
            : CustomColorThemeConstantZangGolden.darkDefault;
    }
  }
}

/// Temporary theme class for debug purposes
class _TempAppTheme {
  final AppSkinStyle skinStyle;

  _TempAppTheme(this.skinStyle);

  ThemeData getThemeLight() {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return ThemeConstantGP.instance.lightDefault;
      case AppSkinStyle.kTemplateA:
        return ThemeConstantTemplateA.instance.lightDefault;
      case AppSkinStyle.kTemplateB:
        return ThemeConstantTemplateB.lightDefault;
      case AppSkinStyle.kTemplateC:
        return ThemeConstantTemplateC.lightDefault;
      case AppSkinStyle.kTemplateD:
        return ThemeConstantTemplateD.lightDefault;
      case AppSkinStyle.kZangGolden:
        return ThemeConstantZangGolden.lightDefault;
    }
  }

  ThemeData getThemeDark() {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return ThemeConstantGP.instance.darkDefault;
      case AppSkinStyle.kTemplateA:
        return ThemeConstantTemplateA.instance.darkDefault;
      case AppSkinStyle.kTemplateB:
        return ThemeConstantTemplateB.darkDefault;
      case AppSkinStyle.kTemplateC:
        return ThemeConstantTemplateC.darkDefault;
      case AppSkinStyle.kTemplateD:
        return ThemeConstantTemplateD.darkDefault;
      case AppSkinStyle.kZangGolden:
        return ThemeConstantZangGolden.darkDefault;
    }
  }
}
