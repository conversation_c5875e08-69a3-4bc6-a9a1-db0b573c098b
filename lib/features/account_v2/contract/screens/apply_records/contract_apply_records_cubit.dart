import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract_appley_record_list.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'contract_apply_records_state.dart';

/// 合约申请记录列表 Cubit
class ContractApplyRecordsCubit extends Cubit<ContractApplyRecordsState> {
  ContractApplyRecordsCubit() : super(const ContractApplyRecordsState()) {
    fetchData();
  }

  Future<void> fetchData({bool isLoadMore = false}) async {
    if (isLoadMore) {
      if (state.hasReachedMax ||
          state.fetchStatus == DataStatus.loading ||
          state.paginationStatus == DataStatus.loading) {
        return;
      }
      emit(state.copyWith(paginationStatus: DataStatus.loading));
    } else {
      emit(state.copyWith(
        fetchStatus: DataStatus.loading,
        pageNum: 1,
        hasReachedMax: false,
      ));
    }

    final page = isLoadMore ? state.pageNum + 1 : state.pageNum;
    final result = await ContractApi.fetchContractApplyList(pageNum: page);

    if (isClosed) return;

    if (result == null) {
      emit(state.copyWith(
        fetchStatus: DataStatus.failed,
        paginationStatus: DataStatus.failed,
      ));
      return;
    }

    if (isLoadMore) {
      emit(state.copyWith(
        fetchStatus: DataStatus.success,
        paginationStatus: DataStatus.success,
        records: [...state.records, ...result.records],
        pageNum: page,
        hasNext: result.hasNext,
        hasReachedMax: !result.hasNext,
      ));
    } else {
      emit(state.copyWith(
        fetchStatus: DataStatus.success,
        paginationStatus: DataStatus.success,
        records: result.records,
        pageNum: page,
        hasNext: result.hasNext,
        hasReachedMax: !result.hasNext,
      ));
    }
  }

  Future<void> refresh() => fetchData(isLoadMore: false);
  Future<void> loadMore() => fetchData(isLoadMore: true);
}
