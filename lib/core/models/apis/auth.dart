import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/user/user.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AuthApi {
  /// 获取是否需要网易行为验证
  static Future<(bool isSuccess, bool isNeedCaptcha)> fetchWangYiCaptchaRequired(
      {required WangYiCaptchaType type}) async {
    // sceneType(1.Login登陆 2.SMS短信）
    final res = await Http().request(
      ApiEndpoints.wangYiCaptcha,
      method: HttpMethod.get,
      queryParameters: {"sceneType": type.code},
      needSignIn: false,
    );
    return (res.isSuccess, res.data == true);
  }

  /// 获取短信验证码
  static Future<bool> fetchMobileOTP({
    required String phoneNumber,
    required String sendType,
    String? validate,
  }) async {
    final res = await Http().request<bool>(ApiEndpoints.sendMsg, needSignIn: false, params: {
      "mobile": phoneNumber,
      "sendType": sendType,
      if (validate != null) "validate": validate,
    });
    return res.data == true;
  }

  static Future<bool> fetchEmailOTP({
    required String email,
    required String sendType,
    String? validate,
  }) async {
    final res = await Http().request<bool>(ApiEndpoints.sendEmailCode, needSignIn: false, queryParameters: {
      "email": email,
      "sendType": sendType,
      if (validate != null) "validate": validate,
    });
    return res.data == true;
  }

  /// 登录
  static Future<UserModel?> login({
    required String userName,
    required String password,
    required String verifyType,
    String? smsCode,
    String? emailCode,
    String? validate,
  }) async {
    final res = await Http().request<UserModel>(ApiEndpoints.login,
        params: {
          "username": userName,
          "mobile": userName, // 暂时兼容正式环境后端接口未更新无法登录，等后端发布可以删除
          "password": password.toBase64(),
          "verifyType": verifyType,
          if (smsCode != null) "smsCode": smsCode,
          if (emailCode != null) "emailCode": emailCode,
          if (validate != null) "validate": validate,
        },
        needSignIn: false);
    return res.data;
  }

  /// 获取用户数据
  static Future<UserModel?> getUserInfo() async {
    final res = await Http().request<UserModel>(ApiEndpoints.getUserInfo, method: HttpMethod.get);
    return res.data;
  }

  /// 注册
  static Future<bool> register({
    required String password,
    required String registerType,
    String? smsCode,
    String? emailCode,
    String? inviteCode,
    String? mobile,
    String? email,
  }) async {
    final res = await Http().request(
      ApiEndpoints.register,
      needSignIn: false,
      params: {
        if (mobile != null) "mobile": mobile,
        if (email != null) "email": email,
        "password": password,
        "registerType": registerType,
        if (inviteCode != null) "inviteCode": inviteCode,
        if (smsCode != null) "smsCode": smsCode,
        if (emailCode != null) "emailCode": emailCode,
      },
    );
    return res.isSuccess;
  }
}
