import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/activity/activity_cubit.dart';
import 'package:gp_stock_app/features/activity/widgets/hot_events.dart';

class ActivityScreen extends StatelessWidget {
  final bool showBackButton;

  const ActivityScreen({
    super.key,
    this.showBackButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<ActivityCubit>()..fetchNotifications(),
      child: Scaffold(
        appBar: showBackButton
            ? AppBar(
                backgroundColor: context.theme.scaffoldBackgroundColor,
                elevation: 0,
                leading: IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: context.colorTheme.textPrimary,
                    size: 20.gw,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
                title: Text(
                  'activity'.tr(),
                  style: TextStyle(
                    color: context.colorTheme.textPrimary,
                    fontSize: 18.gsp,
                  ),
                ),
              )
            : null,
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gw),
          child: const SingleChildScrollView(
            child: HotEvents(),
          ),
        ),
      ),
    );
  }
}
