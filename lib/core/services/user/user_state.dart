import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/models/entities/account/account.dart';
import 'package:gp_stock_app/core/models/entities/user/user.dart';

class UserState extends Equatable {
  /// 是否已登录
  final bool isLogin;

  /// 用户token
  final String? token;

  /// 用户信息
  final UserModel? userInfo;

  /// 账户信息
  final AccountInfo? accountInfo;

  final String remotePushToken;

  const UserState({
    this.isLogin = false,
    this.token,
    this.userInfo,
    this.accountInfo,
    this.remotePushToken = '',
  });


  UserState copyWith({
    bool? isLogin,
    ValueGetter<String?>? token,
    ValueGetter<UserModel?>? userInfo,
    AccountInfo? accountInfo,
    String? remotePushToken,
  }) {
    return UserState(
      isLogin: isLogin ?? this.isLogin,
      token: token != null ? token() : this.token,
      userInfo: userInfo != null ? userInfo() : this.userInfo,
      accountInfo: accountInfo ?? this.accountInfo,
      remotePushToken: remotePushToken ?? this.remotePushToken,
    );
  }

  @override
  List<Object?> get props => [
        token,
        userInfo,
        accountInfo,
        remotePushToken,
      ];
}
