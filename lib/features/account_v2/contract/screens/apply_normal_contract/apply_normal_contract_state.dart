part of 'apply_normal_contract_cubit.dart';

class ApplyNormalContractState extends Equatable {
  final ApplyNormalContractConfigEntity? configInfo;
  final DataStatus configInfoFetchStatus;
  final DataStatus applyContractStatus;
  final ApplyNormalContractConfigContractConfigMap? selectedContractConfig;
  final ApplyNormalContractConfigContractConfigMapConfigList? selectedConfigList;
  final int? selectedAmount;
  final ContractModelAmount? contractModelAmount;
  final ApplyNormalContractConfigRuleMap? selectedMarket;
  final String currency;
  final bool isAgree;
  final String? error;
  final ContractApplyAmountEntity? contractCalculation;
  final InstrumentType contractType;

  const ApplyNormalContractState({
    this.configInfo,
    this.configInfoFetchStatus = DataStatus.idle,
    this.applyContractStatus = DataStatus.idle,
    this.selectedContractConfig,
    this.selectedConfigList,
    this.selectedAmount,
    this.contractModelAmount,
    this.selectedMarket,
    this.currency = 'CNY',
    this.error,
    this.isAgree = true,
    this.contractCalculation,
    this.contractType = InstrumentType.stock,
  });

  ApplyNormalContractState copyWith({
    ApplyNormalContractConfigEntity? configInfo,
    DataStatus? configInfoFetchStatus,
    DataStatus? applyContractStatus,
    ApplyNormalContractConfigContractConfigMap? selectedContractConfig,
    ApplyNormalContractConfigContractConfigMapConfigList? selectedConfigList,
    int? Function()? selectedAmount,
    ApplyNormalContractConfigRuleMap? selectedMarket,
    ContractModelAmount? contractModelAmount,
    String? currency,
    String? error,
    bool? isAgree,
    ContractApplyAmountEntity? contractCalculation,
    InstrumentType? contractType,
  }) =>
      ApplyNormalContractState(
        configInfo: configInfo ?? this.configInfo,
        configInfoFetchStatus: configInfoFetchStatus ?? this.configInfoFetchStatus,
        applyContractStatus: applyContractStatus ?? this.applyContractStatus,
        selectedContractConfig: selectedContractConfig ?? this.selectedContractConfig,
        selectedConfigList: selectedConfigList ?? this.selectedConfigList,
        selectedAmount: selectedAmount != null ? selectedAmount() : this.selectedAmount,
        contractModelAmount: contractModelAmount ?? this.contractModelAmount,
        selectedMarket: selectedMarket ?? this.selectedMarket,
        currency: currency ?? this.currency,
        isAgree: isAgree ?? this.isAgree,
        contractType: contractType ?? this.contractType,
        error: error ?? this.error,
        contractCalculation: contractCalculation ?? this.contractCalculation,
      );

  @override
  List<Object?> get props => [
        configInfo,
        configInfoFetchStatus,
        applyContractStatus,
        selectedContractConfig,
        selectedConfigList,
        selectedAmount,
        contractModelAmount,
        selectedMarket,
        currency,
        isAgree,
        error,
        contractCalculation,
        contractType,
      ];
}
