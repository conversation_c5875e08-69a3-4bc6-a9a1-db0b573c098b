import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/validators.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/app/extension/string_extension.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/enums/signup_type.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/custom_pin_keyboard.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:pinput/pinput.dart';

import '../../../../shared/app/utilities/easy_loading.dart';
import '../../../../shared/widgets/otp_field.dart';
import '../../../account/logic/otp/otp_cubit.dart';
import '../../../account/logic/otp/otp_state.dart';
import '../../../main/widgets/draggable_float_widget.dart';
import '../../logic/profile/profile_cubit.dart';
import '../../widgets/settings/settings_appbar.dart';

class ChangePasswordWithOtpScreen extends StatefulWidget {
  final PasswordModifyType type;
  const ChangePasswordWithOtpScreen({super.key, required this.type});

  @override
  State<ChangePasswordWithOtpScreen> createState() => _ChangePasswordWithOtpScreenState();
}

class _ChangePasswordWithOtpScreenState extends State<ChangePasswordWithOtpScreen> {
  final TextEditingController _phoneEmailController = TextEditingController();
  final TextEditingController _verificationCodeController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  bool _isFormValid = false;
  final OtpCubit _otpCubit = OtpCubit();

  late PinTheme defaultPinTheme;

  @override
  void initState() {
    super.initState();

    final currentPhone = context.read<UserCubit>().state.userInfo?.mobile;
    final currentEmail = context.read<UserCubit>().state.userInfo?.email;
    final hasEmailAndMobile = context.read<UserCubit>().hasEmailAndMobile;
    if (hasEmailAndMobile) {
      context.read<ProfileCubit>().changePasswordTab(SignUpType.phone);
      _phoneEmailController.text = currentPhone ?? '';
    } else {
      if (currentPhone.isNotNullNorEmpty) {
        _phoneEmailController.text = currentPhone ?? '';
        context.read<ProfileCubit>().changePasswordTab(SignUpType.phone);
      } else if (currentEmail.isNotNullNorEmpty) {
        _phoneEmailController.text = currentEmail ?? '';
        context.read<ProfileCubit>().changePasswordTab(SignUpType.email);
      }
    }

    for (var controller in [_verificationCodeController, _passwordController, _confirmPasswordController]) {
      controller.addListener(_validateForm);
    }
  }

  void _validateForm() {
    if (widget.type == PasswordModifyType.financial) {
      final codeValid = _verificationCodeController.text.length >= 4;
      final pinValid = _passwordController.text.length == 6;
      setState(() => _isFormValid = codeValid && pinValid);
      return;
    }

    final codeValid = _verificationCodeController.text.length >= 4;
    final passwordValid = Validators.validatePassword(_passwordController.text) == null;
    final confirmPasswordValid = _passwordController.text == _confirmPasswordController.text && passwordValid;
    setState(() => _isFormValid = codeValid && passwordValid && confirmPasswordValid);
  }

  void _handleSendVerificationCode(SignUpType signUpType) {
    final validation = signUpType == SignUpType.phone
        ? Validators.validateMobile(_phoneEmailController.text)
        : Validators.validateEmail(_phoneEmailController.text);
    if (validation != null) {
      GPEasyLoading.showToast(validation);
      return;
    }

    // Use verifyAndSendOtp to handle captcha requirements
    _otpCubit.verifyAndSendOtp(mobileOrEmail: _phoneEmailController.text, type: signUpType.otpTypeUpdatePassword);
  }

  Future<void> _handleSubmit() async {
    if (!_isFormValid) return;
    final success = await context.read<ProfileCubit>().changePasswordByOtp(
          password: _passwordController.text,
          smsCode: _verificationCodeController.text,
          type: PasswordChangeType.smsVerification,
          passwordModifyType: widget.type,
        );
    if (success && mounted) {
      if (widget.type == PasswordModifyType.account) {
        Helper.logoutUser();
      } else {
        Navigator.pop(context);
      }
    }
  }

  void _showKeyboard(TextEditingController controller) {
    final oldPosition = FloatingPosition.bottomRight;

    FloatingWidgetManager().updatePosition(FloatingPosition.centerRight);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.transparent,
      builder: (_) => WithdrawalPasswordKeyboard(
        controller: controller,
        bottomPadding: MediaQuery.of(context).viewInsets.bottom + MediaQuery.of(context).padding.bottom,
        onChanged: (_) => _validateForm(),
        onSubmit: () {
          Navigator.pop(context);
          FocusScope.of(context).unfocus();
        },
      ),
    ).whenComplete(() => FloatingWidgetManager().updatePosition(oldPosition));
  }

  @override
  Widget build(BuildContext context) {
    // Initialize the defaultPinTheme here where context is available
    defaultPinTheme = PinTheme(
      width: 48,
      height: 48,
      textStyle: context.textTheme.primary.w500,
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(10.gr),
        border: Border.all(color: context.theme.primaryColor, width: 0.1),
      ),
    );

    return BlocProvider(
      create: (_) => _otpCubit,
      child: Scaffold(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        appBar: SettingsAppBar(title: 'phoneVerification'.tr()),
        body: SafeArea(
          child: BlocSelector<ProfileCubit, ProfileState, SignUpType>(
            selector: (state) => state.changePasswordTab,
            builder: (context, state) {
              return ListView(
                padding: EdgeInsets.symmetric(horizontal: 16.gw),
                children: [
                  12.verticalSpace,
                  if (context.read<UserCubit>().hasEmailAndMobile)
                    CommonTabBar(
                      data: ['signUpMobile'.tr(), 'signUpEmail'.tr()],
                      onTap: (index) {
                        context.read<ProfileCubit>().changePasswordTab(SignUpType.values[index]);
                        _phoneEmailController.text = SignUpType.values[index] == SignUpType.phone
                            ? context.read<UserCubit>().state.userInfo?.mobile ?? ''
                            : context.read<UserCubit>().state.userInfo?.email ?? '';
                      },
                      currentIndex: state.index,
                      style: switch (AppConfig.instance.skinStyle) {
                        AppSkinStyle.kGP => CommonTabBarStyle.round,
                        AppSkinStyle.kTemplateD => CommonTabBarStyle.trapezoid,
                        _ => CommonTabBarStyle.line,
                      },
                      backgroundColor: Colors.transparent,
                      isScrollable: false,
                    ),
                  12.verticalSpace,
                  _buildPhoneEmailSection(context, state),
                  24.verticalSpace,
                  _buildPasswordSection(context, state),
                  24.verticalSpace,
                  CustomMaterialButton(
                    buttonText: 'submit'.tr(),
                    onPressed: _isFormValid ? _handleSubmit : null,
                    isEnabled: _isFormValid,
                    height: 48.gw,
                    borderRadius: 8.gr,
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildPhoneEmailSection(BuildContext context, SignUpType selectedTab) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.gr),
        border: Border.all(color: context.theme.dividerColor),
        color: context.theme.cardColor,
      ),
      padding: EdgeInsets.all(16.gr),
      child: BlocBuilder<OtpCubit, OtpState>(
        builder: (context, otpState) {
          return OtpField(
            mobileController: _phoneEmailController,
            codeController: _verificationCodeController,
            otpState: otpState,
            onSendCode: () => _handleSendVerificationCode(selectedTab),
            readOnlyMobile: true,
            readOnlyFieldLabel: selectedTab == SignUpType.phone ? 'currentPhone'.tr() : 'currentEmail'.tr(),
            mobileField: TextFieldWidget(
              controller: _phoneEmailController,
              hintText: selectedTab == SignUpType.phone ? 'loginPhonePlaceholder'.tr() : 'loginEmailPlaceholder'.tr(),
              textInputType: selectedTab == SignUpType.phone ? TextInputType.phone : TextInputType.emailAddress,
              maxLength: selectedTab == SignUpType.phone ? 11 : null,
              counterText: '',
              validator: (value) =>
                  selectedTab == SignUpType.phone ? Validators.validateMobile(value) : Validators.validateEmail(value),
              prefixIcon: SvgPicture.asset(
                selectedTab == SignUpType.phone ? Assets.mobileIcon : Assets.mailIcon,
                fit: BoxFit.scaleDown,
                width: 18.gw,
                height: 18.gw,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPasswordSection(BuildContext context, SignUpType selectedTab) {
    if (widget.type == PasswordModifyType.financial) {
      return Container(
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(8.gr),
          border: Border.all(color: context.theme.dividerColor),
        ),
        padding: EdgeInsets.all(16.gr),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${'newPassword'.tr()} *', style: context.textTheme.stockRed.w500),
            16.verticalSpace,
            GestureDetector(
              onTap: () => _showKeyboard(_passwordController),
              child: AbsorbPointer(
                child: Pinput(
                  controller: _passwordController,
                  length: 6,
                  obscureText: true,
                  defaultPinTheme: defaultPinTheme,
                  focusedPinTheme: defaultPinTheme.copyWith(
                    decoration: defaultPinTheme.decoration!.copyWith(
                      border: Border.all(color: context.theme.primaryColor, width: 0.8),
                    ),
                  ),
                  errorPinTheme: defaultPinTheme.copyWith(
                    decoration: defaultPinTheme.decoration!.copyWith(
                      border: Border.all(color: context.colorTheme.stockRed),
                    ),
                  ),
                  errorTextStyle: context.textTheme.stockRed.fs12.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        border: Border.all(color: context.theme.dividerColor),
      ),
      padding: EdgeInsets.all(16.gr),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 80.gw,
                child: Text(
                  '${'newPassword'.tr()} *',
                  style: context.textTheme.stockRed.w500,
                ),
              ),
              Expanded(
                child: TextFieldWidget(
                  controller: _passwordController,
                  hintText: 'registerPasswordHint'.tr(),
                  textInputType: TextInputType.visiblePassword,
                  inputFormatters: [LengthLimitingTextInputFormatter(16)],
                  obscureText: true,
                  fillColor: Colors.transparent,
                  borderType: TextFieldBorderType.none,
                  passwordIcon: true,
                ),
              ),
            ],
          ),
          Divider(color: context.theme.dividerColor),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 80.gw,
                child: Text(
                  '${'confirmPassword'.tr()} *',
                  style: context.textTheme.stockRed.w500,
                ),
              ),
              Expanded(
                child: TextFieldWidget(
                  controller: _confirmPasswordController,
                  hintText: 'reenterPassword'.tr(),
                  textInputType: TextInputType.visiblePassword,
                  inputFormatters: [LengthLimitingTextInputFormatter(16)],
                  obscureText: true,
                  fillColor: Colors.transparent,
                  borderType: TextFieldBorderType.none,
                  passwordIcon: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _phoneEmailController.dispose();
    _verificationCodeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _otpCubit.close();
    super.dispose();
  }
}
