import 'dart:io';

import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/utils/app_lifecycle_service.dart';
import 'package:gp_stock_app/core/utils/connectivity_util.dart';

/// 网络可达性检测工具类
class NetworkReachability {
  NetworkReachability._();

  /// 判断响应是否看起来像 HTML 网页（如网关页/错误页）
  static bool looksLikeHtml(Response? response) {
    if (response == null) return false;
    try {
      final contentType = response.headers.value('content-type')?.toLowerCase() ?? '';
      if (contentType.contains('text/html')) return true;
      final data = response.data;
      if (data is String) {
        final s = data.toLowerCase();
        if (s.contains('<!doctype html') || s.contains('<html')) return true;
      }
    } catch (_) {}
    return false;
  }

  /// 网络正常但服务器不可达的判定
  static bool isServerUnreachable(DioException err) {
    if (!ConnectivityUtil().status.hasConnection) return false;
    if (AppLifecycleService.instance.inBackground) return false;

    switch (err.type) {
      case DioExceptionType.connectionError:
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
        return true;
      case DioExceptionType.unknown:
        final underlying = err.error;
        return underlying is SocketException || underlying is HandshakeException || underlying is HttpException;
      case DioExceptionType.badResponse:
        final status = err.response?.statusCode ?? 0;
        if (status >= 500) return true;
        if (status == 404 && looksLikeHtml(err.response)) return true;
        return false;
      case DioExceptionType.badCertificate:
      case DioExceptionType.cancel:
      case DioExceptionType.sendTimeout:
        return false;
    }
  }
}
