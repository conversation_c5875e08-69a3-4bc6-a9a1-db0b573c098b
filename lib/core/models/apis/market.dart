import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/models/entities/market/dist_and_flow.dart';
import 'package:gp_stock_app/core/models/entities/market/gain_distribution.dart';
import 'package:gp_stock_app/core/models/entities/market/index_stock_config.dart';
import 'package:gp_stock_app/core/models/entities/market/market_plate_list.dart';
import 'package:gp_stock_app/core/models/entities/market/market_search_result.dart';
import 'package:gp_stock_app/core/models/entities/market/market_status_info.dart';
import 'package:gp_stock_app/core/models/entities/market/plate_info_list.dart';
import 'package:gp_stock_app/core/models/entities/market/quotation_broker.dart';
import 'package:gp_stock_app/core/models/entities/market/stock_company_info.dart';
import 'package:gp_stock_app/core/models/entities/market/stock_kline_entity.dart';
import 'package:gp_stock_app/core/models/entities/market/stock_news_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/stock_plate.dart';

/// 行情接口
class MarketApi {
  /// 获取市场板块列表 / Get market plate list
  /// https://s.apifox.cn/75d4d7bb-feba-4952-8ad6-29a94f49a930/api-*********
  ///
  /// [sortType] - 排序类型：0=成交量, 1=最新价, 2=涨跌幅
  /// [order] - 排序方向：'ASC' 或 'DESC'
  /// [pageNum] - 页码
  /// [stockPlate] - 股票板块（如 StockPlate.cnSzse）
  /// [marketCategory] - 市场分类（如 MarketCategory.cnStocks）
  /// [isHome] - 是否为首页请求（首页不传 plate 参数）
  /// [pageSize] - 每页数量
  Future<MarketPlateList?> fetchMarketPlateList({
    required int sortType,
    required String order,
    required int pageNum,
    required StockPlate? stockPlate,
    required MarketCategory marketCategory,
    required bool isHome,
    required int pageSize,
  }) async {
    try {
      final res = await Http().request<MarketPlateList>(
        ApiEndpoints.getMarketPlate,
        method: HttpMethod.get,
        queryParameters: {
          'marketType': marketCategory.market,
          'securityType': '1',
          if (!isHome && stockPlate != null) 'plate': stockPlate.plateName,
          'field': _getSortFieldParam(sortType),
          'order': order,
          'pageNumber': pageNum.toString(),
          'pageSize': pageSize.toString(),
        },
        needShowToast: false,
        needSignIn: false,
      );
      return res.data;
    } catch (e) {
      return null;
    }
  }

  /// 将排序类型转换为接口字段名 / Convert sort type to API field name
  String _getSortFieldParam(int sortType) {
    return switch (sortType) {
      0 => 'volume',
      1 => 'latestPrice',
      2 => 'gain',
      _ => 'volume',
    };
  }

  /// 获取所有分时数据（支持股票和期货）
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/timeLineUsingGET_3
  /// https://s.apifox.cn/75d4d7bb-feba-4952-8ad6-29a94f49a930/api-232870458
  ///
  /// [futuresScope] - 期货范围，null 表示股票，cn 表示国内期货，global 表示国际期货
  static Future<StockKlineEntity?> fetchKLine({
    required String instrument,
    required String period,
    FuturesScope? futuresScope,
    CancelToken? cancelToken,
  }) async {
    // 根据period 判断是 K-line 还是 timeline
    final isTimeline = _isTimelinePeriod(period);
    // 默认请求股票，
    String endpoint = switch (futuresScope) {
      // 股票
      null => isTimeline ? ApiEndpoints.getTimeLineData : ApiEndpoints.getKlineData,

      // 国内期货
      FuturesScope.cn => isTimeline ? ApiEndpoints.getFuturesMarketTimeLine : ApiEndpoints.getFuturesMarketKLine,

      // 国际期货（或其它）
      _ => isTimeline ? ApiEndpoints.getGlobalFuturesMarketTimeLine : ApiEndpoints.getGlobalFuturesMarketKLine,
    };

    final res = await Http().request<StockKlineEntity>(
      endpoint,
      queryParameters: {
        "instrument": instrument,
        "period": period,
      },
      needShowToast: false,
      needSignIn: false,
      method: HttpMethod.get,
      cancelToken: cancelToken,
    );
    return res.data;
  }

  /// 判断是否是分时周期
  static bool _isTimelinePeriod(String period) {
    return period == 'day' || period == '5day';
  }

  /// 获取票经纪商数据
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getBrokerDataUsingGET_1
  /// https://s.apifox.cn/75d4d7bb-feba-4952-8ad6-29a94f49a930/api-*********
  static Future<QuotationBroker?> fetchBroker({required String instrument}) async {
    final res = await Http().request<QuotationBroker>(
      ApiEndpoints.brokerQueue,
      queryParameters: {
        "instrument": instrument,
      },
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取股票公司新闻
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getCompanyNewsUsingGET_1
  /// https://s.apifox.cn/75d4d7bb-feba-4952-8ad6-29a94f49a930/api-*********
  static Future<StockNewsEntity?> fetchNews({
    required Security security,
    required int page,
  }) async {
    final res = await Http().request<StockNewsEntity>(
      ApiEndpoints.companyNews,
      queryParameters: {
        'symbol': security.symbol,
        'market': security.market,
        'securityType': security.securityType,
        'pageNumber': page,
        'pageSize': 20,
      },
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data;
  }

  static Future<CompanyNewsEntity?> getCompanyNewsDetails({required String id}) async {
    final res = await Http().request<CompanyNewsEntity>(
      ApiEndpoints.companyNewsInfo,
      queryParameters: {
        'id': id,
      },
      method: HttpMethod.get,
      needSignIn: false,
    );
    return res.data;
  }

  /// 获取股票公司新闻
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getCompanyNewsUsingGET_1
  /// https://s.apifox.cn/75d4d7bb-feba-4952-8ad6-29a94f49a930/api-*********
  static Future<StockCompanyInfo?> fetchCompanyInfo({required Security security}) async {
    final res = await Http().request<StockCompanyInfo>(
      ApiEndpoints.companyInfo,
      queryParameters: {
        'symbol': security.symbol,
        'market': security.market,
        'securityType': security.securityType,
      },
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取股票分布和流向数据
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getDistAndFlowUsingGET_1
  /// https://s.apifox.cn/75d4d7bb-feba-4952-8ad6-29a94f49a930/api-*********
  static Future<DistAndFlow?> fetchDistAndFlow({required Security security}) async {
    final res = await Http().request<DistAndFlow>(
      ApiEndpoints.getDistFlow,
      queryParameters: {
        'symbol': security.symbol,
        'market': security.market,
        'securityType': security.securityType,
      },
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取市场状态
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getDistAndFlowUsingGET_1
  /// https://s.apifox.cn/75d4d7bb-feba-4952-8ad6-29a94f49a930/api-232870462
  static Future<MarketStatus?> fetchMarketStatus({required Security security}) async {
    final res = await Http().request<MarketStatus>(
      ApiEndpoints.getMarketStatus,
      queryParameters: {
        'symbol': security.symbol,
        'market': security.market,
        'securityType': security.securityType,
      },
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取所有市场状态数据
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getCombinedMarketStatusUsingGET_1
  static Future<List<MarketStatusInfo>> fetchMarketStatusCombine() async {
    final res = await Http().request<MarketStatusInfoList>(
      ApiEndpoints.getMarketStatusCombine,
      queryParameters: {'marketTypes': "US|HK|CN"},
      method: HttpMethod.get,
      needSignIn: false,
      needShowToast: false,
    );
    return res.data?.list ?? [];
  }

  /// 获取股指配置
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%82%A1%E6%8C%87%E9%85%8D%E7%BD%AE/getConfigUsingGET_3
  static Future<List<IndexStockConfig>> fetchIndexStockConfigList() async {
    final res = await Http().request<IndexStockConfigList>(
      ApiEndpoints.getIndexStockConfig,
      method: HttpMethod.get,
      needSignIn: false,
      needShowToast: false,
    );
    return res.data?.list ?? [];
  }

  /// 搜索
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/getSymbolsByKeywordUsingGET_1
  static Future<List<MarketSearchResult>> fetchSearchResults(String query, {String? market, int? type}) async {
    final res = await Http().request<MarketSearchResultList>(
      ApiEndpoints.search,
      method: HttpMethod.get,
      queryParameters: {
        "keyword": query, // 关键字
        if (market != null) "market": market, // 市场类型 (CN: A股, HK: 港股, US: 美股)
        if (type != null) "type": type, // 类型 (1: 股票, 2: 指数)
      },
      needSignIn: false,
      needShowToast: false,
    );
    return res.data?.list ?? [];
  }

  /// 板块列表（大板块概览）
  static Future<PlateInfoList?> fetchSectorList({
    required MarketCategory category,
    int? pageNum,
    int? securityType,
    String? field,
    String? order,
  }) async {
    final markets = switch (category) {
      MarketCategory.cnStocks => 'SZSE,SSE',
      MarketCategory.hkStocks => 'HKEX',
      MarketCategory.usStocks => 'US',
      _ => '',
    };
    if (markets.isEmpty) return null;

    final res = await Http().request<PlateInfoList>(
      ApiEndpoints.getSectorList,
      method: HttpMethod.get,
      queryParameters: {
        'markets': markets,
        'securityType': securityType ?? 1,
        'pageNumber': pageNum ?? 1,
        'pageSize': 20,
        if (field != null) 'field': field,
        if (order != null) 'order': order,
      },
      needSignIn: false,
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取行业板块详情数据(对应行业板块的股票列表)
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/plateInfoUsingGET_1
  static Future<MarketPlateList?> fetchSectorStockList({
    required String plateId,
    required MarketCategory category,
    int? pageNum,
    int? securityType,
    String? field,
    String? order,
  }) async {
    final markets = switch (category) {
      MarketCategory.cnStocks => 'SZSE,SSE',
      MarketCategory.hkStocks => 'HKEX',
      MarketCategory.usStocks => 'US',
      _ => '',
    };
    if (markets.isEmpty) return null;

    final res = await Http().request<MarketPlateList>(
      ApiEndpoints.getSectorStockList,
      method: HttpMethod.get,
      queryParameters: {
        'plateId': plateId,
        'markets': markets,
        'securityType': securityType ?? 1,
        'pageNumber': pageNum ?? 1,
        'pageSize': 20,
        if (field != null) 'field': field,
        if (order != null) 'order': order,
      },
      needSignIn: false,
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取今日股市的涨跌分布数据
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/gainDistributionUsingGET_1
  static Future<List<int>?> fetchGainDistributionList({
    required MarketCategory category,
  }) async {
    final markets = switch (category) {
      MarketCategory.cnStocks => 'SZSE,SSE',
      MarketCategory.hkStocks => 'HKEX',
      MarketCategory.usStocks => 'US',
      _ => '',
    };
    if (markets.isEmpty) return null;

    final res = await Http().request<GainDistribution>(
      ApiEndpoints.getGainDistribution,
      method: HttpMethod.get,
      queryParameters: {
        'markets': markets,
        'securityType': 1,
      },
      needSignIn: false,
      needShowToast: false,
    );
    return res.data?.list ?? [];
  }
}
