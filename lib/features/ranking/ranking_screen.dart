import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/ranking/index.dart';
import 'package:gp_stock_app/features/ranking/logic/ranking_cubit.dart';
import 'package:gp_stock_app/features/ranking/logic/ranking_state.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

class RankingScreen extends StatefulWidget {
  const RankingScreen({super.key});

  @override
  State<RankingScreen> createState() => _RankingScreenState();
}

class _RankingScreenState extends State<RankingScreen> {
  @override
  void initState() {
    super.initState();
    context.read<RankingCubit>().fetchRankList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ranking'),
      ),
      body: BlocSelector<RankingCubit, RankingState, RankTabType>(
        selector: (state) => state.currentRankTabType,
        builder: (context, currentTab) {
          return Column(
            children: [
              getTabBar(
                context,
                tabs: RankTabType.values.map((e) => e.name.tr()).toList(),
                currentIndex: currentTab.index,
                onTabSelected: (index) => context.read<RankingCubit>().switchRankTab(RankTabType.values[index]),
              ),
              getTabView(
                context,
                currentIndex: currentTab.index,
                onPageChanged: (index) => context.read<RankingCubit>().switchRankTab(RankTabType.values[index]),
              )
            ],
          );
        },
      ),
    );
  }

  Expanded getTabView(
    BuildContext context, {
    required int currentIndex,
    required Function(int) onPageChanged,
  }) {
    return Expanded(
      child: DirectSlideView(
        pages: RankTabType.values.map((e) => RankingView()).toList(),
        pageIndex: currentIndex,
        onPageChanged: onPageChanged,
      ),
    );
  }

  Widget getTabBar(
    BuildContext context, {
    required List<String> tabs,
    required int currentIndex,
    required Function(int) onTabSelected,
  }) {
    return Container(
      width: double.infinity,
      color: context.theme.appBarTheme.backgroundColor,
      padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 4.gw),
      child: CommonTabBar.withAutoKey(
        tabs,
        currentIndex: currentIndex,
        onTap: onTabSelected,
        style: CommonTabBarStyle.line,
        isScrollable: false,
        backgroundColor: context.theme.appBarTheme.backgroundColor,
      ),
    );
  }
}
