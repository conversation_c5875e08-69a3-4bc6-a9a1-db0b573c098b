import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/market/watch_list/widgets/wishlist_data_table.dart';
import 'charts/index_stock_list_view.dart';
import 'charts/home_index_cards.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import '../home_section_style.dart';
import 'market_data_table.dart';

/// 首页行情 Tab 区域（通用样式）
class HomeMarketSection extends StatelessWidget {
  final int tabIndex;
  final List<MarketSectionTab> tabTypes;
  final Function(int) onTabChanged;
  final HomeSectionStyle style;

  const HomeMarketSection({
    super.key,
    required this.tabIndex,
    required this.tabTypes,
    required this.onTabChanged,
    this.style = const HomeSectionStyle(),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: style.clipBehavior,
      margin: style.margin,
      padding: style.padding,
      decoration: style.decoration,
      child: switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kGP => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 8.gw,
            children: [
              Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.gw)),
                margin: EdgeInsets.symmetric(horizontal: 16.gw),
                child: _buildTabHeader(context),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 2.gw),
                child: _buildTabContent(context),
              ),
            ],
          ),
        _ => Column(
            spacing: 16.gw,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTabHeader(context),
              _buildTabContent(context),
            ],
          ),
      },
    );
  }

  Widget _buildTabHeader(BuildContext context) {
    final tabData = tabTypes.map((tabType) => tr(tabType.name, context: context)).toList();

    final tabStyle = switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateB => CommonTabBarStyle.line,
      AppSkinStyle.kTemplateC || AppSkinStyle.kTemplateD || AppSkinStyle.kGP => CommonTabBarStyle.rectangular,
      AppSkinStyle.kZangGolden => CommonTabBarStyle.rectangular,
    };

    return CommonTabBar.withAutoKey(
      tabData,
      currentIndex: tabIndex,
      onTap: onTabChanged,
      style: tabStyle,
      isScrollable: switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kGP => false,
        _ => true,
      },
      height: 30.gw,
      labelPadding: EdgeInsets.symmetric(horizontal: 5.gw),
    );
  }

  Widget _buildTabContent(BuildContext context) {
    if (tabTypes.isEmpty || tabIndex >= tabTypes.length) {
      return const SizedBox.shrink();
    }

    final tabType = tabTypes[tabIndex];

    return switch (tabType) {
      MarketSectionTab.stockIndex => switch (AppConfig.instance.skinStyle) {
          AppSkinStyle.kGP => HomeIndexCards(isFromHome: true),
          _ => IndexStockListView(),
        },
      MarketSectionTab.stocks => switch (AppConfig.instance.skinStyle) {
          AppSkinStyle.kGP => Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.gw),
              child: const MarketDataTable(limit: 5, isHome: true),
            ),
          _ => const MarketDataTable(limit: 5, isHome: true, showInCard: true),
        },
      MarketSectionTab.cnFutures => BlocProvider<CNFuturesListCubit>(
          key: const ValueKey('home_cn_futures'),
          create: (_) => CNFuturesListCubit(FTradeListRepository(), showInHomePage: true),
          child: Padding(
            padding: switch (AppConfig.instance.skinStyle) {
              AppSkinStyle.kGP => EdgeInsets.symmetric(horizontal: 12.gw),
              _ => EdgeInsets.zero,
            },
            child: FTradeListScreen(
              scope: FuturesScope.cn,
              showInHomePage: true,
              showInCard: switch (AppConfig.instance.skinStyle) {
                AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateC => true,
                _ => false,
              },
            ),
          ),
        ),
      MarketSectionTab.globalFutures => BlocProvider<GlobalFuturesListCubit>(
          key: const ValueKey('home_global_futures'),
          create: (_) => GlobalFuturesListCubit(FTradeListRepository(), showInHomePage: true),
          child: Padding(
            padding: switch (AppConfig.instance.skinStyle) {
              AppSkinStyle.kGP => EdgeInsets.symmetric(horizontal: 12.gw),
              _ => EdgeInsets.zero,
            },
            child: FTradeListScreen(
              scope: FuturesScope.global,
              showInHomePage: true,
              showInCard: switch (AppConfig.instance.skinStyle) {
                AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateC => true,
                _ => false,
              },
            ),
          ),
        ),
      MarketSectionTab.watchList => Padding(
          padding: switch (AppConfig.instance.skinStyle) {
            AppSkinStyle.kGP => EdgeInsets.symmetric(horizontal: 12.gw),
            _ => EdgeInsets.zero,
          },
          child: WishListDataTable(
              limit: 5,
              isFromHome: true,
              showInCard: switch (AppConfig.instance.skinStyle) {
                AppSkinStyle.kGP => null,
                _ => true,
              }),
        ),
    };
  }

}
