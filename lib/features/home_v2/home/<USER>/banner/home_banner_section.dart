import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../home_section_style.dart';
import 'home_banner_cell.dart';

/// 首页 Banner 轮播区域（通用样式）
class HomeBannerSection extends StatefulWidget {
  final List<BannerEntity>? bannerList;
  final DataStatus status;
  final int currentIndex;
  final Function(int) onIndexChanged;
  final Function(BannerEntity)? onBannerTap;
  final HomeSectionStyle style;

  const HomeBannerSection({
    super.key,
    required this.bannerList,
    required this.status,
    required this.currentIndex,
    required this.onIndexChanged,
    this.onBannerTap,
    required this.style,
  });

  @override
  State<HomeBannerSection> createState() => _HomeBannerSectionState();
}

class _HomeBannerSectionState extends State<HomeBannerSection> {
  final CarouselSliderController _controller = CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    if (widget.status == DataStatus.loading) {
      return _buildShimmer(context);
    }
    if (widget.bannerList == null || widget.bannerList!.isEmpty) {
      return const SizedBox.shrink();
    }
    return _buildCarousel(context);
  }

  Widget _buildShimmer(BuildContext context) {
    final bannerHeight = _getBannerHeight();
    final radius = _getRadius();

    return Padding(
      padding: widget.style.margin ?? EdgeInsets.zero,
      child: ShimmerWidget(
        width: double.infinity,
        height: bannerHeight,
        radius: radius,
      ),
    );
  }

  Widget _buildCarousel(BuildContext context) {
    final bannerHeight = _getBannerHeight();
    final radius = _getRadius();
    final aspectRatio = _getAspectRatio();
    final margin = widget.style.margin ?? EdgeInsets.zero;

    final carousel = CarouselSlider.builder(
      options: CarouselOptions(
        aspectRatio: aspectRatio,
        viewportFraction: 1,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 5),
        enableInfiniteScroll: true,
        onPageChanged: (index, reason) => widget.onIndexChanged(index),
      ),
      itemCount: widget.bannerList!.length,
      itemBuilder: (context, itemIndex, pageViewIndex) {
        final banner = widget.bannerList![itemIndex];
        return HomeBannerCell(
          banner: banner,
          height: bannerHeight,
          radius: radius,
          margin: margin,
          onTap: widget.onBannerTap != null
              ? () => widget.onBannerTap!(banner)
              : null,
        );
      },
      carouselController: _controller,
    );

    final dotIndicator = _buildDotIndicator(context);

    // GP & YHXT 使用 Stack 布局
    if (_useStackLayout()) {
      return Stack(
        children: [
          carousel,
          dotIndicator,
        ],
      );
    }

    return Column(
      spacing: 6.gw,
      children: [carousel, dotIndicator],
    );
  }

  Widget _buildDotIndicator(BuildContext context) {
    final bannerList = widget.bannerList;
    if (bannerList == null || bannerList.isEmpty) {
      return const SizedBox.shrink();
    }

    // GP 使用圆点样式
    if (_useCircleIndicator()) {
      return Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: bannerList.indexed.map((item) {
            final (index, _) = item;
            return GestureDetector(
              onTap: () => _controller.animateToPage(index),
              child: Container(
                width: 6.0.gw,
                height: 6.0.gw,
                margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: widget.currentIndex == index
                      ? context.theme.primaryColor.withNewOpacity(0.9)
                      : Colors.white.withNewOpacity(0.5),
                ),
              ),
            );
          }).toList(),
        ),
      );
    }

    // tempa 不显示指示器
    if (AppConfig.instance.skinStyle == AppSkinStyle.kTemplateA) {
      return const SizedBox.shrink();
    }

    // 默认使用 DotsIndicator
    return DotsIndicator(
      dotsCount: bannerList.length,
      position: widget.currentIndex.toDouble(),
      decorator: DotsDecorator(
        size: const Size.square(9.0),
        activeSize: const Size(23.0, 7.0),
        color: context.colorTheme.textRegular,
        activeColor: context.theme.primaryColor,
        activeShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
        ),
      ),
    );
  }

  double _getBannerHeight() {
    return switch (AppConfig.instance.skinStyle) {
      _ when AppConfig.instance.flavor == Flavor.yhxt => 126.gw,
      AppSkinStyle.kTemplateC || AppSkinStyle.kTemplateD => 126.gw,
      _ => 188.gw,
    };
  }

  double _getRadius() {
    return switch (AppConfig.instance.skinStyle) {
      _ when AppConfig.instance.flavor == Flavor.yhxt => 8.gr,
      AppSkinStyle.kTemplateA => 4.gr,
      _ => 8.gr,
    };
  }

  double _getAspectRatio() {
    return switch (AppConfig.instance.skinStyle) {
      _ when AppConfig.instance.flavor == Flavor.yhxt => 348 / 126,
      AppSkinStyle.kTemplateC || AppSkinStyle.kTemplateD => 348 / 126,
      _ => 373 / 120,
    };
  }

  bool _useCircleIndicator() =>
      AppConfig.instance.skinStyle == AppSkinStyle.kGP ||
      AppConfig.instance.flavor == Flavor.yhxt;

  bool _useStackLayout() => _useCircleIndicator();
}
