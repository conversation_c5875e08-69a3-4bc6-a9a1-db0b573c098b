import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/withdraw_config_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/withdraw_config_entity.g.dart';

@JsonSerializable()
class WithdrawConfigEntity {
  final String endWithdrawalTime;
  final int handlingFeeType;
  final int id;
  final int maxWithdrawalAmount;
  final int minWithdrawalAmount;
  final String startWithdrawalTime;
  final bool testUsersCanWithdraw;
  final int withdrawalFee;
  final bool withdrawalStatus;
  final int withdrawalsDayCount;

  const WithdrawConfigEntity({
    this.endWithdrawalTime = '',
    this.handlingFeeType = 0,
    this.id = 0,
    this.maxWithdrawalAmount = 0,
    this.minWithdrawalAmount = 0,
    this.startWithdrawalTime = '',
    this.testUsersCanWithdraw = false,
    this.withdrawalFee = 0,
    this.withdrawalStatus = false,
    this.withdrawalsDayCount = 0,
  });

  factory WithdrawConfigEntity.fromJson(Map<String, dynamic> json) => $WithdrawConfigEntityFromJson(json);

  Map<String, dynamic> toJson() => $WithdrawConfigEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}