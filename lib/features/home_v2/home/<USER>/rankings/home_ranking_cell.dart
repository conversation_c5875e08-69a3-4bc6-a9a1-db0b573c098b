import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';
import 'package:gp_stock_app/shared/widgets/avatar/app_avatar.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/features/ranking/index.dart';

class HomeRankingCell extends StatelessWidget {
  final RankingEntity model;
  final RankTabType rankType;

  const HomeRankingCell({super.key, required this.model, required this.rankType});

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 13.gw,
      children: [
        SizedBox(
          width: 10.gw,
          child: Text(
            "${model.rankingNo}",
            style: TextStyle(
              color: _getRankColor(model.rankingNo),
              fontSize: 14.gsp,
              fontWeight: FontWeight.bold,
              fontFeatures: const [FontFeature.tabularFigures()],
            ),
          ),
        ),
        AppAvatar(avatar: model.avatar, size: 20.gw),
        Expanded(
            child: Text(
          model.nickName,
          style: TextStyle(fontSize: 12.gsp, color: Color(0xFF333333)),
        )),
        Text(
          "${_getTimeStr()} +${model.amount.formatLargeNumber(context.locale.languageCode)}",
          style: TextStyle(fontSize: 10.gsp, color: Color(0xFFA3A3A3)),
        ),
      ],
    );
  }

  Color _getRankColor(int? rank) {
    return switch (rank) {
      1 => Color(0xFFE25962),
      2 => Color(0xFFFFC259),
      3 => Color(0xFFF0D543),
      _ => Color(0xFF333333),
    };
  }

  String _getTimeStr() {
    return switch (rankType) {
      RankTabType.daily => "昨日收益",
      RankTabType.weekly => "近一周收益",
      RankTabType.monthly => "近一月收益",
    };
  }
}
