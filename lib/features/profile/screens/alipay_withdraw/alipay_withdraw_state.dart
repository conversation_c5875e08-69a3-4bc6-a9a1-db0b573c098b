import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_wallet_entity_list.dart';
import 'package:gp_stock_app/core/models/entities/wallet/withdraw_config_entity.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AlipayWithdrawState extends Equatable {
  final int? withdrawalAmount;
  final DataStatus submitStatus;
  final bool isAmountValid;
  final WithdrawConfigEntity? withdrawConfigEntity;
  final DataStatus withdrawConfigStatus;
  final List<UserWalletEntity> alipayAccounts;
  final DataStatus alipayAccountStatus;
  final UserWalletEntity? selectedAlipayAccount;
  final DataStatus bindWalletStatus;
  final bool isEditMode;
  final ({DataStatus? status, int? id})? unbindWalletStatus;
  const AlipayWithdrawState(
      {this.withdrawalAmount,
      this.submitStatus = DataStatus.idle,
      this.isAmountValid = true,
      this.withdrawConfigEntity,
      this.withdrawConfigStatus = DataStatus.idle,
      this.alipayAccounts = const [],
      this.alipayAccountStatus = DataStatus.idle,
      this.selectedAlipayAccount,
      this.bindWalletStatus = DataStatus.idle,
      this.isEditMode = false,
      this.unbindWalletStatus = (id: null, status: DataStatus.idle)});

  AlipayWithdrawState copyWith({
    int? Function()? withdrawalAmount,
    DataStatus? submitStatus,
    bool? isAmountValid,
    double? minAmount,
    double? maxAmount,
    WithdrawConfigEntity? withdrawConfigEntity,
    DataStatus? withdrawConfigStatus,
    List<UserWalletEntity>? alipayAccounts,
    DataStatus? alipayAccountStatus,
    UserWalletEntity? selectedAlipayAccount,
    DataStatus? bindWalletStatus,
    bool? isEditMode,
    ({DataStatus? status, int? id})? unbindWalletStatus,
  }) {
    return AlipayWithdrawState(
      withdrawalAmount: withdrawalAmount != null ? withdrawalAmount() : this.withdrawalAmount,
      submitStatus: submitStatus ?? this.submitStatus,
      isAmountValid: isAmountValid ?? this.isAmountValid,
      withdrawConfigEntity: withdrawConfigEntity ?? this.withdrawConfigEntity,
      withdrawConfigStatus: withdrawConfigStatus ?? this.withdrawConfigStatus,
      alipayAccounts: alipayAccounts ?? this.alipayAccounts,
      alipayAccountStatus: alipayAccountStatus ?? this.alipayAccountStatus,
      selectedAlipayAccount: selectedAlipayAccount ?? this.selectedAlipayAccount,
      bindWalletStatus: bindWalletStatus ?? this.bindWalletStatus,
      isEditMode: isEditMode ?? this.isEditMode,
      unbindWalletStatus: unbindWalletStatus ?? this.unbindWalletStatus,
    );
  }

  bool get canSubmit {
    return withdrawalAmount != null &&
        withdrawalAmount! > 0 &&
        isAmountValid &&
        selectedAlipayAccount != null &&
        submitStatus != DataStatus.loading;
  }

  @override
  List<Object?> get props => [
        withdrawalAmount,
        submitStatus,
        isAmountValid,
        withdrawConfigEntity,
        withdrawConfigStatus,
        alipayAccounts,
        alipayAccountStatus,
        selectedAlipayAccount,
        bindWalletStatus,
        isEditMode,
        unbindWalletStatus,
      ];
}
