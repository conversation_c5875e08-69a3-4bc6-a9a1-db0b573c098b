import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract_appley_record_list.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/convert_helper.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';

class ContractApplyRecordCell extends StatelessWidget {
  final ContractAppleyRecord record;
  const ContractApplyRecordCell({super.key, required this.record});

  @override
  Widget build(BuildContext context) {
    return AnimationConfiguration.synchronized(
      duration: const Duration(milliseconds: 300),
      child: ScaleAnimation(
        scale: 0.95,
        child: ShadowBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    record.contractLabel,
                    style: context.textTheme.primary.fs12.w600,
                  ),
                  Text(
                    ContractAuditStatus.values[record.auditStatus].text.tr(),
                    style: context.textTheme.primary.copyWith(
                      color: ContractAuditStatus.values[record.auditStatus].color(context),
                    ),
                  ),
                ],
              ),
              5.verticalSpace,
              Divider(color: context.theme.dividerColor),
              5.verticalSpace,
              _DataField(
                label: 'contractMargin'.tr(),
                value: record.totalCash.formatWithCommas(),
              ),
              _DataField(
                label: 'totalMargin'.tr(),
                value: record.totalPower.formatWithCommas(),
              ),
              _DataField(
                label: 'applyTime'.tr(),
                value: ConvertHelper.formatDateType1(
                  record.createTime,
                  separator: '-',
                  showSeconds: true,
                ),
                valueStyle: context.textTheme.primary.w600,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _DataField extends StatelessWidget {
  final String label;
  final String value;
  final TextStyle? valueStyle;
  const _DataField({
    required this.label,
    required this.value,
    this.valueStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: context.textTheme.regular.fs13,
        ),
        Text(
          value,
          style: valueStyle ?? context.textTheme.primary.w600.ffAkz,
        ),
      ],
    );
  }
}
