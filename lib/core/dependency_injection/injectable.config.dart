// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

import '../../features/account/domain/repository/bank_repository.dart' as _i908;
import '../../features/account/domain/services/bank_service.dart' as _i429;
import '../../features/account/logic/deposit_channel/deposit_channel_cubit.dart'
    as _i1054;
import '../../features/account/logic/otp/otp_cubit.dart' as _i101;
import '../../features/account/logic/proxy_pay_channel/proxy_pay_channel_cubit.dart'
    as _i419;
import '../../features/account_v2/0_home/account_screen_cubit_v2.dart' as _i608;
import '../../features/account_v2/1_common/fund_records/fund_records_cubit.dart'
    as _i1022;
import '../../features/activity/activity_cubit.dart' as _i240;
import '../../features/chat/logic/chat/chat_cubit.dart' as _i190;
import '../../features/company_news/logic/news/company_news_cubit.dart' as _i32;
import '../../features/contract/logic/contract_terminate/contract_terminate_cubit.dart'
    as _i51;
import '../../features/convert_rate/logic/convert_rate/convert_rate_cubit.dart'
    as _i810;
import '../../features/forgot/logic/forgot/forgot_cubit.dart' as _i620;
import '../../features/invite/logic/invite/invite_cubit.dart' as _i1070;
import '../../features/main/logic/main/main_cubit.dart' as _i1007;
import '../../features/market/logic/cubit/index_trade_cubit.dart' as _i947;
import '../../features/market/logic/market/market_cubit.dart' as _i360;
import '../../features/market/logic/market_status/market_status_cubit.dart'
    as _i727;
import '../../features/market/watch_list/logic/watch_list_cubit.dart' as _i749;
import '../../features/notifications/logic/notifications_cubit.dart' as _i719;
import '../../features/profile/logic/app_info/app_info_cubit.dart' as _i836;
import '../../features/profile/logic/auth_n/auth_n_cubit.dart' as _i597;
import '../../features/profile/logic/help/help_cubit.dart' as _i201;
import '../../features/profile/logic/mission_center/mission_activity_cubit.dart'
    as _i259;
import '../../features/profile/screens/alipay_deposit/alipay_deposit_cubit.dart'
    as _i487;
import '../../features/profile/screens/usdt_withdraw/usdt_withdraw_cubit.dart'
    as _i338;
import '../../features/sign_in/logic/sign_in/sign_in_cubit.dart' as _i821;
import '../../shared/logic/exchange_rate/exchange_rate_cubit.dart' as _i695;
import '../../shared/logic/selected_exchange_cubit/selected_exchange_cubit.dart'
    as _i86;
import '../../shared/logic/sort_color/sort_color_cubit.dart' as _i1;
import '../../shared/logic/sys_settings/sys_settings_cubit.dart' as _i1045;
import '../../shared/routes/navigator_utils.dart' as _i657;
import '../../shared/services/polling/polling_service.dart' as _i1013;
import '../../shared/services/polling/polling_service_v2.dart' as _i117;
import '../../shared/services/web_socket/web_socket_interface.dart' as _i378;
import '../../shared/services/web_socket/web_socket_service.dart' as _i13;
import '../services/app_info_service.dart' as _i680;
import '../services/image_picker/image_picker_repository.dart' as _i531;
import '../services/image_picker/image_picker_service.dart' as _i818;
import '../services/user/user_cubit.dart' as _i85;
import 'cubit_module.dart' as _i551;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final cubitModule = _$CubitModule();
    gh.singleton<_i378.WebSocketService>(() => _i13.WebSocketChannelService());
    gh.factory<_i487.AlipayDepositCubit>(() => cubitModule.alipayDepositCubit);
    gh.factory<_i680.AppInfoService>(() => _i680.AppInfoService());
    gh.factory<_i51.ContractTerminateCubit>(
        () => _i51.ContractTerminateCubit());
    gh.factory<_i32.CompanyNewsCubit>(() => _i32.CompanyNewsCubit());
    gh.factory<_i1070.InviteCubit>(() => _i1070.InviteCubit());
    gh.factory<_i201.HelpCubit>(() => _i201.HelpCubit());
    gh.factory<_i338.UsdtWithdrawCubit>(() => _i338.UsdtWithdrawCubit());
    gh.factory<_i360.MarketCubit>(() => _i360.MarketCubit());
    gh.factory<_i620.ForgotCubit>(() => _i620.ForgotCubit());
    gh.factory<_i101.OtpCubit>(() => _i101.OtpCubit());
    gh.factory<_i1054.DepositChannelCubit>(() => _i1054.DepositChannelCubit());
    gh.factory<_i419.ProxyPayChannelCubit>(() => _i419.ProxyPayChannelCubit());
    gh.factory<_i1022.FundRecordsCubit>(() => _i1022.FundRecordsCubit());
    gh.factory<_i810.ConvertRateCubit>(() => _i810.ConvertRateCubit());
    gh.factory<_i240.ActivityCubit>(() => _i240.ActivityCubit());
    gh.singleton<_i85.UserCubit>(() => _i85.UserCubit());
    gh.singleton<_i190.ChatCubit>(() => _i190.ChatCubit());
    gh.singleton<_i836.AppInfoCubit>(() => _i836.AppInfoCubit());
    gh.singleton<_i259.MissionActivityCubit>(
        () => _i259.MissionActivityCubit());
    gh.singleton<_i947.IndexTradeCubit>(() => _i947.IndexTradeCubit());
    gh.singleton<_i727.MarketStatusCubit>(() => _i727.MarketStatusCubit());
    gh.singleton<_i749.WatchListCubit>(() => _i749.WatchListCubit());
    gh.singleton<_i1007.MainCubit>(() => _i1007.MainCubit());
    gh.singleton<_i608.AccountScreenCubitV2>(
        () => _i608.AccountScreenCubitV2());
    gh.singleton<_i719.NotificationsCubit>(() => _i719.NotificationsCubit());
    gh.singleton<_i1045.SysSettingsCubit>(() => _i1045.SysSettingsCubit());
    gh.singleton<_i1.SortColorCubit>(() => _i1.SortColorCubit());
    gh.singleton<_i695.ExchangeRateCubit>(() => _i695.ExchangeRateCubit());
    gh.singleton<_i657.NavigatorService>(() => _i657.NavigatorService());
    gh.singleton<_i1013.PollingService>(() => _i1013.PollingService());
    gh.singleton<_i117.PollingServiceV2>(() => _i117.PollingServiceV2());

    gh.factory<_i531.ImagePickerRepository>(() => _i818.ImagePickerService());
    gh.singleton<_i908.BankRepository>(() => _i429.BankService());
    gh.lazySingleton<_i821.SignInCubit>(
        () => _i821.SignInCubit(gh<_i378.WebSocketService>()));
    gh.singleton<_i597.AuthNCubit>(
        () => _i597.AuthNCubit(gh<_i531.ImagePickerRepository>()));
    gh.factoryParam<_i86.SelectedExchangeCubit, String, dynamic>((
      uniqueKey,
      _,
    ) =>
        _i86.SelectedExchangeCubit(uniqueKey));
    return this;
  }
}

class _$CubitModule extends _i551.CubitModule {}
