import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/features/account/domain/models/deposit_channel/deposit_channel_model.dart';
import 'package:gp_stock_app/features/account/domain/models/proxy_pay_channel/proxy_pay_channel_model.dart';

import '../models/deposit_records/deposit_record.dart';
import '../models/pay_order/pay_order_response.dart';
import '../models/withdrawal_records/withdrawal_record.dart';

abstract class BankRepository {
  Future<ResponseResult<bool>> pay({
    required int payAmount,
    int? userBankId,
    String? orderNumber,
    required int sysBankCardId,
    int? type,
  });

  Future<ResponseResult<WithdrawalRecordResponse>> getWithdrawalRecords(int pageNumber, int pageSize);
  Future<ResponseResult<DepositRecordResponse>> getDepositRecords(int pageNumber, int pageSize);
  Future<ResponseResult<PayOrderResponse>> getPayOrders({
    required int pageNumber,
    required int pageSize,
    String? createDateStart,
    String? createDateEnd,
  });
  Future<ResponseResult<List<DepositChannelModel>>> getChannelList();
  Future<ResponseResult<List<ProxyPayChannelModel>>> getProxyPayChannelList();
}
