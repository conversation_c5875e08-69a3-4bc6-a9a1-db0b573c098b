import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_channel.dart';

class UsdtDepositState extends Equatable {
  final List<USDTDepositChannel> usdtList;
  final USDTDepositChannel? selectedChannel;
  final String? selectedAmount;
  final bool isSubmitting;
  final bool isAmountValid;
  final bool isLoadingChannels;

  const UsdtDepositState({
    this.usdtList = const [],
    this.selectedChannel,
    this.selectedAmount,
    this.isSubmitting = false,
    this.isAmountValid = true,
    this.isLoadingChannels = false,
  });

  UsdtDepositState copyWith({
    List<USDTDepositChannel>? usdtList,
    USDTDepositChannel? selectedChannel,
    String? selectedAmount,
    bool? isSubmitting,
    bool? isAmountValid,
    bool? isLoadingChannels,
  }) {
    return UsdtDepositState(
      usdtList: usdtList ?? this.usdtList,
      selectedChannel: selectedChannel ?? this.selectedChannel,
      selectedAmount: selectedAmount ?? this.selectedAmount,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isAmountValid: isAmountValid ?? this.isAmountValid,
      isLoadingChannels: isLoadingChannels ?? this.isLoadingChannels,
    );
  }

  @override
  List<Object?> get props => [
        usdtList,
        selectedChannel,
        selectedAmount,
        isSubmitting,
        isAmountValid,
        isLoadingChannels,
      ];
}
