import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_normal_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'apply_normal_contract_state.dart';

class ApplyNormalContractCubit extends Cubit<ApplyNormalContractState> {
  ApplyNormalContractCubit() : super(const ApplyNormalContractState());

  void getConfigInfo(double exchangeRate, {required InstrumentType contractType}) async {
    emit(state.copyWith(configInfoFetchStatus: DataStatus.loading, contractType: contractType));
    final result = await ContractApi.fetchApplyNormalContractConfig(type: contractType.value);
    if (result == null) {
      emit(state.copyWith(configInfoFetchStatus: DataStatus.failed));
      return;
    }

    final contractConfigMap = result.contractConfigMap;
    if (contractConfigMap.isEmpty) {
      emit(state.copyWith(configInfoFetchStatus: DataStatus.failed));
      return;
    }

    // 选择默认配置/市场/金额
    final firstContractConfig = contractConfigMap.first;
    selectConfigList(
      firstContractConfig.configList.firstOrNull ?? ApplyNormalContractConfigContractConfigMapConfigList(),
      exchangeRate,
    );
    selectContractConfig(firstContractConfig, exchangeRate);
    selectMarket(result.ruleMap.firstOrNull ?? ApplyNormalContractConfigRuleMap(), exchangeRate);
    selectAmount(result.amountList.firstOrNull?.applyAmount, exchangeRate);

    emit(state.copyWith(configInfoFetchStatus: DataStatus.success, configInfo: result));
  }

  void selectContractConfig(
      ApplyNormalContractConfigContractConfigMap contractConfig, double exchangeRate) {
    selectConfigList(
        contractConfig.configList.firstOrNull ??
            ApplyNormalContractConfigContractConfigMapConfigList(),
        exchangeRate);
    emit(state.copyWith(selectedContractConfig: contractConfig));
  }

  void selectConfigList(
      ApplyNormalContractConfigContractConfigMapConfigList configList, double exchangeRate) {
    emit(state.copyWith(selectedConfigList: configList));
    calculateContractAmount(exchangeRate);
  }

  void selectMarket(ApplyNormalContractConfigRuleMap rule, double exchangeRate) {
    emit(state.copyWith(selectedMarket: rule));
    _updateCurrency(rule.market);
    calculateContractAmount(exchangeRate);
  }

  void selectAmount(int? amount, double exchangeRate) {
    emit(state.copyWith(selectedAmount: () => amount));
    calculateContractAmount(exchangeRate);
  }

  Future<void> calculateContractAmount(double exchangeRate) async {
    // 后端计算与本地计算分开，调用方可以 await 确保状态已更新
    await getApplyAmount(contractType: state.contractType);
    final amount = state.selectedAmount;
    try {
      final principal = amount?.toDouble() ?? 0;
      final multiple = state.selectedConfigList?.multiple ?? 1;

      final warnRatio = state.selectedMarket?.warnLossRadio ?? 0;
      final closeRatio = state.selectedMarket?.closeLossRadio ?? 0;

      double adjustedPrincipal = principal;

      // Calculate leverage amount
      final multipleAmount = adjustedPrincipal * multiple;

      // Total trading funds
      final total = multipleAmount + adjustedPrincipal;

      // Warning loss line
      final warn = multipleAmount + (adjustedPrincipal * warnRatio / 100);

      // Close loss line
      final close = multipleAmount + ((adjustedPrincipal * (100 - closeRatio) / 100));

      // Calculate interest
      final interestRate = state.selectedConfigList?.interestRate ?? 0;
      final percent = multipleAmount * interestRate / 100;

      final deductionAmount = (adjustedPrincipal + percent) / exchangeRate;

      // Update contract model amount
      final contractModelAmount = ContractModelAmount(
        totalTadingFunds: total,
        lossWarningLine: warn,
        lossFlatLine: close,
        interestRate: percent,
        deductionAmount: deductionAmount,
        actualAmount: adjustedPrincipal,
        intrestDeductionAmount: 0,
      );

      emit(state.copyWith(contractModelAmount: contractModelAmount));
    } catch (e) {
      emit(state.copyWith(error: 'Error calculating contract amount: ${e.toString()}'));
    }
  }

  void updateIsAgree(bool isAgree) => emit(state.copyWith(isAgree: isAgree));

  void applyContract({required InstrumentType contractType}) async {
    emit(state.copyWith(applyContractStatus: DataStatus.loading));
    final result = await ContractApi.applyNormalContract(
      applyAmount: state.selectedAmount ?? 0,
      contractConfigId: state.selectedConfigList?.id ?? 0,
      riskId: state.selectedMarket?.id ?? 0,
      type: contractType.value,
    );
    if (result) {
      emit(state.copyWith(applyContractStatus: DataStatus.success));
    } else {
      emit(state.copyWith(applyContractStatus: DataStatus.failed));
    }
  }

  /// Updates currency based on the market type
  void _updateCurrency(String market) => emit(state.copyWith(
          currency: switch (market) {
        'CN' => 'CNY',
        'HK' => 'HKD',
        'US' => 'USD',
        _ => state.currency,
      }));

  Future<void> getApplyAmount({required InstrumentType contractType}) async {
    if (!((state.selectedAmount ?? 0) > 0)) return;
    final result = await ContractApi.getNormalContractAmount(
      contractConfigId: state.selectedConfigList?.id ?? 0,
      riskId: state.selectedMarket?.id ?? 0,
      applyAmount: state.selectedAmount ?? 0,
      type: contractType.value,
    );
    if (result != null) {
      emit(state.copyWith(contractCalculation: result));
    }
  }
}
