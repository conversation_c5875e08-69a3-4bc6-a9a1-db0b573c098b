import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/models/apis/account.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_records/interest_records_model.dart';
import 'package:gp_stock_app/features/contract/screens/interest_records/interset_records_cell.dart';
import 'package:gp_stock_app/features/contract/screens/interest_records/interset_records_filter_sheet.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class InterestRecordsScreen extends StatefulWidget {
  const InterestRecordsScreen({super.key});

  @override
  State<InterestRecordsScreen> createState() => _InterestRecordsScreenState();
}

class _InterestRecordsScreenState extends State<InterestRecordsScreen> {
  DataStatus dataStatus = DataStatus.loading;
  InterestRecordsModel model = InterestRecordsModel();
  int? fromType;
  final RefreshController _refreshController = RefreshController();

  @override
  void initState() {
    super.initState();
    Helper.afterInit(() {
      _loadData(isFirst: true);
    });
  }

  void _loadData({bool isFirst = false}) {
    if (isFirst) {
      setState(() {
        dataStatus = DataStatus.loading;
      });
    }

    AccountApi.fetchInterestRecordList(pageNumber: 1, fromType: fromType).then((result) {
      _refreshController.refreshCompleted();

      if (result != null) {
        setState(() {
          model = result;
          dataStatus = DataStatus.success;
        });

        if (result.hasNext) {
          _refreshController.resetNoData();
        } else {
          _refreshController.loadNoData();
        }
      } else {
        setState(() {
          dataStatus = DataStatus.failed;
        });
        if (kDebugMode) {
          Helper.showFlutterToast(
            'Failed to fetch network error',
          );
        }
      }
    }).catchError((e) {
      _refreshController.refreshCompleted();
      setState(() {
        dataStatus = DataStatus.failed;
      });
      if (kDebugMode) {
        Helper.showFlutterToast(
          'Failed to fetch network error',
        );
      }
    });
  }

  void _onLoadmore() async {
    AccountApi.fetchInterestRecordList(pageNumber: model.current + 1, fromType: fromType).then((result) {
      _refreshController.loadComplete();
      if (result != null) {
        setState(() {
          model = result.copyWith(records: model.records + result.records);
        });
        if (result.hasNext) {
          _refreshController.resetNoData();
        } else {
          _refreshController.loadNoData();
        }
      } else {
        if (kDebugMode) {
          Helper.showFlutterToast(
            'Failed to fetch network error',
          );
        }
      }
    }).catchError((e) {
      _refreshController.loadComplete();
      if (kDebugMode) {
        Helper.showFlutterToast(
          'Failed to fetch network error',
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        surfaceTintColor: Colors.transparent,
        title: Text('interestRecords'.tr()),
        actions: [
          IconButton(
            onPressed: () {
              showModalBottomSheet(
                context: context,
                backgroundColor: context.theme.cardColor,
                isScrollControlled: true,
                showDragHandle: true,
                builder: (_) => IntersetRecordsFilterSheet(
                  selectedFromType: fromType,
                  onTapFromType: (idx) {
                    setState(() {
                      fromType = idx;
                    });
                    _loadData();
                  },
                ),
              );
            },
            icon: Icon(LucideIcons.filter),
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(10.gr),
        child: _buildTableContent(),
      ),
    );
  }

  Widget _buildTableContent() {
    if (dataStatus == DataStatus.loading) {
      return _buildLoadingList();
    }

    if (model.records.isEmpty) {
      return TableEmptyWidget();
    }

    return CommonRefresher(
      controller: _refreshController,
      enablePullDown: true,
      enablePullUp: true,
      onRefresh: _loadData,
      onLoading: _onLoadmore,
      bgColor: Colors.transparent,
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: model.records.length,
        itemBuilder: (context, index) => IntersetRecordsCell(record: model.records[index]),
      ),
    );
  }

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        6,
        (_) => Padding(
          padding: EdgeInsets.only(bottom: 10.gw),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.gr),
            child: ShimmerWidget(
              height: 80.gw,
              width: double.infinity,
            ),
          ),
        ),
      ),
    );
  }
}
