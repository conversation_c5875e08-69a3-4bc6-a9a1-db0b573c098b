import 'dart:math' show max, min;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/market/index_stock_card_model.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/trade_navigation_helper.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';


/// 首页指数卡片组件（带背景图，YHXT样式）
class HomeIndexCardsYhxt extends StatefulWidget {
  final bool isFromHome;

  const HomeIndexCardsYhxt({super.key, this.isFromHome = false});

  @override
  State<HomeIndexCardsYhxt> createState() => _HomeIndexCardsYhxtState();
}

class _HomeIndexCardsYhxtState extends State<HomeIndexCardsYhxt> with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  int _firstVisibleItemIndex = 0;
  static const double _cardHeight = 84;
  final double _itemWidth = 0.32.gsw - 4.gw; // Exactly 1/3 of screen width
  final double _itemSpacing = 8.gw;

  // Variables for snapping behavior
  bool _isScrolling = false;
  int _targetPage = 0;
  final int _itemsPerPage = 3; // We want to show 3 cards per page
  late AnimationController _animationController;
  late Animation<double> _animation;
  final PageController _pageController = PageController(
    viewportFraction: 0.33,
    initialPage: 0,
  );

  // Calculate the total width of a single item including spacing
  double get _totalItemWidth => _itemWidth + _itemSpacing;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller for smooth scrolling
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Add listeners for scroll events
    _scrollController.addListener(_onScroll);

    // Add listener for when scrolling ends to snap to the nearest page
    _scrollController.addListener(_onScrollEnd);

    // Add listener for when page changes
    _pageController.addListener(_onPageChanged);

    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        context.read<IndexTradeCubit>().updateAnimate();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.removeListener(_onScrollEnd);
    _scrollController.dispose();
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onPageChanged() {
    // Sync the PageController with our ScrollController
    if (_scrollController.hasClients && _scrollController.position.maxScrollExtent > 0) {
      _scrollController.position.notifyListeners();
    }
  }

  // This method handles the scrolling logic and updates the current visible index
  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final state = context.read<IndexTradeCubit>().state;
    final itemCount = state.indexStocks.length;
    if (itemCount == 0) return;

    final viewportWidth = _scrollController.position.viewportDimension;
    final scrollOffset = _scrollController.offset;

    // Calculate the current page based on scroll offset

    // Calculate the first visible item index
    final viewportCenter = scrollOffset + (viewportWidth / 2);
    final calculatedIndex = (viewportCenter / _totalItemWidth).floor();
    final safeIndex = max(0, min(calculatedIndex, itemCount - 1));

    if (safeIndex != _firstVisibleItemIndex) {
      setState(() => _firstVisibleItemIndex = safeIndex);
    }
  }

  // This method detects when scrolling ends and snaps to the nearest page
  void _onScrollEnd() {
    if (!_scrollController.hasClients) return;

    // Check if the scroll is idle (not being dragged)
    if (!_scrollController.position.isScrollingNotifier.value) {
      if (_isScrolling) {
        _isScrolling = false;
        _snapToPage();
      }
    } else {
      _isScrolling = true;
    }
  }

  // Snap to the nearest page when scrolling ends
  void _snapToPage() {
    if (!_scrollController.hasClients) return;

    final viewportWidth = _scrollController.position.viewportDimension;
    final scrollOffset = _scrollController.offset;

    // Calculate the target page based on current scroll position
    final itemCount = context.read<IndexTradeCubit>().state.indexStocks.length;
    final maxPages = (itemCount / _itemsPerPage).ceil();

    // Calculate which page we're closest to
    final page = (scrollOffset / viewportWidth).round();
    _targetPage = max(0, min(page, maxPages - 1));

    // Calculate the target offset for the page
    final targetOffset = _targetPage * viewportWidth;

    // Only animate if we're not already at the target
    if ((targetOffset - scrollOffset).abs() > 0.5) {
      // Set up the animation
      _animation = Tween<double>(
        begin: scrollOffset,
        end: targetOffset,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ));

      // Add listener to update scroll position during animation
      _animation.addListener(() {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(_animation.value);
        }
      });

      // Reset and start the animation
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<IndexTradeCubit, IndexTradeState>(
      listenWhen: (previous, current) => previous.animate != current.animate,
      listener: (context, state) {
        if (widget.isFromHome) return;
        if (state.animate) {
          _pageController.animateToPage(
            state.selectedIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          context.read<IndexTradeCubit>().updateAnimate(animate: false);
          setState(() {
            _firstVisibleItemIndex = state.selectedIndex;
          });
        }
      },
      child: BlocBuilder<IndexTradeCubit, IndexTradeState>(
        bloc: getIt<IndexTradeCubit>(),
        builder: (context, state) {
          int itemCount = state.indexStocks.length;
          if (state.status == DataStatus.loading) {
            return _buildLoading();
          }

          if (state.status.isFailed || itemCount == 0) {
            return const Center(child: TableEmptyWidget());
          }

          return SizedBox(
            height: _cardHeight.gw,
            child: PageView.builder(
              key: PageStorageKey('visual_graph//${widget.isFromHome}'),
              controller: _pageController,
              itemCount: itemCount,
              onPageChanged: (index) {
                setState(() {
                  _firstVisibleItemIndex = index;
                });
              },
              pageSnapping: true,
              padEnds: false,
              // physics: const CustomPageViewScrollPhysics(),
              itemBuilder: (context, i) => Padding(
                padding: EdgeInsets.only(right: _itemSpacing),
                child: _buildStockCard(context, state, i),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStockCard(BuildContext context, IndexTradeState state, int i) {
    final data = state.indexStocks;
    final isSelected = i == state.selectedIndex;
    final stockData = IndexStockCardModel.fromStockInfoWithBgImage(context, data[i]);

    return GestureDetector(
      onTap: () => _handleCardTap(data, i),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        width: _itemWidth,
        padding: EdgeInsets.symmetric(horizontal: 4.gw),
        margin: _calculateCardMargin(isSelected, i),
        decoration: BoxDecoration(
            image: stockData.bgImgPath != null
                ? DecorationImage(
                    image: AssetImage(stockData.bgImgPath!),
                    fit: BoxFit.fill,
                    alignment: Alignment.topCenter,
                  )
                : null),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 8.gw),
            // Stock name
            Text(
              stockData.name,
              style: TextStyle(color: Colors.black),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 5.gw),
            // Price
            FlipText(
              stockData.price,
              style: context.textTheme.primary.w500.copyWith(
                color: stockData.textColor,
              ),
              fractionDigits: 3,
            ),
            SizedBox(height: 6.gw),
            // Change and percentage
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: 4,
              children: [
                // Price change
                FlipText(
                  stockData.change,
                  prefix: stockData.change >= 0 ? '+' : '',
                  defaultFontFamily: false,
                  style: context.textTheme.primary.fs11.copyWith(
                    color: stockData.textColor,
                  ),
                ),
                // Percentage change
                FlipText(
                  stockData.gainPercentage,
                  suffix: '%',
                  defaultFontFamily: false,
                  style: context.textTheme.primary.fs11.w500.copyWith(
                    color: stockData.textColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Calculate card margin based on selection state and position
  EdgeInsets _calculateCardMargin(bool isSelected, int index) {
    return EdgeInsets.only(
      right: isSelected && !widget.isFromHome ? 0.gw : 1.gw,
      left: 0,
      bottom: isSelected && !widget.isFromHome ? 0 : 2.gw,
      top: isSelected && !widget.isFromHome ? 0 : 2.gw,
    );
  }

  void _handleCardTap(List<IndexStockInfo> data, int i) {
    if (widget.isFromHome) {
      AuthUtils.verifyAuth(() {
        final stockInfo = data[i].stockInfo;
        TradeNavigationHelper.goToTradingCenterV2(
          context,
          security: stockInfo.instrumentInfo,
          tabType: TradeTabType.Quotes,
          isIndexTradingOverride: true,
        );
      });
    }
    context.read<IndexTradeCubit>().updateSelectedIndex(i);
  }

  Widget _buildLoading() {
    final double itemWidth = 0.32.gsw - 4.gw;
    return SizedBox(
      height: 84.gw,
      child: ListView.separated(
        itemCount: 3,
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 6),
        scrollDirection: Axis.horizontal,
        separatorBuilder: (_, __) => const SizedBox(width: 8),
        itemBuilder: (_, __) => ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: ShimmerWidget(
            height: 84.gw,
            width: itemWidth,
          ),
        ),
      ),
    );
  }
}
