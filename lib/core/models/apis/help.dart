import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/system/help_category_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

class HelpService {
  // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/App%E5%B8%AE%E5%8A%A9%E4%B8%AD%E5%BF%83/getQuestionsUsingGET_1
  static Future<List<HelpCategoryEntity>> getHelpList() async {
    final res = await Http().request<HelpCategoryEntityList>(
      ApiEndpoints.helpList,
      method: HttpMethod.get,
    );
    return res.data?.list ?? [];
  }

  // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/App%E5%B8%AE%E5%8A%A9%E4%B8%AD%E5%BF%83/getQuestionDetailUsingGET_1
  static Future<HelpQuestion?> getHelpQuestionDetail(String id) async {
    final res = await Http().request<HelpQuestion>(
      ApiEndpoints.helpQuestionDetail,
      method: HttpMethod.get,
      queryParameters: {'id': id},
    );
    return res.data;
  }

  // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E9%97%AE%E9%A2%98%E5%8F%8D%E9%A6%88/solvedUsingPOST_1
  static Future<String?> markQuestionSolved(String questionId) async {
    final res = await Http().request<String?>(
      ApiEndpoints.questionFeedbackSolved,
      method: HttpMethod.post,
      params: {'questionId': questionId},
    );
    return res.data;
  }

  // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E9%97%AE%E9%A2%98%E5%8F%8D%E9%A6%88/unsolvedUsingPOST_1
  static Future<String?> markQuestionUnsolved(String questionId, String content) async {
    final res = await Http().request<String?>(
      ApiEndpoints.questionFeedbackUnsolved,
      method: HttpMethod.post,
      params: {
        'questionId': questionId,
        'content': content,
      },
    );
    return res.data;
  }

  // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/App%E5%B8%AE%E5%8A%A9%E4%B8%AD%E5%BF%83/getQuestionListUsingGET_1
  static Future<List<HelpQuestion>> searchHelpQuestions(String keyword) async {
    final res = await Http().request<HelpQuestionList>(
      ApiEndpoints.helpQuestionSearch,
      method: HttpMethod.get,
      queryParameters: {'keyword': keyword},
    );
    return res.data?.list ?? [];
  }
}
