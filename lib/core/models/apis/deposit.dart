import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_channel.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

/// 存款
class DepositApi {
  /// 获取所有USDT充值渠道
  static Future<List<USDTDepositChannel>> fetchUsdtList() async {
    final res = await Http().request<USDTDepositChannelListEntity>(
      ApiEndpoints.getDepositUsdtList,
      method: HttpMethod.get,
    );
    return res.data?.list ?? [];
  }

  /// 提交USDT存款申请
  static Future<UsdtRechargeOrder?> applyUsdtDepost({
    required int id,
    required double amount,
  }) async {
    final res = await Http().request<UsdtRechargeOrder>(ApiEndpoints.applyUsdtDeposit, params: {
      'id': id,
      'rechargeAmount': amount,
    });
    return res.data;
  }

  /// 确认USDT存款已支付
  static Future<bool> confirmUsdtPaid({required int id}) async {
    final res = await Http().request(
      "${ApiEndpoints.confirmUsdtPaid}/$id",
      method: HttpMethod.put,
    );
    return res.isSuccess;
  }
}
