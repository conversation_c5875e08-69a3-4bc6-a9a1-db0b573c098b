import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../home_section_style.dart';
import 'home_news_cell.dart';

/// 首页新闻区域（通用样式）
class HomeNewsSection extends StatelessWidget {
  final NewsRecordsList? newsData;
  final DataStatus status;
  final HomeSectionStyle style;

  const HomeNewsSection({
    super.key,
    required this.newsData,
    required this.status,
    this.style = const HomeSectionStyle(),
  });

  @override
  Widget build(BuildContext context) {
    if (AppConfig.instance.flavor == Flavor.yhxt) {
      return _buildYhxtLayout(context);
    }

    return ShadowBox(
      borderRadius: style.borderRadius,
      margin: style.margin,
      child: Column(
        children: [
          Row(
            children: [
              _buildHeader(context),
            ],
          ),
          10.verticalSpace,
          _buildNewsList(context),
          10.verticalSpace,
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return MarketTableHeaderStyle(
      title: tr("todayNews", context: context),
      isSelected: true,
      onTap: () {},
      padding: switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kGP => EdgeInsets.symmetric(horizontal: 16.gw, vertical: 5.gw),
        _ => null,
      },
      radius: switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kGP => 8.gr,
        _ => null,
      },
    );
  }

  Widget _buildNewsList(BuildContext context, {bool isYhxt = false}) {
    if (status == DataStatus.loading) {
      return isYhxt ? _buildYhxtShimmer(context) : const NewsListShimmer();
    }

    if (status == DataStatus.failed) {
      return Center(child: TableEmptyWidget(height: 50.gw, width: 50.gw));
    }

    final newsList = newsData?.records ?? [];
    if (newsList.isEmpty) {
      return const Center(child: Text('No news available'));
    }

    return AnimationLimiter(
      child: ListView.separated(
        padding: EdgeInsets.zero,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: newsList.length,
        shrinkWrap: true,
        separatorBuilder: (_, __) => Divider(
          height: 24.gw,
          thickness: 1,
          color: context.theme.primaryColor.withValues(alpha: 0.05),
        ),
        itemBuilder: (context, index) {
          final item = newsList[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 600),
            child: SlideAnimation(
              verticalOffset: 30.0,
              child: FadeInAnimation(
                child: Bounceable(
                  onTap: () => _navigateToDetail(item),
                  child: HomeNewsCell(news: item),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildYhxtLayout(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.gw),
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Stack(
        children: [
          Positioned.fill(
            child: Container(color: Colors.white),
          ),
          Container(
            width: 195.gw,
            height: 195.gw,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFFd3e8fd),
                  Colors.white,
                ],
                stops: [0, 0.25],
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(16.gw, 11.gw, 12.gw, 26.gw),
            child: Column(
              children: [
                _buildYhxtHeader(context),
                SizedBox(height: 10.gw),
                _buildNewsList(context, isYhxt: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYhxtHeader(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Image.asset(
          "assets/images/icon_home_news.png",
          width: 24.gw,
          height: 28.gw,
        ),
        SizedBox(width: 15.gw),
        Text(
          "todayNews".tr(),
          style: TextStyle(color: context.colorTheme.tabActive).fs16.w500,
        ),
      ],
    );
  }

  Widget _buildYhxtShimmer(BuildContext context) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: 5,
      separatorBuilder: (_, __) => Divider(
        height: 24.gw,
        thickness: 1,
        color: context.theme.primaryColor.withValues(alpha: 0.05),
      ),
      itemBuilder: (context, index) => ShimmerWidget(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 8.gw),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      height: 16.gw,
                      color: context.theme.cardColor,
                      margin: EdgeInsets.only(bottom: 8.gw),
                    ),
                    Container(
                      width: 0.7.gsw,
                      height: 16.gw,
                      color: context.theme.cardColor,
                      margin: EdgeInsets.only(bottom: 8.gw),
                    ),
                    Container(
                      width: 100.gw,
                      height: 12.gw,
                      color: context.theme.cardColor,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.gw),
              Container(
                width: 120.gw,
                height: 80.gw,
                decoration: BoxDecoration(
                  color: context.theme.cardColor,
                  borderRadius: BorderRadius.circular(8.gr),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToDetail(NewsRecords news) {
    getIt<NavigatorService>().push(
      AppRouter.routeNewsDetails,
      arguments: news,
    );
  }
}

/// 新闻列表 Shimmer 加载状态
class NewsListShimmer extends StatelessWidget {
  final int itemCount;

  const NewsListShimmer({
    super.key,
    this.itemCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: itemCount,
      separatorBuilder: (_, __) => Divider(
        height: 24.gw,
        thickness: 1,
        color: context.theme.primaryColor.withValues(alpha: 0.05),
      ),
      itemBuilder: (context, index) => const _NewsListItemShimmer(),
    );
  }
}

class _NewsListItemShimmer extends StatelessWidget {
  const _NewsListItemShimmer();

  @override
  Widget build(BuildContext context) {
    final imageWidget = Container(
      width: 120.gw,
      height: 80.gw,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
      ),
    );
    final textWidget = Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            height: 16.gw,
            color: context.theme.cardColor,
            margin: EdgeInsets.only(bottom: 8.gw),
          ),
          Container(
            width: 0.7.gsw,
            height: 16.gw,
            color: context.theme.cardColor,
            margin: EdgeInsets.only(bottom: 8.gw),
          ),
          Container(
            width: 100.gw,
            height: 12.gw,
            color: context.theme.cardColor,
          ),
        ],
      ),
    );
    return ShimmerWidget(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.gw),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: switch (AppConfig.instance.skinStyle) {
            AppSkinStyle.kTemplateC => [
                textWidget,
                16.horizontalSpace,
                imageWidget,
              ],
            _ => [
                imageWidget,
                16.horizontalSpace,
                textWidget,
              ],
          },
        ),
      ),
    );
  }
}

/// 通用 Tab 标题样式组件
class MarketTableHeaderStyle extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;
  final EdgeInsets? padding;
  final Color? activeColor;
  final TextStyle? activeTextStyle;
  final Color? inactiveColor;
  final TextStyle? inactiveTextStyle;
  final double? radius;

  const MarketTableHeaderStyle({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
    this.padding,
    this.activeColor,
    this.activeTextStyle,
    this.inactiveColor,
    this.inactiveTextStyle,
    this.radius,
  });

  @override
  Widget build(BuildContext context) {
    TextStyle textStyle = context.textTheme.regular.copyWith(
      color: isSelected ? Colors.white : context.colorTheme.textRegular,
    );
    if (activeTextStyle != null && isSelected) {
      textStyle = activeTextStyle!;
    }
    if (inactiveTextStyle != null && !isSelected) {
      textStyle = inactiveTextStyle!;
    }
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? (activeColor ?? context.theme.primaryColor) : (inactiveColor ?? Colors.transparent),
          borderRadius: BorderRadius.circular(radius ?? 4),
        ),
        child: Text(
          title,
          style: textStyle,
        ),
      ),
    );
  }
}
