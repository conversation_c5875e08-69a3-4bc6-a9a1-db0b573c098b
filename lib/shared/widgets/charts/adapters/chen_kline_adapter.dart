import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chen_kchart/k_chart.dart' as chen;
import 'package:gp_stock_app/core/models/entities/market/stock_kline_entity.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'kline_adapter.dart';
import '../models/kline_config.dart';
import '../models/kline_data.dart';
import '../utils/kline_helpers.dart';

class ChenKlineAdapter implements KlineAdapter {
  @override
  Widget buildChart({
    required BuildContext context,
    required List<StockKlineItem> rawData,
    required KlineConfig config,
    dynamic controller,
  }) {
    // Convert raw data to chart entities
    final chartData = _convertToChartEntities(rawData, config);

    if (chartData.isEmpty) {
      return _buildEmptyChart(context, config);
    }

    // Create chart style and colors
    final chartStyle = _createChartStyle(context, config, chartData);
    final chartColors = _createChartColors(context);

    // Get configuration
    final timeFormat = _getTimeFormat(config);
    final mainState = _mapMainIndicator(config.mainIndicator);
    final secondaryState = _mapSecondaryIndicator(config.secondaryIndicator);

    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
      child: SizedBox(
        height: config.chartHeight ?? 0.30.gsh,
        child: chen.KChartWidget(
          chartData,
          chartStyle: chartStyle,
          chartColors: chartColors,
          controller: controller as chen.KChartController?,
          isTrendLine: config.enableTrendLine,
          mainState: mainState,
          secondaryState: secondaryState,
          volHidden: !config.showVolume,
          isTapShowInfoDialog: config.enableTapToShowInfo,
          timeFormat: timeFormat,
          verticalTextAlignment: _mapVerticalAlignment(config.verticalTextAlignment),
          isLine: config.isLine,
          xFrontPadding: config.xFrontPadding,
          maDayList: config.maDayList,
          enableTheme: true,
          showNowPrice: config.showNowPrice,
          showInfoDialog: config.showInfoDialog,
          materialInfoDialog: true,
          fixedLength: config.fixedLength,
          enablePinchZoom: config.enablePinchZoom,
          enableScrollZoom: config.enableScrollZoom,
          scaleSensitivity: config.scaleSensitivity,
          minScale: config.minScale,
          maxScale: config.maxScale,
          enableScaleAnimation: config.enableScaleAnimation,
          enablePerformanceMode: config.enablePerformanceMode,
          onLoadMore: config.onLoadMore,
          onScaleChanged: config.onScaleChanged,
          isIndexTrading: config.isIndexTrading,
        ),
      ),
    );
  }

  @override
  dynamic createController() {
    return chen.KChartController();
  }

  @override
  void dispose() {
    // Chen KChart controller doesn't require explicit disposal
  }

  /// Convert raw data to chen.KLineEntity
  List<chen.KLineEntity> _convertToChartEntities(List<StockKlineItem> rawData, KlineConfig config) {
    final entities = <chen.KLineEntity>[];

    if (rawData.isEmpty) return entities;

    double? previousClose;

    for (int i = 0; i < rawData.length; i++) {
      final item = rawData[i];

      // Extract data from item (works with KlineItem)
      final time = _getTime(item);
      final price = _getPrice(item);
      final open = _getOpen(item);
      final close = _getClose(item);
      final high = _getHigh(item);
      final low = _getLow(item);
      final volume = _getVolume(item);

      // Skip invalid items
      if (time == null || time <= 0) continue;

      // Skip empty/invalid candlestick data
      if (!config.isLine && (open == 0 && close == 0 && high == 0 && low == 0 && volume == 0 && price == 0)) {
        continue;
      }

      // For line charts, skip if price is null or zero
      if (config.isLine && price <= 0) {
        continue;
      }

      double openPrice;
      double closePrice;
      double highPrice;
      double lowPrice;

      if (config.isLine) {
        openPrice = price;
        closePrice = price;
        highPrice = price;
        lowPrice = price;
      } else {
        if (open > 0) {
          openPrice = open;
          closePrice = close > 0 ? close : price;
          highPrice = high > 0 ? high : price;
          lowPrice = low > 0 ? low : price;
        } else if (price > 0) {
          openPrice = previousClose ?? price;
          closePrice = price;
          highPrice = price;
          lowPrice = price;
        } else {
          openPrice = previousClose ?? close;
          closePrice = close;
          highPrice = high > 0 ? high : close;
          lowPrice = low > 0 ? low : close;
        }
      }

      // API returns UTC timestamps, adjust to market time
      final marketOffset = 8; // Shanghai UTC+8
      final adjustedTime = (time + (marketOffset * 3600)) * 1000;

      final chenEntity = chen.KLineEntity.fromCustom(
        time: adjustedTime,
        close: closePrice,
        open: openPrice,
        high: highPrice,
        low: lowPrice,
        vol: volume,
        amount: price,
      );

      // Calculate change and ratio
      if (config.isLine) {
        if (config.referencePrice != null && config.referencePrice! != 0) {
          chenEntity.change = closePrice - config.referencePrice!;
          chenEntity.ratio = (chenEntity.change! / config.referencePrice!) * 100;
        }
      } else {
        if (previousClose != null && previousClose != 0) {
          chenEntity.change = closePrice - previousClose;
          chenEntity.ratio = (chenEntity.change! / previousClose) * 100;
        }
      }

      entities.add(chenEntity);
      previousClose = closePrice;
    }

    // Calculate technical indicators
    if (entities.isNotEmpty) {
      try {
        chen.DataUtil.calculate(entities, config.maDayList);
      } catch (e) {
        debugPrint('Error calculating technical indicators: $e');
      }
    }

    return entities;
  }

  // Helper methods to extract data from different item types
  int? _getTime(StockKlineItem item) {
    return item.time;
  }

  double _getPrice(StockKlineItem item) {
    return item.price;
  }

  double _getOpen(StockKlineItem item) {
    return item.open;
  }

  double _getClose(StockKlineItem item) {
    return item.close;
  }

  double _getHigh(StockKlineItem item) {
     return item.high;
  }

  double _getLow(StockKlineItem item) {
    return item.low;
  }

  double _getVolume(StockKlineItem item) {
    return item.volume;
  }

  /// Create ChartColors matching app theme
  chen.ChartColors _createChartColors(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorTheme = context.colorTheme;

    chen.ChartThemeManager.setTheme(isDark ? chen.ChartTheme.dark : chen.ChartTheme.light);

    final chartColors = chen.ChartColors();

    chartColors.upColor = const Color(0xFFD2544F);
    chartColors.dnColor = const Color(0xFF5DAF78);
    chartColors.kLineColor = const Color(0xFFD2544F);
    chartColors.gridColor = Colors.transparent;
    chartColors.lineFillColor = const Color(0xFF005af1).withValues(alpha: 0.1);
    chartColors.lineFillInsideColor = const Color(0xFF005af1).withValues(alpha: 0.1);
    chartColors.bgColor = [context.theme.cardColor, context.theme.cardColor];

    chartColors.ma5Color = const Color(0xffE5B767);
    chartColors.ma10Color = const Color(0xff1FD1AC);
    chartColors.ma30Color = const Color(0xffB48CE3);

    chartColors.volColor = const Color(0xff4729AE);
    chartColors.macdColor = const Color(0xff4729AE);
    chartColors.difColor = const Color(0xffC9B885);
    chartColors.deaColor = const Color(0xff6CB0A6);

    chartColors.kColor = const Color(0xffC9B885);
    chartColors.dColor = const Color(0xff6CB0A6);
    chartColors.jColor = const Color(0xff9979C6);
    chartColors.rsiColor = const Color(0xffC9B885);

    chartColors.defaultTextColor = colorTheme.textPrimary;

    chartColors.nowPriceUpColor = const Color(0xff4DAA90);
    chartColors.nowPriceDnColor = const Color(0xffC15466);
    chartColors.nowPriceTextColor = Colors.white;

    chartColors.depthBuyColor = const Color(0xff60A893);
    chartColors.depthSellColor = const Color(0xffC15866);

    chartColors.selectBorderColor = const Color(0xff6C7A86);
    chartColors.selectFillColor = isDark ? const Color(0xff0D1722) : const Color(0xffF5F5F5);

    chartColors.infoWindowNormalColor = colorTheme.textPrimary;
    chartColors.infoWindowTitleColor = colorTheme.textPrimary;
    chartColors.infoWindowUpColor = const Color(0xff00ff00);
    chartColors.infoWindowDnColor = const Color(0xffff0000);

    chartColors.hCrossColor = isDark ? Colors.white : Colors.black;
    chartColors.vCrossColor = isDark ? const Color(0x1Effffff) : const Color(0x1E000000);
    chartColors.crossTextColor = isDark ? Colors.white : Colors.black;

    chartColors.maxColor = colorTheme.textPrimary;
    chartColors.minColor = colorTheme.textPrimary;

    return chartColors;
  }

  /// Create ChartStyle based on configuration
  chen.ChartStyle _createChartStyle(
    BuildContext context,
    KlineConfig config,
    List<chen.KLineEntity> chartData,
  ) {
    final mediaQuery = MediaQuery.of(context);
    final width = config.chartWidth ?? mediaQuery.size.width;
    final height = config.chartHeight ?? mediaQuery.size.height;

    var chartStyle = chen.ChartStyle();

    // Responsive sizing
    final isTablet = width > 600;
    final isLargeScreen = height > 800;

    // Use explicit point/candle width from config if provided
    if (config.pointWidth != null && config.candleWidth != null) {
      chartStyle.pointWidth = config.pointWidth!;
      chartStyle.candleWidth = config.candleWidth!;
    }
    // Calculate point width based on expected data points
    else if (config.expectedDataPoints != null && config.expectedDataPoints! > 0) {
      final availableWidth = width;
      final calculatedPointWidth = availableWidth / config.expectedDataPoints!;
      final calculatedCandleWidth = calculatedPointWidth * 0.75;

      chartStyle.pointWidth = calculatedPointWidth.clamp(0.2, 15.0);
      chartStyle.candleWidth = calculatedCandleWidth.clamp(0.2, 12.0);
    } else {
      if (config.isIntradayType) {
        chartStyle.pointWidth = isTablet ? 8.0 : 6.0;
        chartStyle.candleWidth = isTablet ? 6.0 : 4.0;
      } else {
        chartStyle.pointWidth = isTablet ? 11.0 : 9.0;
        chartStyle.candleWidth = isTablet ? 8.5 : 6.5;
      }
    }

    chartStyle.candleLineWidth = 1.5;
    chartStyle.volWidth = chartStyle.candleWidth;
    chartStyle.macdWidth = 3.0;
    chartStyle.vCrossWidth = chartStyle.candleWidth;
    chartStyle.hCrossWidth = 0.5;

    chartStyle.topPadding = isLargeScreen ? 30.0 : 20.0;
    chartStyle.bottomPadding = isLargeScreen ? 20.0 : 15.0;
    chartStyle.childPadding = isLargeScreen ? 12.0 : 8.0;

    chartStyle.gridRows = 4;
    chartStyle.gridColumns = isTablet ? 6 : 4;

    chartStyle.nowPriceLineLength = 1;
    chartStyle.nowPriceLineSpan = 1;
    chartStyle.nowPriceLineWidth = 1;

    // Set custom intraday labels
    if (config.customIntradayLabels != null) {
      chartStyle.customIntradayLabels = config.customIntradayLabels;
    }
    if (config.marketType != null) {
      chartStyle.marketType = config.marketType!.code;
    }

    // Configure percentage labels
    final shouldShowPercentage = config.isIntradayType;
    chartStyle = chartStyle.copyWith(
      showPercentageLabels: shouldShowPercentage,
      referencePrice: config.referencePrice,
    );

    final locale = context.locale.languageCode;
    final chartPeriod = KlineHelpers.periodToDateFormatString(config.period);

    if (config.period == ChartPeriod.fiveDay && chartData.isNotEmpty) {
      final dateLabels = _extract5DayDateLabels(chartData, locale);
      final labelPositions = _calculate5DayLabelPositions(chartData, locale, width);

      chartStyle = chartStyle.copyWith(
        custom5DayDateLabels: dateLabels,
        custom5DayLabelPositions: labelPositions,
        is5DayChart: true,
        marketType: config.marketType?.code ?? 'CN',
      );
    }

    chartStyle = chartStyle.copyWith(
      locale: locale,
      chartPeriod: chartPeriod,
    );

    return chartStyle;
  }

  /// Get time format based on period
  List<String> _getTimeFormat(KlineConfig config) {
    if (config.customTimeFormat != null) {
      return config.customTimeFormat!;
    }

    if (config.isIntradayType || config.isLine) {
      return [chen.HH, ':', chen.nn];
    } else {
      return chen.TimeFormat.YEAR_MONTH_DAY;
    }
  }

  /// Map main indicator to chen main state
  chen.MainState _mapMainIndicator(MainIndicator indicator) {
    return switch (indicator) {
      MainIndicator.ma => chen.MainState.MA,
      MainIndicator.boll => chen.MainState.BOLL,
      MainIndicator.none => chen.MainState.NONE,
    };
  }

  /// Map secondary indicator to chen secondary state
  chen.SecondaryState _mapSecondaryIndicator(SecondaryIndicator indicator) {
    return switch (indicator) {
      SecondaryIndicator.macd => chen.SecondaryState.MACD,
      SecondaryIndicator.kdj => chen.SecondaryState.KDJ,
      SecondaryIndicator.rsi => chen.SecondaryState.RSI,
      SecondaryIndicator.wr => chen.SecondaryState.WR,
      SecondaryIndicator.cci => chen.SecondaryState.CCI,
      SecondaryIndicator.none => chen.SecondaryState.NONE,
    };
  }

  /// Map vertical alignment
  chen.VerticalTextAlignment _mapVerticalAlignment(VerticalTextAlignment alignment) {
    return switch (alignment) {
      VerticalTextAlignment.left => chen.VerticalTextAlignment.left,
      VerticalTextAlignment.right => chen.VerticalTextAlignment.right,
    };
  }

  String _format5DayDateLabel(DateTime date, String locale) {
    final isChinese = locale.startsWith('zh');

    if (isChinese) {
      // "10/14" (MM/DD)
      return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
    } else {
      // "Oct 14" (MMM DD)
      return '${_getMonthShort(date.month)} ${date.day}';
    }
  }

  String _getMonthShort(int month) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[month - 1];
  }

  /// Extract 5-day date labels from chart data
  List<String> _extract5DayDateLabels(List<chen.KLineEntity> data, String locale) {
    final dateSet = <String>{};

    for (final entity in data) {
      if (entity.time != null) {
        final date = DateTime.fromMillisecondsSinceEpoch(entity.time!, isUtc: true);
        final dateStr = _format5DayDateLabel(date, locale);
        dateSet.add(dateStr);
      }
    }

    final sortedDates = dateSet.toList()..sort();
    return sortedDates;
  }

  /// Calculate 5-day label positions
  List<double> _calculate5DayLabelPositions(List<chen.KLineEntity> data, String locale, double chartWidth) {
    final datePositions = <String, List<int>>{};

    for (int i = 0; i < data.length; i++) {
      final entity = data[i];
      if (entity.time != null) {
        final date = DateTime.fromMillisecondsSinceEpoch(entity.time!, isUtc: true);
        final dateStr = _format5DayDateLabel(date, locale);

        datePositions.putIfAbsent(dateStr, () => []);
        datePositions[dateStr]!.add(i);
      }
    }

    final sortedDates = datePositions.keys.toList()..sort();
    final positions = <double>[];
    final totalDataPoints = data.length;

    for (final date in sortedDates) {
      final dayIndices = datePositions[date] ?? [];
      if (dayIndices.isNotEmpty) {
        final dayStartIndex = dayIndices.first;
        final dayEndIndex = dayIndices.last;
        final dayCenterIndex = (dayStartIndex + dayEndIndex) / 2;
        final relativePosition = dayCenterIndex / totalDataPoints;
        final chartPosition = relativePosition * chartWidth;
        positions.add(chartPosition);
      }
    }

    return positions;
  }

  /// Build empty chart placeholder
  Widget _buildEmptyChart(BuildContext context, KlineConfig config) {
    return SizedBox(
      height: config.chartHeight ?? 0.30.gsh,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.timeline, color: Colors.grey.withValues(alpha: 0.5), size: 32),
            const SizedBox(height: 8),
            Text(
              'chart_failed_to_load'.tr(),
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            // Reload button
            if (config.onReload != null) ...[
              const SizedBox(height: 16),
              InkWell(
                onTap: config.onReload,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh, color: Colors.grey, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'reload'.tr(),
                        style: TextStyle(fontSize: 11, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Controller wrapper for Chen KChart
class ChenKlineControllerWrapper implements KlineController {
  final chen.KChartController _controller;

  ChenKlineControllerWrapper(this._controller);

  @override
  Future<void> scaleTo(double scale) => _controller.scaleTo(scale);

  @override
  Future<void> zoomIn() => _controller.zoomIn();

  @override
  Future<void> zoomOut() => _controller.zoomOut();

  @override
  Future<void> resetZoom() => _controller.resetScale();

  @override
  double get currentScale => _controller.currentScale;

  @override
  void saveState() => _controller.saveScaleState();

  @override
  Future<void> restoreState() => _controller.restoreScaleState();

  @override
  void dispose() {
    // Chen controller doesn't need explicit disposal
  }
}
