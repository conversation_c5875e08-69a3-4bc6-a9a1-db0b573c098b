import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/core/models/entities/wallet/bank_entity_list.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'bank_list_state.freezed.dart';

@freezed
class BankListState with _$BankListState {
  const factory BankListState({
    @Default(DataStatus.idle) DataStatus bankListFetchStatus,
    String? error,
    List<BankEntity>? bankList,
  }) = _BankListState;
}
