import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/utils/system_util.dart';
import 'package:gp_stock_app/features/home_v2/home/<USER>/temp_b_home_page.dart';
import 'package:gp_stock_app/features/home_v2/home/<USER>/marquee/notification_popup.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/mixins/locale_aware_mixins.dart';

import 'home_v2_cubit.dart';
import 'home_v2_state.dart';
import 'pages/home_page.dart';
import 'pages/yhxt_home_page.dart';

/// 首页 V2 入口
/// 职责：
/// 1. 提供 BlocProvider
/// 2. 分发到对应的布局页面
class HomeV2Screen extends StatelessWidget {
  const HomeV2Screen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) {
        // 检查 App 更新（移至 SystemUtil）
        SystemUtil.checkAppUpdate();
        return HomeV2Cubit()..loadAll();
      },
      child: const _HomeV2Content(),
    );
  }
}

/// 内部内容组件，处理生命周期和数据分发
class _HomeV2Content extends StatefulWidget {
  const _HomeV2Content();

  @override
  State<_HomeV2Content> createState() => _HomeV2ContentState();
}

class _HomeV2ContentState extends State<_HomeV2Content>
    with WidgetsBindingObserver, LocaleAwareScreenMixin {
  /// 记录应用进入后台的时间戳（秒）
  int _pausedTimeSeconds = 0;

  /// 超过此秒数后切换回前台需要刷新时间线
  static const int _timelineRefreshThreshold = 30;

  /// 超过此秒数后切换回前台需要刷新所有数据
  static const int _fullRefreshThreshold = 60;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // 订阅股指时间线
    getIt<IndexTradeCubit>().subscribeToTimeline();
  }

  @override
  void dispose() {
    getIt<IndexTradeCubit>().unsubscribeFromTimeline();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// 语言切换时刷新数据
  @override
  void onLocaleChanged(String locale) {
    if (mounted) {
      context.read<HomeV2Cubit>().loadAll();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;

    if (state == AppLifecycleState.paused) {
      _pausedTimeSeconds = now;
    } else if (state == AppLifecycleState.resumed) {
      final elapsed = now - _pausedTimeSeconds;

      // 超过 30 秒，刷新时间线
      if (elapsed > _timelineRefreshThreshold) {
        getIt<IndexTradeCubit>().reloadTimeline();
      }

      // 超过 60 秒，刷新所有数据
      if (elapsed > _fullRefreshThreshold) {
        context.read<HomeV2Cubit>().loadAll();
      }

      _pausedTimeSeconds = 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<HomeV2Cubit, HomeV2State>(
      listener: _handleSideEffects,
      builder: (context, state) {
        // 根据 Flavor 选择布局

        return switch (AppConfig.instance.flavor) {
        Flavor.yhxt => YhxtHomePage(state: state),
        Flavor.tempb => TempBHomePage(state: state),
        _ => HomePage(state: state),
        };
      },
    );
  }

  /// 处理副作用（通知弹窗等）
  void _handleSideEffects(BuildContext context, HomeV2State state) {
    // 处理通知弹窗
    if (!state.shouldSuppressPopup &&
        state.notificationStatus == DataStatus.success) {
      final popupNotificationList =
          state.notifications.where((e) => e.type == 3).toList();
      if (popupNotificationList.isNotEmpty) {
        _showNotificationPopup(context, popupNotificationList);
      }
    }
  }

  /// 显示通知弹窗
  void _showNotificationPopup(
    BuildContext context,
    List<NotificationEntity> notificationList,
  ) {
    // 预加载图片
    for (var notification in notificationList) {
      if (notification.imageUrl.isEmpty) continue;
      precacheImage(
        CachedNetworkImageProvider(notification.imageUrl),
        context,
      );
    }


    // 延迟显示弹窗，确保页面已渲染
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      showDialog(
        context: context,
        builder: (context) => NotificationPopup(notifications: notificationList),
      );
      // 标记已显示，防止重复弹出
      this.context.read<HomeV2Cubit>().suppressPopup();
    });
  }
}
