import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/wallet/bank_entity_list.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_bank_entity_list.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_wallet_entity_list.dart';
import 'package:gp_stock_app/core/models/entities/wallet/withdraw_config_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

class WalletApi {
  /// 获取所有usdt钱包地址
  static Future<List<USDTWallet>> fetchUsdtWalletAddressList() async {
    final res = await Http().request<USDTWalletList>(
      ApiEndpoints.getUsdtWalletAddressList,
      method: HttpMethod.get,
    );
    return res.data?.list ?? [];
  }

  /// 获取usdt网络类型
  static Future<List<USDTNetworkType>> fetchUsdtNetworkTypeList() async {
    final res = await Http().request<USDTNetworkTypeList>(
      ApiEndpoints.getUsdtNetworkType,
      method: HttpMethod.get,
    );
    return res.data?.list ?? [];
  }

  /// 新增usdt钱包地址
  static Future<bool> addUsdtWalletAddress({
    required String walletAddress, // 钱包地址
    required int network, // 网络 (1=TRC20, 2=ERC20, 3=BEP20 等)
    required bool isWithdrawDefault, // 是否默认提现地址 (0=否, 1=是)
  }) async {
    final res = await Http().request(
      ApiEndpoints.addUsdtWalletAddress,
      method: HttpMethod.post,
      params: {
        "walletAddress": walletAddress,
        "network": network,
        "isWithdrawDefault": isWithdrawDefault ? 1 : 0,
      },
    );
    return res.isSuccess;
  }

  /// 编辑usdt钱包地址
  static Future<bool> editUsdtWalletAddress({
    required int id, // 唯一标识
    required String walletAddress, // 钱包地址
    required int network, // 网络 (1=TRC20, 2=ERC20, 3=BEP20 等)
    required bool isWithdrawDefault, // 是否默认提现地址 (0=否, 1=是)
  }) async {
    final res = await Http().request(
      ApiEndpoints.editUsdtWalletAddress,
      method: HttpMethod.post,
      params: {
        "id": id,
        "walletAddress": walletAddress,
        "network": network,
        "isWithdrawDefault": isWithdrawDefault ? 1 : 0,
      },
    );
    return res.isSuccess;
  }

  /// 删除usdt钱包地址
  static Future<bool> deleteUsdtWalletAddress({
    required int id, // 唯一标识
  }) async {
    final res = await Http().request(
      "${ApiEndpoints.deleteUsdtWalletAddress}/$id",
      method: HttpMethod.delete,
      // queryParameters: {"id": id},
    );
    return res.isSuccess;
  }

  static Future<List<BankEntity>> getBankList() async {
    final res = await Http().request<BankEntityList>(
      ApiEndpoints.getBankList,
      method: HttpMethod.get,
    );
    return res.data?.list ?? [];
  }

  static Future<List<UserBankEntity>> getUserBankList() async {
    final res = await Http().request<UserBankEntityList>(
      ApiEndpoints.getUserBankList,
      method: HttpMethod.get,
    );
    return res.data?.list ?? [];
  }

  static Future<List<UserWalletEntity>> getUserWalletList({String? bankCode}) async {
    final res = await Http().request<UserWalletEntityList>(
      ApiEndpoints.getUserWalletList,
      method: HttpMethod.get,
      queryParameters: bankCode != null ? {'bankCode': bankCode} : null,
    );
    return res.data?.list ?? [];
  }

  static Future<bool> addUserBank({
    required String bankCardNo,
    required int systemBankId,
    required String mobile,
    required String smsCode,
  }) async {
    final res = await Http().request(
      ApiEndpoints.addUserBank,
      method: HttpMethod.post,
      params: {
        'bankCardNo': bankCardNo,
        'systemBankId': systemBankId,
        'mobile': mobile,
        'smsCode': smsCode,
      },
    );
    return res.isSuccess;
  }

  static Future<bool> deleteUserBank(int id) async {
    final res = await Http().request(
      '${ApiEndpoints.deleteUserBank}/$id',
      method: HttpMethod.delete,
    );
    return res.isSuccess;
  }

  static Future<WithdrawConfigEntity?> getWithdrawalConfig() async {
    final res = await Http().request<WithdrawConfigEntity>(
      ApiEndpoints.withdrawalConfig,
      method: HttpMethod.get,
    );
    return res.data;
  }
}
