import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/withdraw_config_entity.dart';

WithdrawConfigEntity $WithdrawConfigEntityFromJson(Map<String, dynamic> json) {
  final String? endWithdrawalTime = jsonConvert.convert<String>(json['endWithdrawalTime']);
  final int? handlingFeeType = jsonConvert.convert<int>(json['handlingFeeType']);
  final int? id = jsonConvert.convert<int>(json['id']);
  final int? maxWithdrawalAmount = jsonConvert.convert<int>(json['maxWithdrawalAmount']);
  final int? minWithdrawalAmount = jsonConvert.convert<int>(json['minWithdrawalAmount']);
  final String? startWithdrawalTime = jsonConvert.convert<String>(json['startWithdrawalTime']);
  final bool? testUsersCanWithdraw = jsonConvert.convert<bool>(json['testUsersCanWithdraw']);
  final int? withdrawalFee = jsonConvert.convert<int>(json['withdrawalFee']);
  final bool? withdrawalStatus = jsonConvert.convert<bool>(json['withdrawalStatus']);
  final int? withdrawalsDayCount = jsonConvert.convert<int>(json['withdrawalsDayCount']);
  return WithdrawConfigEntity(
    endWithdrawalTime: endWithdrawalTime ?? '',
    handlingFeeType: handlingFeeType ?? 0,
    id: id ?? 0,
    maxWithdrawalAmount: maxWithdrawalAmount ?? 0,
    minWithdrawalAmount: minWithdrawalAmount ?? 0,
    startWithdrawalTime: startWithdrawalTime ?? '',
    testUsersCanWithdraw: testUsersCanWithdraw ?? false,
    withdrawalFee: withdrawalFee ?? 0,
    withdrawalStatus: withdrawalStatus ?? false,
    withdrawalsDayCount: withdrawalsDayCount ?? 0,
  );
}

Map<String, dynamic> $WithdrawConfigEntityToJson(WithdrawConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['endWithdrawalTime'] = entity.endWithdrawalTime;
  data['handlingFeeType'] = entity.handlingFeeType;
  data['id'] = entity.id;
  data['maxWithdrawalAmount'] = entity.maxWithdrawalAmount;
  data['minWithdrawalAmount'] = entity.minWithdrawalAmount;
  data['startWithdrawalTime'] = entity.startWithdrawalTime;
  data['testUsersCanWithdraw'] = entity.testUsersCanWithdraw;
  data['withdrawalFee'] = entity.withdrawalFee;
  data['withdrawalStatus'] = entity.withdrawalStatus;
  data['withdrawalsDayCount'] = entity.withdrawalsDayCount;
  return data;
}

extension WithdrawConfigEntityExtension on WithdrawConfigEntity {
  WithdrawConfigEntity copyWith({
    String? endWithdrawalTime,
    int? handlingFeeType,
    int? id,
    int? maxWithdrawalAmount,
    int? minWithdrawalAmount,
    String? startWithdrawalTime,
    bool? testUsersCanWithdraw,
    int? withdrawalFee,
    bool? withdrawalStatus,
    int? withdrawalsDayCount,
  }) {
    return WithdrawConfigEntity(
      endWithdrawalTime: endWithdrawalTime ?? this.endWithdrawalTime,
      handlingFeeType: handlingFeeType ?? this.handlingFeeType,
      id: id ?? this.id,
      maxWithdrawalAmount: maxWithdrawalAmount ?? this.maxWithdrawalAmount,
      minWithdrawalAmount: minWithdrawalAmount ?? this.minWithdrawalAmount,
      startWithdrawalTime: startWithdrawalTime ?? this.startWithdrawalTime,
      testUsersCanWithdraw: testUsersCanWithdraw ?? this.testUsersCanWithdraw,
      withdrawalFee: withdrawalFee ?? this.withdrawalFee,
      withdrawalStatus: withdrawalStatus ?? this.withdrawalStatus,
      withdrawalsDayCount: withdrawalsDayCount ?? this.withdrawalsDayCount,
    );
  }
}