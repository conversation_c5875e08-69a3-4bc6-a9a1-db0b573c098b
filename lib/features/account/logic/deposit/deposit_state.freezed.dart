// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DepositState {
  DataStatus get depositStatus => throw _privateConstructorUsedError;
  DataStatus get depositRecordsFetchStatus =>
      throw _privateConstructorUsedError; // Added for deposit records
  String? get error => throw _privateConstructorUsedError;
  String? get depositAmount => throw _privateConstructorUsedError;
  UserBankEntity? get selectedBank => throw _privateConstructorUsedError;
  String? get orderNumber => throw _privateConstructorUsedError;
  bool? get isFormValid => throw _privateConstructorUsedError;
  List<DepositRecord>? get depositRecords =>
      throw _privateConstructorUsedError; // Added for deposit records
  int? get depositCurrent =>
      throw _privateConstructorUsedError; // Added for pagination
  int? get depositTotal => throw _privateConstructorUsedError;

  /// Create a copy of DepositState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DepositStateCopyWith<DepositState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DepositStateCopyWith<$Res> {
  factory $DepositStateCopyWith(
          DepositState value, $Res Function(DepositState) then) =
      _$DepositStateCopyWithImpl<$Res, DepositState>;
  @useResult
  $Res call(
      {DataStatus depositStatus,
      DataStatus depositRecordsFetchStatus,
      String? error,
      String? depositAmount,
      UserBankEntity? selectedBank,
      String? orderNumber,
      bool? isFormValid,
      List<DepositRecord>? depositRecords,
      int? depositCurrent,
      int? depositTotal});
}

/// @nodoc
class _$DepositStateCopyWithImpl<$Res, $Val extends DepositState>
    implements $DepositStateCopyWith<$Res> {
  _$DepositStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DepositState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? depositStatus = null,
    Object? depositRecordsFetchStatus = null,
    Object? error = freezed,
    Object? depositAmount = freezed,
    Object? selectedBank = freezed,
    Object? orderNumber = freezed,
    Object? isFormValid = freezed,
    Object? depositRecords = freezed,
    Object? depositCurrent = freezed,
    Object? depositTotal = freezed,
  }) {
    return _then(_value.copyWith(
      depositStatus: null == depositStatus
          ? _value.depositStatus
          : depositStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      depositRecordsFetchStatus: null == depositRecordsFetchStatus
          ? _value.depositRecordsFetchStatus
          : depositRecordsFetchStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      depositAmount: freezed == depositAmount
          ? _value.depositAmount
          : depositAmount // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as UserBankEntity?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      isFormValid: freezed == isFormValid
          ? _value.isFormValid
          : isFormValid // ignore: cast_nullable_to_non_nullable
              as bool?,
      depositRecords: freezed == depositRecords
          ? _value.depositRecords
          : depositRecords // ignore: cast_nullable_to_non_nullable
              as List<DepositRecord>?,
      depositCurrent: freezed == depositCurrent
          ? _value.depositCurrent
          : depositCurrent // ignore: cast_nullable_to_non_nullable
              as int?,
      depositTotal: freezed == depositTotal
          ? _value.depositTotal
          : depositTotal // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DepositStateImplCopyWith<$Res>
    implements $DepositStateCopyWith<$Res> {
  factory _$$DepositStateImplCopyWith(
          _$DepositStateImpl value, $Res Function(_$DepositStateImpl) then) =
      __$$DepositStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DataStatus depositStatus,
      DataStatus depositRecordsFetchStatus,
      String? error,
      String? depositAmount,
      UserBankEntity? selectedBank,
      String? orderNumber,
      bool? isFormValid,
      List<DepositRecord>? depositRecords,
      int? depositCurrent,
      int? depositTotal});
}

/// @nodoc
class __$$DepositStateImplCopyWithImpl<$Res>
    extends _$DepositStateCopyWithImpl<$Res, _$DepositStateImpl>
    implements _$$DepositStateImplCopyWith<$Res> {
  __$$DepositStateImplCopyWithImpl(
      _$DepositStateImpl _value, $Res Function(_$DepositStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DepositState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? depositStatus = null,
    Object? depositRecordsFetchStatus = null,
    Object? error = freezed,
    Object? depositAmount = freezed,
    Object? selectedBank = freezed,
    Object? orderNumber = freezed,
    Object? isFormValid = freezed,
    Object? depositRecords = freezed,
    Object? depositCurrent = freezed,
    Object? depositTotal = freezed,
  }) {
    return _then(_$DepositStateImpl(
      depositStatus: null == depositStatus
          ? _value.depositStatus
          : depositStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      depositRecordsFetchStatus: null == depositRecordsFetchStatus
          ? _value.depositRecordsFetchStatus
          : depositRecordsFetchStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      depositAmount: freezed == depositAmount
          ? _value.depositAmount
          : depositAmount // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as UserBankEntity?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      isFormValid: freezed == isFormValid
          ? _value.isFormValid
          : isFormValid // ignore: cast_nullable_to_non_nullable
              as bool?,
      depositRecords: freezed == depositRecords
          ? _value._depositRecords
          : depositRecords // ignore: cast_nullable_to_non_nullable
              as List<DepositRecord>?,
      depositCurrent: freezed == depositCurrent
          ? _value.depositCurrent
          : depositCurrent // ignore: cast_nullable_to_non_nullable
              as int?,
      depositTotal: freezed == depositTotal
          ? _value.depositTotal
          : depositTotal // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$DepositStateImpl implements _DepositState {
  const _$DepositStateImpl(
      {this.depositStatus = DataStatus.idle,
      this.depositRecordsFetchStatus = DataStatus.idle,
      this.error,
      this.depositAmount,
      this.selectedBank,
      this.orderNumber,
      this.isFormValid,
      final List<DepositRecord>? depositRecords,
      this.depositCurrent,
      this.depositTotal})
      : _depositRecords = depositRecords;

  @override
  @JsonKey()
  final DataStatus depositStatus;
  @override
  @JsonKey()
  final DataStatus depositRecordsFetchStatus;
// Added for deposit records
  @override
  final String? error;
  @override
  final String? depositAmount;
  @override
  final UserBankEntity? selectedBank;
  @override
  final String? orderNumber;
  @override
  final bool? isFormValid;
  final List<DepositRecord>? _depositRecords;
  @override
  List<DepositRecord>? get depositRecords {
    final value = _depositRecords;
    if (value == null) return null;
    if (_depositRecords is EqualUnmodifiableListView) return _depositRecords;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Added for deposit records
  @override
  final int? depositCurrent;
// Added for pagination
  @override
  final int? depositTotal;

  @override
  String toString() {
    return 'DepositState(depositStatus: $depositStatus, depositRecordsFetchStatus: $depositRecordsFetchStatus, error: $error, depositAmount: $depositAmount, selectedBank: $selectedBank, orderNumber: $orderNumber, isFormValid: $isFormValid, depositRecords: $depositRecords, depositCurrent: $depositCurrent, depositTotal: $depositTotal)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DepositStateImpl &&
            (identical(other.depositStatus, depositStatus) ||
                other.depositStatus == depositStatus) &&
            (identical(other.depositRecordsFetchStatus,
                    depositRecordsFetchStatus) ||
                other.depositRecordsFetchStatus == depositRecordsFetchStatus) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.depositAmount, depositAmount) ||
                other.depositAmount == depositAmount) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber) &&
            (identical(other.isFormValid, isFormValid) ||
                other.isFormValid == isFormValid) &&
            const DeepCollectionEquality()
                .equals(other._depositRecords, _depositRecords) &&
            (identical(other.depositCurrent, depositCurrent) ||
                other.depositCurrent == depositCurrent) &&
            (identical(other.depositTotal, depositTotal) ||
                other.depositTotal == depositTotal));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      depositStatus,
      depositRecordsFetchStatus,
      error,
      depositAmount,
      selectedBank,
      orderNumber,
      isFormValid,
      const DeepCollectionEquality().hash(_depositRecords),
      depositCurrent,
      depositTotal);

  /// Create a copy of DepositState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DepositStateImplCopyWith<_$DepositStateImpl> get copyWith =>
      __$$DepositStateImplCopyWithImpl<_$DepositStateImpl>(this, _$identity);
}

abstract class _DepositState implements DepositState {
  const factory _DepositState(
      {final DataStatus depositStatus,
      final DataStatus depositRecordsFetchStatus,
      final String? error,
      final String? depositAmount,
      final UserBankEntity? selectedBank,
      final String? orderNumber,
      final bool? isFormValid,
      final List<DepositRecord>? depositRecords,
      final int? depositCurrent,
      final int? depositTotal}) = _$DepositStateImpl;

  @override
  DataStatus get depositStatus;
  @override
  DataStatus get depositRecordsFetchStatus; // Added for deposit records
  @override
  String? get error;
  @override
  String? get depositAmount;
  @override
  UserBankEntity? get selectedBank;
  @override
  String? get orderNumber;
  @override
  bool? get isFormValid;
  @override
  List<DepositRecord>? get depositRecords; // Added for deposit records
  @override
  int? get depositCurrent; // Added for pagination
  @override
  int? get depositTotal;

  /// Create a copy of DepositState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DepositStateImplCopyWith<_$DepositStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
