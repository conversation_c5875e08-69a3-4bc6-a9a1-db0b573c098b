import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/bank_entity_list.dart';

BankEntityList $BankEntityListFromJson(Map<String, dynamic> json) {
  final List<BankEntity>? list = (json['list'] as List<dynamic>?)?.map(
                      (e) => jsonConvert.convert<BankEntity>(e) as BankEntity).toList();
  return BankEntityList(
    list: list ?? const [],
  );
}

Map<String, dynamic> $BankEntityListToJson(BankEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension BankEntityListExtension on BankEntityList {
  BankEntityList copyWith({
    List<BankEntity>? list,
  }) {
    return BankEntityList(
      list: list ?? this.list,
    );
  }
}

BankEntity $BankEntityFromJson(Map<String, dynamic> json) {
  final String? bankCode = jsonConvert.convert<String>(json['bankCode']);
  final String? bankFullName = jsonConvert.convert<String>(json['bankFullName']);
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  final int? bankRegion = jsonConvert.convert<int>(json['bankRegion']);
  final String? icon = jsonConvert.convert<String>(json['icon']);
  final int? id = jsonConvert.convert<int>(json['id']);
  return BankEntity(
    bankCode: bankCode ?? '',
    bankFullName: bankFullName ?? '',
    bankName: bankName ?? '',
    bankRegion: bankRegion ?? 0,
    icon: icon ?? '',
    id: id ?? 0,
  );
}

Map<String, dynamic> $BankEntityToJson(BankEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['bankCode'] = entity.bankCode;
  data['bankFullName'] = entity.bankFullName;
  data['bankName'] = entity.bankName;
  data['bankRegion'] = entity.bankRegion;
  data['icon'] = entity.icon;
  data['id'] = entity.id;
  return data;
}

extension BankEntityExtension on BankEntity {
  BankEntity copyWith({
    String? bankCode,
    String? bankFullName,
    String? bankName,
    int? bankRegion,
    String? icon,
    int? id,
  }) {
    return BankEntity(
      bankCode: bankCode ?? this.bankCode,
      bankFullName: bankFullName ?? this.bankFullName,
      bankName: bankName ?? this.bankName,
      bankRegion: bankRegion ?? this.bankRegion,
      icon: icon ?? this.icon,
      id: id ?? this.id,
    );
  }
}