{"version": "0.2.0", "configurations": [{"name": "GP (debug)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_gp.dart", "preLaunchTask": "Setup skin: GP", "args": ["--flavor", "gp", "-t", "lib/main_gp.dart"]}, {"name": "Pre (debug)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_pre.dart", "preLaunchTask": "Setup skin: PRE", "args": ["--flavor", "pre", "-t", "lib/main_pre.dart"]}, {"name": "RSYP (rongshunyoupei)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_rsyp.dart", "preLaunchTask": "Setup skin: RSYP", "args": ["--flavor", "rsyp", "-t", "lib/main_rsyp.dart"]}, {"name": "YHXT (<PERSON><PERSON>)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_yhxt.dart", "preLaunchTask": "Setup skin: YHXT", "args": ["--flavor", "yhxt", "-t", "lib/main_yhxt.dart"]}, {"name": "TempA (debug)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_tempa.dart", "preLaunchTask": "Setup skin: TEMPA", "args": ["--flavor", "tempa", "-t", "lib/main_tempa.dart"]}, {"name": "TempB (debug)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_tempb.dart", "preLaunchTask": "Setup skin: <PERSON>AN<PERSON>_GOLDEN", "args": ["--flavor", "tempb", "-t", "lib/main_tempb.dart"]}, {"name": "BSZB (Gem Capital - bǎoshí)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_bszb.dart", "preLaunchTask": "Setup skin: BSZB", "args": ["--flavor", "bszb", "-t", "lib/main_bszb.dart"]}, {"name": "DYZB (deying ziben)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_dyzb.dart", "preLaunchTask": "Setup skin: DYZB", "args": ["--flavor", "dyzb", "-t", "lib/main_dyzb.dart"]}, {"name": "XYZQ (xinyuan)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_xyzq.dart", "preLaunchTask": "Setup skin: XYZQ", "args": ["--flavor", "xyzq", "-t", "lib/main_xyzq.dart"]}]}