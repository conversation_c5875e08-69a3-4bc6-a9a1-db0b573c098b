import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/secure_storage_helper.dart';
import 'package:gp_stock_app/shared/app/utilities/auth_style.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums/signup_type.dart';
import 'package:gp_stock_app/shared/constants/keys.dart';
import 'package:gp_stock_app/shared/widgets/app_header.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

import '../../../core/utils/validators.dart';
import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/constants/enums.dart';
import '../../account/logic/otp/otp_cubit.dart';
import '../../account/logic/otp/otp_state.dart';
import '../logic/sign_up/sign_up_cubit.dart';
import '../widgets/sign_up_widget.dart';

class SignUpScreen extends StatelessWidget with AppHeaderMixin {
  const SignUpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: AuthStyle.headerWrapper(
          appheader: buildAppHeader(context, showHeaderImage: true),
          loginForm: _SignUpForm(),
        ),
      ),
    );
  }
}

class _SignUpForm extends StatefulWidget {
  @override
  State<_SignUpForm> createState() => _SignUpFormState();
}

class _SignUpFormState extends State<_SignUpForm> {
  final TextEditingController mobileEmailController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  final TextEditingController inviteCodeController = TextEditingController();

  String? inviteCode;

  @override
  void initState() {
    _getInviteCode();
    super.initState();
  }

  void _getInviteCode() async {
    if (AppConfig.instance.flavor == Flavor.yhxt && Platform.isIOS) {
      // 沅和的iOS包默认显示这个邀请码
      inviteCodeController.text = "5805063";
    }
    final code = await SecureStorageHelper().readSecureData(LocalStorageKeys.kRegisterInviteCodeKey);
    if (code != null) {
      setState(() {
        inviteCode = code;
        inviteCodeController.text = code;
      });
    }
  }

  // Add form key for validation
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  void _handleRegisterButtonPress(BuildContext context) {
    // Use form validation instead of toast messages
    if (!_formKey.currentState!.validate()) {
      return;
    }
    context.read<SignUpCubit>().register(
          mobileOrEmail: mobileEmailController.text,
          password: passwordController.text.toBase64(),
          inviteCode: inviteCodeController.text,
          code: codeController.text,
        );
  }

  void _handleSendVerificationCode(BuildContext context, SignUpType signUpType) async {
    // Validate only the mobile or email field
    final validation = signUpType == SignUpType.phone
        ? Validators.validateMobile(mobileEmailController.text)
        : Validators.validateEmail(mobileEmailController.text);
    if (validation != null) {
      _formKey.currentState?.validate();
      return;
    }
    context.read<OtpCubit>().verifyAndSendOtp(
          mobileOrEmail: mobileEmailController.text,
          type: signUpType.otpTypeRegister,
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => OtpCubit(),
      child: MultiBlocListener(
        listeners: [
          BlocListener<OtpCubit, OtpState>(
            listenWhen: (previous, current) => previous.sendStatus != current.sendStatus,
            listener: (context, state) {
              if (state.sendStatus == DataStatus.loading) {
                GPEasyLoading.showLoading(message: 'sendingCode'.tr());
              } else if (state.sendStatus == DataStatus.success && state.isSent) {
                GPEasyLoading.showSuccess(message: 'verificationCodeSent'.tr());
              }
            },
          ),
          BlocListener<SignUpCubit, SignUpState>(
            listenWhen: (previous, current) => previous.signUpFetchStatus != current.signUpFetchStatus,
            listener: (context, state) {
              if (state.signUpFetchStatus == DataStatus.success) {
                GPEasyLoading.showSuccess(message: 'registerSuccess'.tr());
                Navigator.of(context).pop({
                  'mobile': mobileEmailController.text,
                  'password': passwordController.text,
                  'registerType': state.signUpType,
                });
              }
              if (state.signUpFetchStatus == DataStatus.failed) {
                NetworkHelper.handleMessage(
                  state.error,
                  type: HandleTypes.customDialog,
                  snackBarType: SnackBarType.error,
                  dialogKey: 'signup_error_dialog',
                );
              }
            },
          ),
        ],
        child: Builder(
          builder: (context) {
            return AnimationConfiguration.synchronized(
                duration: const Duration(milliseconds: 800),
                child: SlideAnimation(
                  verticalOffset: 100.0,
                  child: FadeInAnimation(
                    child: Container(
                      width: double.infinity,
                      height: 0.70.gsh,
                      decoration: BoxDecoration(
                        color: context.theme.cardColor,
                        borderRadius: AuthStyle.loginFormBorder,
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 30.gw),
                        child: SingleChildScrollView(
                          child: AnimationLimiter(
                            child: BlocSelector<SignUpCubit, SignUpState, SignUpType>(
                              selector: (state) => state.signUpType,
                              builder: (context, signUpType) {
                                return Form(
                                  key: _formKey,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: AnimationConfiguration.toStaggeredList(
                                      duration: const Duration(milliseconds: 600),
                                      childAnimationBuilder: (widget) => SlideAnimation(
                                        verticalOffset: 50.0,
                                        child: FadeInAnimation(
                                          child: widget,
                                        ),
                                      ),
                                      children: [
                                        28.verticalSpace,
                                        CommonTabBar(
                                          data: ['signUpMobile'.tr(), 'signUpEmail'.tr()],
                                          onTap: (index) =>
                                              context.read<SignUpCubit>().selectTab(SignUpType.values[index]),
                                          currentIndex: signUpType.index,
                                          style: switch (AppConfig.instance.skinStyle) {
                                            AppSkinStyle.kGP => CommonTabBarStyle.round,
                                            AppSkinStyle.kTemplateD => CommonTabBarStyle.trapezoid,
                                            _ => CommonTabBarStyle.line,
                                          },
                                          backgroundColor: Colors.transparent,
                                          isScrollable: false,
                                          styleOverrides: AuthStyle.styleOverrides(context),
                                        ),
                                        SizedBox(height: 20.gw),
                                        BlocBuilder<OtpCubit, OtpState>(
                                          builder: (context, otpState) {
                                            return Column(
                                              children: [
                                                // Phone field with validation
                                                TextFieldWidget(
                                                  controller: mobileEmailController,
                                                  hintText: signUpType == SignUpType.phone
                                                      ? 'loginPhonePlaceholder'.tr()
                                                      : 'loginEmailPlaceholder'.tr(),
                                                  textInputType: signUpType == SignUpType.phone
                                                      ? TextInputType.phone
                                                      : TextInputType.emailAddress,
                                                  maxLength: signUpType == SignUpType.phone ? 11 : null,
                                                  counterText: '',
                                                  validator: (value) => signUpType == SignUpType.phone
                                                      ? Validators.validateMobile(value)
                                                      : Validators.validateEmail(value),
                                                  prefixIcon: SvgPicture.asset(
                                                    signUpType == SignUpType.phone
                                                        ? Assets.mobileIcon
                                                        : Assets.mailIcon,
                                                    fit: BoxFit.scaleDown,
                                                    width: 18.gw,
                                                    height: 18.gw,
                                                  ),
                                                ),
                                                15.verticalSpace,
                                                // Verification code field with button
                                                Container(
                                                  padding: EdgeInsets.zero,
                                                  child: Row(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Expanded(
                                                        flex: 3,
                                                        child: SizedBox(
                                                          child: TextFieldWidget(
                                                            controller: codeController,
                                                            inputFormatters: [
                                                              LengthLimitingTextInputFormatter(6),
                                                            ],
                                                            hintText: 'loginVerificationCodePlaceholder'.tr(),
                                                            textInputType: TextInputType.text,
                                                            contentPadding: EdgeInsets.zero,
                                                            validator: (value) => value == null || value.isEmpty
                                                                ? 'incorrectOtp'.tr()
                                                                : null,
                                                            prefixIcon: SvgPicture.asset(
                                                              Assets.shieldIcon,
                                                              fit: BoxFit.scaleDown,
                                                              width: 18.gw,
                                                              height: 18.gw,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      8.horizontalSpace,
                                                      Expanded(
                                                        flex: 2,
                                                        child: SizedBox(
                                                          height: 43.gw,
                                                          child: AnimationConfiguration.synchronized(
                                                            duration: const Duration(milliseconds: 300),
                                                            child: ScaleAnimation(
                                                              scale: 0.95,
                                                              child: CommonButton(
                                                                title: otpState.isTimerActive
                                                                    ? '${'resend'.tr()} (${otpState.timerDuration}s)'
                                                                    : 'loginVerificationCode'.tr(),
                                                                fontSize: 12.gsp,
                                                                onPressed: otpState.isTimerActive
                                                                    ? null
                                                                    : () => _handleSendVerificationCode(
                                                                        context, signUpType),
                                                                enable: !otpState.isTimerActive,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                15.verticalSpace,
                                                TextFieldWidget(
                                                  controller: passwordController,
                                                  hintText: 'registerPasswordHint'.tr(),
                                                  textInputType: TextInputType.visiblePassword,
                                                  prefixIcon: SvgPicture.asset(
                                                    Assets.lockIcon,
                                                    fit: BoxFit.scaleDown,
                                                    width: 18.gw,
                                                    height: 18.gw,
                                                  ),
                                                  obscureText: true,
                                                  passwordIcon: true,
                                                  validator: (value) => !Validators.newPassword.hasMatch(value ?? '')
                                                      ? 'registerPasswordHint'.tr()
                                                      : null,
                                                ),
                                                15.verticalSpace,
                                                TextFieldWidget(
                                                  controller: confirmPasswordController,
                                                  hintText: 'registerConfirmPassword'.tr(),
                                                  textInputType: TextInputType.visiblePassword,
                                                  prefixIcon: SvgPicture.asset(
                                                    Assets.lockIcon,
                                                    fit: BoxFit.scaleDown,
                                                    width: 18.gw,
                                                    height: 18.gw,
                                                  ),
                                                  obscureText: true,
                                                  passwordIcon: true,
                                                  validator: (value) => value != passwordController.text
                                                      ? 'passwordsDontMatch'.tr()
                                                      : null,
                                                ),
                                                15.verticalSpace,
                                                TextFieldWidget(
                                                  controller: inviteCodeController,
                                                  hintText: 'registerInviteCode'.tr(),
                                                  textInputType: TextInputType.number,
                                                  enabled: inviteCode == null,
                                                  prefixIcon: SvgPicture.asset(
                                                    Assets.mailIcon,
                                                    fit: BoxFit.scaleDown,
                                                    width: 18.gw,
                                                    height: 18.gw,
                                                  ),
                                                  inputFormatters: [
                                                    FilteringTextInputFormatter.digitsOnly,
                                                    LengthLimitingTextInputFormatter(11),
                                                  ],
                                                  validator: (value) => (value ?? '').isNotEmpty &&
                                                          !Validators.inviteCode.hasMatch(value ?? '')
                                                      ? 'registerInviteCodeHint'.tr()
                                                      : null,
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                        20.verticalSpace,
                                        AnimationConfiguration.synchronized(
                                          duration: const Duration(milliseconds: 300),
                                          child: ScaleAnimation(
                                            scale: 0.95,
                                            child: CommonButton(
                                              title: 'register'.tr(),
                                              showLoading:
                                                  context.read<SignUpCubit>().state.signUpFetchStatus.isLoading,
                                              onPressed: () => _handleRegisterButtonPress(context),
                                              radius: AuthStyle.buttonBorderRadius,
                                            ),
                                          ),
                                        ),
                                        12.verticalSpace,
                                        RegisterFooter(),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ));
          },
        ),
      ),
    );
  }
}
