import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/member.dart';
import 'package:gp_stock_app/core/models/entities/user/user.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/forgot/domain/services/forgot_service.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums/signup_type.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_socket_interface.dart';

import '../../../../shared/constants/enums.dart';

part 'profile_state.dart';

class ProfileCubit extends AuthAwareCubit<ProfileState> {
  final WebSocketService _webSocketService = getIt<WebSocketService>();

  ProfileCubit() : super(const ProfileState()) {
    _webSocketService.onMessageWithType(SocketEvents.updateProfile, loginRequired: true).listen((message) {
      getIt<UserCubit>().fetchUserInfo();
    });
  }

  @override
  void onLoggedIn() {}

  @override
  void onLoggedOut() => emit(const ProfileState());

  void selectAvatar(int index) {
    emit(state.copyWith(selectedAvatarIndex: index));
  }

  Future<void> confirmAvatarSelection() async {
    if (state.selectedAvatarIndex == null) return;

    final avatarIndex = '${state.selectedAvatarIndex! + 1}';
    await updateAvatar(avatarIndex);
  }

  Future<void> updateAvatar(String avatar) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.avatar,
    ));

    final result = await MemberApi.updateUserInfo(avatar: avatar);
    _handleUpdateResult(result);
    if (state.updateStatus == DataStatus.success) {
      final currentUserInfo = getIt<UserCubit>().state.userInfo;
      if (currentUserInfo != null) {
        final updatedUserInfo = currentUserInfo.copyWith(avatar: avatar);
        getIt<UserCubit>().setUserInfo(updatedUserInfo);
      }
    }
  }

  Future<void> updateNickname(String nickname) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.nickname,
    ));

    final result = await MemberApi.updateUserInfo(nickname: nickname);
    _handleUpdateResult(result);
    if (state.updateStatus == DataStatus.success) {
      final currentUserInfo = getIt<UserCubit>().state.userInfo;
      if (currentUserInfo != null) {
        final updatedUserInfo = currentUserInfo.copyWith(nickname: nickname);
        getIt<UserCubit>().setUserInfo(updatedUserInfo);
      }
    }
  }

  Future<void> updateGender(int sex) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.gender,
    ));

    final result = await MemberApi.updateUserInfo(sex: sex);
    _handleUpdateResult(result);
    if (state.updateStatus == DataStatus.success) {
      final currentUserInfo = getIt<UserCubit>().state.userInfo;
      if (currentUserInfo != null) {
        final updatedUserInfo = currentUserInfo.copyWith(sex: sex);
        getIt<UserCubit>().setUserInfo(updatedUserInfo);
      }
    }
  }

  Future<void> updateEmail(String email) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.email,
    ));

    final result = await MemberApi.updateUserInfo(email: email);
    _handleUpdateResult(result);
    if (state.updateStatus == DataStatus.success) {
      final currentUserInfo = getIt<UserCubit>().state.userInfo;
      if (currentUserInfo != null) {
        final updatedUserInfo = currentUserInfo.copyWith(email: email);
        getIt<UserCubit>().setUserInfo(updatedUserInfo);
      }
    }
  }

  Future<void> updatePhoneNumber(String phoneNo) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.bio,
    ));

    final result = await MemberApi.updateUserInfo(phoneNo: phoneNo);
    _handleUpdateResult(result);
    if (state.updateStatus == DataStatus.success) {
      final currentUserInfo = getIt<UserCubit>().state.userInfo;
      if (currentUserInfo != null) {
        final updatedUserInfo = currentUserInfo.copyWith(profiles: phoneNo);
        getIt<UserCubit>().setUserInfo(updatedUserInfo);
      }
    }
  }

  Future<bool> changePasswordByOtp({
    required String password,
    String? originalPassword,
    String? smsCode,
    PasswordChangeType type = PasswordChangeType.accountVerification,
    required PasswordModifyType passwordModifyType,
  }) async {
    GPEasyLoading.showLoading();
    final userData = getIt<UserCubit>().state.userInfo;
    if (userData == null) {
      emit(state.copyWith(
        updateStatus: DataStatus.failed,
        error: 'userDataNotFound'.tr(),
      ));
      return false;
    }

    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.password,
    ));

    final base64NewPassword = password.toBase64();
    bool result = false;

    if (type == PasswordChangeType.accountVerification) {
      if (originalPassword == null) {
        emit(state.copyWith(
          updateStatus: DataStatus.failed,
          error: 'Original password required',
        ));
        return false;
      }

      result = await ForgotService.resetPassword(
        mobile: state.changePasswordTab == SignUpType.phone ? userData.mobile : null,
        email: state.changePasswordTab == SignUpType.email ? userData.email : null,
        newPassword: base64NewPassword,
        smsCode: state.changePasswordTab == SignUpType.phone ? smsCode : null,
        emailCode: state.changePasswordTab == SignUpType.email ? smsCode : null,
        verifyType: state.changePasswordTab.updatePasswordVerifyType,
        passwordType: passwordModifyType,
      );
      GPEasyLoading.dismiss();
      if (result) {
        emit(state.copyWith(
          updateStatus: DataStatus.success,
          error: null,
        ));
      }
      GPEasyLoading.showSuccess(message: 'passwordChangedSuccessfully'.tr());
      return result;
    } else {
      if (smsCode == null) {
        emit(state.copyWith(
          updateStatus: DataStatus.failed,
          error: 'SMS verification code required',
        ));
        return false;
      }
      result = await ForgotService.resetPassword(
        mobile: state.changePasswordTab == SignUpType.phone ? userData.mobile : null,
        email: state.changePasswordTab == SignUpType.email ? userData.email : null,
        newPassword: base64NewPassword,
        smsCode: state.changePasswordTab == SignUpType.phone ? smsCode : null,
        emailCode: state.changePasswordTab == SignUpType.email ? smsCode : null,
        verifyType: state.changePasswordTab.updatePasswordVerifyType,
        passwordType: passwordModifyType,
      );
      GPEasyLoading.dismiss();
      if (result) {
        emit(state.copyWith(
          updateStatus: DataStatus.success,
          error: null,
        ));
        GPEasyLoading.showSuccess(message: 'passwordChangedSuccessfully'.tr());
        return true;
      }

      emit(state.copyWith(
        updateStatus: DataStatus.failed,
      ));
      return false;
    }
  }

  Future<bool> changePassword({
    required String password,
    required String newPassword,
    required PasswordModifyType passwordModifyType,
  }) async {
    final result = await ForgotService.resetPassword(
      newPassword: newPassword.toBase64(),
      oldPassword: password.toBase64(),
      verifyType: 'account',
      passwordType: passwordModifyType,
    );
    return result;
  }

  void _handleUpdateResult(bool result) {
    if (result) {
      emit(state.copyWith(
        updateStatus: DataStatus.success,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        updateStatus: DataStatus.failed,
      ));
    }
  }

  Future<bool> updateMobile({
    required String countryCode,
    required String currentMobile,
    required String newMobile,
    required String smsCode,
  }) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.mobile,
    ));

    final isSuccess = await MemberApi.updateMobile(
      countryCode: countryCode,
      currentMobile: currentMobile,
      newMobile: newMobile,
      smsCode: smsCode,
    );

    if (isSuccess) {
      emit(state.copyWith(
        updateStatus: DataStatus.success,
        error: null,
      ));
      final currentUserInfo = getIt<UserCubit>().state.userInfo;
      if (currentUserInfo != null) {
        final updatedUserInfo = currentUserInfo.copyWith(mobile: newMobile);
        getIt<UserCubit>().setUserInfo(updatedUserInfo);
      }
      return true;
    } else {
      emit(state.copyWith(
        updateStatus: DataStatus.failed,
      ));
      return false;
    }
  }

  Future<bool> updateEmailV2({
    required String currentEmail,
    required String newEmail,
    required String emailCode,
  }) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.email,
    ));

    final isSuccess = await MemberApi.updateEmail(
      currentEmail: currentEmail,
      newEmail: newEmail,
      emailCode: emailCode,
    );

    if (isSuccess) {
      emit(state.copyWith(
        updateStatus: DataStatus.success,
        error: null,
      ));
      final currentUserInfo = getIt<UserCubit>().state.userInfo;
      if (currentUserInfo != null) {
        final updatedUserInfo = currentUserInfo.copyWith(email: newEmail);
        getIt<UserCubit>().setUserInfo(updatedUserInfo);
      }
      return true;
    } else {
      emit(state.copyWith(
        updateStatus: DataStatus.failed,
      ));
      return false;
    }
  }

  void changePasswordTab(SignUpType tab) => emit(state.copyWith(changePasswordTab: tab));
}
