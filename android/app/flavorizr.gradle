android {
    flavorDimensions += "flavor-type"

    productFlavors {
        pre {
            dimension "flavor-type"
            applicationId "com.gp.pre.stock"
            manifestPlaceholders.appScheme = "ffkoup"
            manifestPlaceholders.OPENINSTALL_APPKEY = "ffkoup"
            manifestPlaceholders.ENGAGELAB_PRIVATES_APPKEY = ""
            manifestPlaceholders.ENGAGELAB_PRIVATES_CHANNEL = "developer"
            manifestPlaceholders.ENGAGELAB_PRIVATES_PROCESS = ":remote"
            manifestPlaceholders.XIAOMI_APPID = ""
            manifestPlaceholders.XIAOMI_APPKEY = ""
            manifestPlaceholders.MEIZU_APPID = ""
            manifestPlaceholders.MEIZU_APPKEY = ""
            manifestPlaceholders.OPPO_APPID = ""
            manifestPlaceholders.OPPO_APPKEY = ""
            manifestPlaceholders.OPPO_APPSECRET = ""
            manifestPlaceholders.VIVO_APPID = ""
            manifestPlaceholders.VIVO_APPKEY = ""
            manifestPlaceholders.HONOR_APPID = ""
            manifestPlaceholders.APP_TCP_SSL = ""
            manifestPlaceholders.APP_DEBUG = ""
            manifestPlaceholders.COUNTRY_CODE = ""
            resValue "string", "app_name", "GP Pre"
            // Use flavor-specific signing config
            signingConfig signingConfigs.preRelease
        }
        gp {
            dimension "flavor-type"
            applicationId "com.gp.stock"
            manifestPlaceholders.appScheme = "vgyl58"
            manifestPlaceholders.OPENINSTALL_APPKEY = "vgyl58"
            manifestPlaceholders.ENGAGELAB_PRIVATES_APPKEY = "745e479f819fc6bafa7a7f13"
            manifestPlaceholders.ENGAGELAB_PRIVATES_CHANNEL = "developer"
            manifestPlaceholders.ENGAGELAB_PRIVATES_PROCESS = ":remote"
            manifestPlaceholders.XIAOMI_APPID = ""
            manifestPlaceholders.XIAOMI_APPKEY = ""
            manifestPlaceholders.MEIZU_APPID = ""
            manifestPlaceholders.MEIZU_APPKEY = ""
            manifestPlaceholders.OPPO_APPID = ""
            manifestPlaceholders.OPPO_APPKEY = ""
            manifestPlaceholders.OPPO_APPSECRET = ""
            manifestPlaceholders.VIVO_APPID = ""
            manifestPlaceholders.VIVO_APPKEY = ""
            manifestPlaceholders.HONOR_APPID = ""
            manifestPlaceholders.APP_TCP_SSL = ""
            manifestPlaceholders.APP_DEBUG = ""
            manifestPlaceholders.COUNTRY_CODE = ""
            resValue "string", "app_name", "GP Stock"
            // Use flavor-specific signing config
            signingConfig signingConfigs.gpRelease
        }
        rsyp {
            dimension "flavor-type"
            applicationId "com.rsyp.stock"
            manifestPlaceholders.appScheme = "n8x1cr"
            manifestPlaceholders.OPENINSTALL_APPKEY = "n8x1cr"
            manifestPlaceholders.ENGAGELAB_PRIVATES_APPKEY = ""
            manifestPlaceholders.ENGAGELAB_PRIVATES_CHANNEL = "developer"
            manifestPlaceholders.ENGAGELAB_PRIVATES_PROCESS = ":remote"
            manifestPlaceholders.XIAOMI_APPID = ""
            manifestPlaceholders.XIAOMI_APPKEY = ""
            manifestPlaceholders.MEIZU_APPID = ""
            manifestPlaceholders.MEIZU_APPKEY = ""
            manifestPlaceholders.OPPO_APPID = ""
            manifestPlaceholders.OPPO_APPKEY = ""
            manifestPlaceholders.OPPO_APPSECRET = ""
            manifestPlaceholders.VIVO_APPID = ""
            manifestPlaceholders.VIVO_APPKEY = ""
            manifestPlaceholders.HONOR_APPID = ""
            manifestPlaceholders.APP_TCP_SSL = ""
            manifestPlaceholders.APP_DEBUG = ""
            manifestPlaceholders.COUNTRY_CODE = ""
            resValue "string", "app_name", "荣顺优配"
            // Use flavor-specific signing config
            signingConfig signingConfigs.rsypRelease
        }
        yhxt {
            dimension "flavor-type"
            applicationId "com.yhxt.stock"
            manifestPlaceholders.appScheme = "gf4rmg"
            manifestPlaceholders.OPENINSTALL_APPKEY = "gf4rmg"
            manifestPlaceholders.ENGAGELAB_PRIVATES_APPKEY = "689a76e7db852a0043f8cf5e"
            manifestPlaceholders.ENGAGELAB_PRIVATES_CHANNEL = "developer"
            manifestPlaceholders.ENGAGELAB_PRIVATES_PROCESS = ":remote"
            manifestPlaceholders.XIAOMI_APPID = ""
            manifestPlaceholders.XIAOMI_APPKEY = ""
            manifestPlaceholders.MEIZU_APPID = ""
            manifestPlaceholders.MEIZU_APPKEY = ""
            manifestPlaceholders.OPPO_APPID = ""
            manifestPlaceholders.OPPO_APPKEY = ""
            manifestPlaceholders.OPPO_APPSECRET = ""
            manifestPlaceholders.VIVO_APPID = ""
            manifestPlaceholders.VIVO_APPKEY = ""
            manifestPlaceholders.HONOR_APPID = ""
            manifestPlaceholders.APP_TCP_SSL = ""
            manifestPlaceholders.APP_DEBUG = ""
            manifestPlaceholders.COUNTRY_CODE = ""
            resValue "string", "app_name", "沅和信投"
            // Use flavor-specific signing config
            signingConfig signingConfigs.yhxtRelease
        }
        tempa {
            dimension "flavor-type"
            applicationId "com.tempa.stock"
            manifestPlaceholders.appScheme = "k0qslv"
            manifestPlaceholders.OPENINSTALL_APPKEY = "k0qslv"
            manifestPlaceholders.ENGAGELAB_PRIVATES_APPKEY = ""
            manifestPlaceholders.ENGAGELAB_PRIVATES_CHANNEL = "developer"
            manifestPlaceholders.ENGAGELAB_PRIVATES_PROCESS = ":remote"
            manifestPlaceholders.XIAOMI_APPID = ""
            manifestPlaceholders.XIAOMI_APPKEY = ""
            manifestPlaceholders.MEIZU_APPID = ""
            manifestPlaceholders.MEIZU_APPKEY = ""
            manifestPlaceholders.OPPO_APPID = ""
            manifestPlaceholders.OPPO_APPKEY = ""
            manifestPlaceholders.OPPO_APPSECRET = ""
            manifestPlaceholders.VIVO_APPID = ""
            manifestPlaceholders.VIVO_APPKEY = ""
            manifestPlaceholders.HONOR_APPID = ""
            manifestPlaceholders.APP_TCP_SSL = ""
            manifestPlaceholders.APP_DEBUG = ""
            manifestPlaceholders.COUNTRY_CODE = ""
            resValue "string", "app_name", "tempa"
            // Use flavor-specific signing config
            signingConfig signingConfigs.tempaRelease
        }
        bszb {
            dimension "flavor-type"
            applicationId "com.tempd.stock"
            manifestPlaceholders.appScheme = "vhtdvx"
            manifestPlaceholders.OPENINSTALL_APPKEY = "vhtdvx"
            manifestPlaceholders.ENGAGELAB_PRIVATES_APPKEY = ""
            manifestPlaceholders.ENGAGELAB_PRIVATES_CHANNEL = "developer"
            manifestPlaceholders.ENGAGELAB_PRIVATES_PROCESS = ":remote"
            manifestPlaceholders.XIAOMI_APPID = ""
            manifestPlaceholders.XIAOMI_APPKEY = ""
            manifestPlaceholders.MEIZU_APPID = ""
            manifestPlaceholders.MEIZU_APPKEY = ""
            manifestPlaceholders.OPPO_APPID = ""
            manifestPlaceholders.OPPO_APPKEY = ""
            manifestPlaceholders.OPPO_APPSECRET = ""
            manifestPlaceholders.VIVO_APPID = ""
            manifestPlaceholders.VIVO_APPKEY = ""
            manifestPlaceholders.HONOR_APPID = ""
            manifestPlaceholders.APP_TCP_SSL = ""
            manifestPlaceholders.APP_DEBUG = ""
            manifestPlaceholders.COUNTRY_CODE = ""
            resValue "string", "app_name", "宝石资本"
            // Use flavor-specific signing config
            signingConfig signingConfigs.bszbRelease
        }
        dyzb {
            dimension "flavor-type"
            applicationId "com.dyzb.stock"
            manifestPlaceholders.appScheme = "isijsu"
            manifestPlaceholders.OPENINSTALL_APPKEY = "isijsu"
            manifestPlaceholders.ENGAGELAB_PRIVATES_APPKEY = ""
            manifestPlaceholders.ENGAGELAB_PRIVATES_CHANNEL = "developer"
            manifestPlaceholders.ENGAGELAB_PRIVATES_PROCESS = ":remote"
            manifestPlaceholders.XIAOMI_APPID = ""
            manifestPlaceholders.XIAOMI_APPKEY = ""
            manifestPlaceholders.MEIZU_APPID = ""
            manifestPlaceholders.MEIZU_APPKEY = ""
            manifestPlaceholders.OPPO_APPID = ""
            manifestPlaceholders.OPPO_APPKEY = ""
            manifestPlaceholders.OPPO_APPSECRET = ""
            manifestPlaceholders.VIVO_APPID = ""
            manifestPlaceholders.VIVO_APPKEY = ""
            manifestPlaceholders.HONOR_APPID = ""
            manifestPlaceholders.APP_TCP_SSL = ""
            manifestPlaceholders.APP_DEBUG = ""
            manifestPlaceholders.COUNTRY_CODE = ""
            resValue "string", "app_name", "德盈资本"
            // Use flavor-specific signing config
            signingConfig signingConfigs.dyzbRelease
        }
        xyzq {
            dimension "flavor-type"
            applicationId "com.xyzq.stock"
            manifestPlaceholders.appScheme = "fdh37v"
            manifestPlaceholders.OPENINSTALL_APPKEY = "fdh37v"
            manifestPlaceholders.ENGAGELAB_PRIVATES_APPKEY = ""
            manifestPlaceholders.ENGAGELAB_PRIVATES_CHANNEL = "developer"
            manifestPlaceholders.ENGAGELAB_PRIVATES_PROCESS = ":remote"
            manifestPlaceholders.XIAOMI_APPID = ""
            manifestPlaceholders.XIAOMI_APPKEY = ""
            manifestPlaceholders.MEIZU_APPID = ""
            manifestPlaceholders.MEIZU_APPKEY = ""
            manifestPlaceholders.OPPO_APPID = ""
            manifestPlaceholders.OPPO_APPKEY = ""
            manifestPlaceholders.OPPO_APPSECRET = ""
            manifestPlaceholders.VIVO_APPID = ""
            manifestPlaceholders.VIVO_APPKEY = ""
            manifestPlaceholders.HONOR_APPID = ""
            manifestPlaceholders.APP_TCP_SSL = ""
            manifestPlaceholders.APP_DEBUG = ""
            manifestPlaceholders.COUNTRY_CODE = ""
            resValue "string", "app_name", "鑫元優策略"
            // Use flavor-specific signing config
            signingConfig signingConfigs.xyzqRelease
        }
    }
}