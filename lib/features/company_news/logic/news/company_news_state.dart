import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';

import '../../../../shared/constants/enums.dart';

class CompanyNewsDetailsState extends Equatable {
  final DataStatus status;
  final CompanyNewsEntity? newsDetails;
  final String? error;

  const CompanyNewsDetailsState({
    this.status = DataStatus.idle,
    this.newsDetails,
    this.error,
  });

  @override
  List<Object?> get props => [status, newsDetails, error];

  CompanyNewsDetailsState copyWith({
    DataStatus? status,
    CompanyNewsEntity? newsDetails,
    String? error,
  }) {
    return CompanyNewsDetailsState(
      status: status ?? this.status,
      newsDetails: newsDetails ?? this.newsDetails,
      error: error ?? this.error,
    );
  }
}
