import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/f_trade_k_line_scroll_view.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/logic/f_trade_center_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_profile/logic/t_trade_profile_scroll_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_profile/t_trade_profile_scroll_view.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

/// 行情 简况 咨询 3和1 页面
class FTrade3In1SectionScreen extends StatefulWidget {
  final FuturesMarketType type;
  final FTradeListItemModel data;
  final void Function(int) onChangeAllInfoScreenTitlesAction;
  const FTrade3In1SectionScreen({
    super.key,
    required this.type,
    required this.data,
    required this.onChangeAllInfoScreenTitlesAction,
  });

  @override
  State<FTrade3In1SectionScreen> createState() => _FTrade3In1SectionScreenState();
}

class _FTrade3In1SectionScreenState extends State<FTrade3In1SectionScreen> {
  final titles = ['tabMarket', 'tabProfile'];
  int _currentTabIndex = 0;
  bool _marketModuleAttached = false;
  bool _profileModuleAttached = false;

  @override
  Widget build(BuildContext context) {
    final tabData = titles.map((title) => title.tr()).toList();

    return Column(
      children: [
        Container(
          height: 40.gw,
          decoration: BoxDecoration(color: context.theme.scaffoldBackgroundColor),
          child: CommonTabBar.withAutoKey(
            tabData,
            currentIndex: _currentTabIndex,
            onTap: (index) {
              setState(() {
                _currentTabIndex = index;
              });
            },
            style: CommonTabBarStyle.line,
            isScrollable: false,
            backgroundColor: context.theme.scaffoldBackgroundColor,
            height: 44.gw,
          ),
        ),
        Expanded(
          child: IndexedStack(
            index: _currentTabIndex,
            children: [
              // 行情标签页 / Market tab
              // FTradeKLineCubit 已在顶层创建，这里只提供 WatchListCubit
              // FTradeKLineCubit is created at top level, only provide WatchListCubit here
              BlocProvider<WatchListCubit>.value(
                value: context.read<WatchListCubit>(),
                child: BlocSelector<FTradeKLineCubit, FTradeKLineState, String>(
                  selector: (state) => state.fTradeInfoModel?.currency ?? '',
                  builder: (marketContext, currency) {
                    _attachMarketModules(marketContext);
                    return FTradeKLineScrollView(
                      type: widget.type,
                      data: widget.data,
                      currency: currency,
                      onChangeAllInfoScreenTitlesAction: widget.onChangeAllInfoScreenTitlesAction,
                    );
                  },
                ),
              ),
              // 资料标签页 / Profile tab
              BlocProvider(
                create: (_) => FTradeProfileScrollCubit(scope: widget.type.scope)..fetchData(instrument: widget.data.makeInstrument()),
                child: Builder(
                  builder: (profileContext) {
                    _attachProfileModule(profileContext);
                    return BlocBuilder<FTradeProfileScrollCubit, FTradeProfileScrollState>(
                      builder: (context, state) {
                        return FTradeProfileScrollView(
                          type: widget.type,
                          data: widget.data,
                          displayList: state.makeDisplayList(),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _attachMarketModules(BuildContext marketContext) {
    if (_marketModuleAttached) return;
    _marketModuleAttached = true;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      marketContext.read<FTradeCenterCubit>().attachQuotationModules(
            kLineCubit: marketContext.read<FTradeKLineCubit>(),
          );
    });
  }

  void _attachProfileModule(BuildContext profileContext) {
    if (_profileModuleAttached) return;
    _profileModuleAttached = true;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      profileContext.read<FTradeCenterCubit>().attachQuotationModules(
            profileCubit: profileContext.read<FTradeProfileScrollCubit>(),
          );
    });
  }

  @override
  void dispose() {
    context.read<FTradeCenterCubit>().detachQuotationModules();
    super.dispose();
  }
}
