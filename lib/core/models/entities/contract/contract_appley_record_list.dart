import 'package:gp_stock_app/core/utils/contract_utils.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/contract_appley_record_list.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/contract_appley_record_list.g.dart';

@JsonSerializable()
class ContractAppleyRecordList {
	/// 当前页码
	final int current;
	/// 是否包含下一页，true:包含，false:不包含
	final bool hasNext;
	/// 数据列表
	final List<ContractAppleyRecord> records;
	/// 总数量
	final int total;

	const ContractAppleyRecordList({
		this.current = 0,
		this.hasNext = false,
		this.records = const [],
		this.total = 0,
	});

	factory ContractAppleyRecordList.fromJson(Map<String, dynamic> json) => $ContractAppleyRecordListFromJson(json);

	Map<String, dynamic> toJson() => $ContractAppleyRecordListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractAppleyRecord {
	/// 审核理由
	final String auditReason;
	/// 审核状态 0：待审核 1：审核成功 2：审核失败
	final int auditStatus;
	/// 审核时间
	final dynamic auditTime;
	/// 申请时间
	final String createTime;
	/// 唯一标识
	final int id;
	/// 市场
	final String marketType;
	/// 倍数
	final int multiple;
	/// 周期类型 1：按日 2：按周 3：按月 4：免息
	final int periodType;
	/// 保证金
	final int totalCash;
	/// 总操盘资金
	final int totalPower;
	/// 合约类型 1：普通 2：体验 3：彩金
	final int type;

	const ContractAppleyRecord({
		this.auditReason = '',
		this.auditStatus = 0,
		this.auditTime,
		this.createTime = '',
		this.id = 0,
		this.marketType = '',
		this.multiple = 0,
		this.periodType = 0,
		this.totalCash = 0,
		this.totalPower = 0,
		this.type = 0,
	});

	factory ContractAppleyRecord.fromJson(Map<String, dynamic> json) => $ContractAppleyRecordFromJson(json);

	Map<String, dynamic> toJson() => $ContractAppleyRecordToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

extension ContractAppleyRecordExtension on ContractAppleyRecord {
	/// 合约标签
	String get contractLabel => ContractUtils.buildLabel(
		id: id,
		type: type,
		periodType: periodType,
		marketType: marketType,
		multiple: multiple,
	);
}


@JsonSerializable()
class ContractAvailableType {

	List<int> list = [];

	ContractAvailableType();

	factory ContractAvailableType.fromJson(Map<String, dynamic> json) => $ContractAvailableTypeFromJson(json);

	Map<String, dynamic> toJson() => $ContractAvailableTypeToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}