import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/account/account.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_normal_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_trial_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract_appley_record_list.dart';
import 'package:gp_stock_app/core/models/entities/wallet/funds_record_list.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

class ContractApi {
  /// 获取合约列表 isSettlement：是否已结算
  static Future<ContractListEntity?> fetchContractSummary({int page = 1, bool isSettlement = false}) async {
    final res = await Http().request<ContractListEntity>(
      ApiEndpoints.getContractSummaryPage,
      method: HttpMethod.get,
      queryParameters: {
        'settlementStatus': isSettlement ? 2 : 1,
        'pageNumber': page,
        'pageSize': 50,
      },
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取合约保证金详情
  static Future<ContractMarginEntity?> fetchContractMarginDetail({required int contractId}) async {
    final res = await Http().request<ContractMarginEntity>(
      '${ApiEndpoints.getContractAccountDetail}/$contractId',
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data;
  }

  /// 追加保证金
  static Future<bool> operateExpandMargin({
    required int contractId,
    required double applyAmount,
    required int type,
  }) async {
    final res = await Http().request(
      ApiEndpoints.addExpandMargin,
      needShowToast: false,
      params: {
        'contractId': contractId,
        'applyAmount': applyAmount,
        'type': type,
      },
    );
    return res.isSuccess;
  }

  static Future<bool> operateRenewalContract({required int contractId}) async {
    final res = await Http().request(
      ApiEndpoints.renewalContract,
      params: {'contractId': contractId},
      needShowToast: false,
    );
    return res.isSuccess;
  }

  /// 获取现货订单详情
  static Future<PositionEntity?> fetchPositionDetail({required int id}) async {
    final res = await Http().request<PositionEntity>(
      '${ApiEndpoints.getPositionDetail}/$id',
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取单个合约的持仓信息
  static Future<Contract?> fetchContractSummaryDetail({required int contractId}) async {
    // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/getContractSummaryUsingGET_1
    final res = await Http().request<Contract>(
      '${ApiEndpoints.getContractSummary}/$contractId',
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取单个合约的持仓信息
  static Future<ContractAppleyRecordList?> fetchContractApplyList({required int pageNum}) async {
    // https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/pageUsingGET_5
    final res = await Http().request<ContractAppleyRecordList>(
      ApiEndpoints.getContractApplyRecords,
      method: HttpMethod.get,
      queryParameters: {
        'pageNumber': pageNum,
        'pageSize': 20,
      },
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取合约提盈config
  static Future<ProfitWithdrawalConfigEntity?> fetchWithdrawalConfig({required int contractId}) async {
    final res = await Http().request<ProfitWithdrawalConfigEntity>(
      '${ApiEndpoints.getWithdrawAmount}/$contractId',
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取 提交提现申请
  static Future<bool> submitWithdraw({required String applyAmount, required int id}) async {
    final res = await Http().request(
      ApiEndpoints.contractWithdraw,
      params: {
        'applyAmount': applyAmount,
        'id': id,
      },
    );
    return res.isSuccess;
  }

  static Future<ApplyTrialContractConfigEntity?> fetchTrialContractConfig({
    required int type,
    required int parentType,
  }) async {
    final res = await Http().request<ApplyTrialContractConfigEntity>(
      ApiEndpoints.getContractActivity,
      method: HttpMethod.get,
      queryParameters: {
        'type': type,
        'parentType': parentType,
      },
      needShowToast: false,
    );
    return res.data;
  }

  static Future<bool> applyTrialContract({
    required int activityId,
    required int activityRiskId,
    required double applyAmount,
    required int type,
    bool isBonus = false,
  }) async {
    final res = await Http().request(
      isBonus ? ApiEndpoints.applyBonusContract : ApiEndpoints.applyExperienceContract,
      params: {
        'activityId': activityId,
        'activityRiskId': activityRiskId,
        'applyAmount': applyAmount,
        'type': type,
      },
    );
    return res.isSuccess;
  }

  /// 获取彩金合约
  static Future<ContractApplyAmountEntity?> getBonusContractAmount({
    required int activityId,
    required int activityRiskId,
    required int applyAmount,
    required int type,
  }) async {
    final res = await Http().request<ContractApplyAmountEntity>(
      ApiEndpoints.getBonusContractAmount,
      method: HttpMethod.get,
      queryParameters: {
        'activityId': activityId,
        'activityRiskId': activityRiskId,
        'applyAmount': applyAmount,
        'type': type,
      },
      needShowToast: false,
    );
    return res.data;
  }

  /// 获取普通合约
  static Future<ContractApplyAmountEntity?> getNormalContractAmount({
    required int contractConfigId,
    required int riskId,
    required int applyAmount,
    required int type,
  }) async {
    final res = await Http().request<ContractApplyAmountEntity>(
      ApiEndpoints.getOrdinaryApplyAmount,
      method: HttpMethod.get,
      queryParameters: {
        'contractConfigId': contractConfigId,
        'riskId': riskId,
        'applyAmount': applyAmount,
        'type': type,
      },
      needShowToast: false,
    );
    return res.data;
  }

  /// 终止合约
  static Future<bool> terminateContract({required String contractId}) async {
    final res = await Http().request(
      ApiEndpoints.endContract,
      params: {'contractId': contractId},
    );
    return res.isSuccess;
  }

  /// 获取可开通的合约类型
  /// 返回值: 0-普通合约 1-彩金合约 2-体验合约
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%8E%B7%E5%8F%96%E5%90%88%E7%BA%A6%E9%85%8D%E7%BD%AE/getOpenContractTypeUsingGET_1
  static Future<List<int>?> fetchOpenContractTypes() async {
    final res = await Http().request<ContractAvailableType>(
      ApiEndpoints.getOpenContractType,
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data?.list;
  }

  /// 普通合约的申请页面配置
  /// type: 1.股票 2.期货
  /// https://h5.gphome.club/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%8E%B7%E5%8F%96%E5%90%88%E7%BA%A6%E9%85%8D%E7%BD%AE/getOrdinaryConfigInfoUsingGET_1
  static Future<ApplyNormalContractConfigEntity?> fetchApplyNormalContractConfig({required int type}) async {
    final res = await Http().request<ApplyNormalContractConfigEntity>(
      ApiEndpoints.getOrdinaryConfigInfo,
      method: HttpMethod.get,
      queryParameters: {
        'type': type,
      },
      needShowToast: false,
    );
    return res.data;
  }


  /// 申请普通合约
  static Future<bool> applyNormalContract({
    required int applyAmount,
    required int contractConfigId,
    required int riskId,
    required int type,
  }) async {
    final res = await Http().request(
      ApiEndpoints.applyOrdinaryContract,
      method: HttpMethod.post,
      params: {
        'applyAmount': applyAmount, // 申请金额
        'contractConfigId': contractConfigId, // 合约配置id，普通合约的时候必传
        'riskId': riskId, // 合约类型风控id
        'type': type, // 1.股票合约 2.期货合约
      },
    );
    return res.data ?? false;
  }

  /// 获取合约资金记录
  static Future<FundsRecordList?> fetchFundsRecordList({
    required int? contractId,
    required int pageNum,
  }) async {
    final response = await Http().request<FundsRecordList>(
      ApiEndpoints.getContractAccountRecord,
      method: HttpMethod.get,
      queryParameters: {
        'contractId': contractId,
        'pageNum': pageNum,
        'pageSize': 20,
      },
      needShowToast: false,
    );
    return response.data;
  }

}
