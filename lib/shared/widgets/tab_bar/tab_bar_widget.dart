import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/buttons/trapezoid_button.dart';

enum TabComponentBoxStyle {
  kPrimary,
  kSecondary,
}

class TabComponentBox extends StatelessWidget {
  final String label1;
  final String label2;
  final int selectedIndex;
  final Function onTap;
  final Widget? widget;
  final TabComponentBoxStyle style;

  const TabComponentBox({
    super.key,
    required this.label1,
    required this.label2,
    required this.selectedIndex,
    required this.onTap,
    this.widget,
    this.style = TabComponentBoxStyle.kPrimary,
  });

  @override
  Widget build(BuildContext context) {
    return switch (style) {
      TabComponentBoxStyle.kPrimary => _buildPrimaryWidget(context),
      TabComponentBoxStyle.kSecondary => _buildSecondaryWidget(context),
    };
  }

  Widget _buildPrimaryWidget(BuildContext context) {
    return Container(
      height: 25.gw,
      width: 180.gw,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        border: Border.all(color: context.theme.primaryColor),
        borderRadius: BorderRadius.circular(2.0),
      ),
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () {
                onTap(0);
              },
              child: Container(
                alignment: Alignment.center,
                color: selectedIndex == 0 ? context.theme.primaryColor : Colors.transparent,
                child: Text(
                  label1,
                  style: context.textTheme.primary.fs12.w600.copyWith(
                    color: selectedIndex == 0 ? Colors.white : context.theme.primaryColor,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: InkWell(
              onTap: () {
                onTap(1);
              },
              child: Container(
                alignment: Alignment.center,
                color: selectedIndex == 1 ? context.theme.primaryColor : Colors.transparent,
                child: Text(
                  label2,
                  style: context.textTheme.primary.fs12.w600.copyWith(
                    color: selectedIndex == 1 ? Colors.white : context.theme.primaryColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecondaryWidget(BuildContext context) {
    return SizedBox(
      height: 30.gw,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TrapezoidButton(
            title: label1,
            isSelected: selectedIndex == 0,
            isLeft: true,
            onTap: () => onTap(0),
          ),
          const SizedBox(width: 4),
          TrapezoidButton(
            title: label2,
            isSelected: selectedIndex == 1,
            isLeft: false,
            onTap: () => onTap(1),
          ),
        ],
      ),
    );
  }
}
