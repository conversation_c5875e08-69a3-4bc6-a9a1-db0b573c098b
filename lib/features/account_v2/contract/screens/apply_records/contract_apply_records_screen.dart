import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account_v2/contract/widgets/contract_apply_record_cell.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'contract_apply_records_cubit.dart';

class ContractApplyRecordsScreen extends StatefulWidget {
  const ContractApplyRecordsScreen({super.key});

  @override
  State<ContractApplyRecordsScreen> createState() => _ContractApplyRecordsScreenState();
}

class _ContractApplyRecordsScreenState extends State<ContractApplyRecordsScreen> {
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('apply_records'.tr()),
        surfaceTintColor: Colors.transparent,
      ),
      body: BlocConsumer<ContractApplyRecordsCubit, ContractApplyRecordsState>(
        listener: (context, state) {
          if (state.fetchStatus == DataStatus.success) {
            _refreshController.refreshCompleted();
            if (state.hasReachedMax) {
              _refreshController.loadNoData();
            } else {
              _refreshController.loadComplete();
            }
          } else if (state.fetchStatus == DataStatus.failed) {
            _refreshController.refreshFailed();
            _refreshController.loadFailed();
          }
        },
        builder: (context, state) {
          if ((state.fetchStatus == DataStatus.loading || state.fetchStatus == DataStatus.idle) &&
              state.records.isEmpty) {
            return _ApplyContractsShimmer();
          }

          if (state.fetchStatus == DataStatus.failed && state.records.isEmpty) {
            return Center(child: Text('loadFailed'.tr()));
          }

          return CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: state.hasNext,
            onRefresh: () => context.read<ContractApplyRecordsCubit>().refresh(),
            onLoading: () => context.read<ContractApplyRecordsCubit>().loadMore(),
            child: state.records.isNotEmpty
                ? AnimationLimiter(
                    child: ListView.separated(
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: EdgeInsets.fromLTRB(12, 12, 12, 24),
                      itemBuilder: (context, index) {
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 600),
                          child: SlideAnimation(
                            verticalOffset: 50.0,
                            child: FadeInAnimation(
                              child: ContractApplyRecordCell(record: state.records[index]),
                            ),
                          ),
                        );
                      },
                      itemCount: state.records.length,
                      separatorBuilder: (context, index) => 10.verticalSpace,
                    ),
                  )
                : Center(child: TableEmptyWidget(height: 60.gw, width: 60.gw)),
          );
        },
      ),
    );
  }
}

class _ApplyContractsShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: EdgeInsets.symmetric(horizontal: 10.gw),
      itemBuilder: (context, index) => ShimmerWidget(
        radius: 12.gr,
        height: 90.gw,
        width: double.infinity,
      ),
      separatorBuilder: (context, index) => 10.verticalSpace,
      itemCount: 6,
    );
  }
}
