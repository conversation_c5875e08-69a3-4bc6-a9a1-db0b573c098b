// lib/core/services/im_url_service_impl.dart

import 'package:gp_stock_app/core/api/network/endpoint/urls.dart';
import 'package:gp_stock_app/core/utils/host_util/host_util.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services/im_url_service.dart';

class AppIMUrlService implements IMUrlService {
  @override
  String getUploadUrl() {
    return HostUtil().currentHost ?? Urls.baseUrl;
  }
}
