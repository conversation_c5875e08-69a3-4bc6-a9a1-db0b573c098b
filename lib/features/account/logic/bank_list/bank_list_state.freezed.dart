// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_list_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BankListState {
  DataStatus get bankListFetchStatus => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  List<BankEntity>? get bankList => throw _privateConstructorUsedError;

  /// Create a copy of BankListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BankListStateCopyWith<BankListState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BankListStateCopyWith<$Res> {
  factory $BankListStateCopyWith(
          BankListState value, $Res Function(BankListState) then) =
      _$BankListStateCopyWithImpl<$Res, BankListState>;
  @useResult
  $Res call(
      {DataStatus bankListFetchStatus,
      String? error,
      List<BankEntity>? bankList});
}

/// @nodoc
class _$BankListStateCopyWithImpl<$Res, $Val extends BankListState>
    implements $BankListStateCopyWith<$Res> {
  _$BankListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BankListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankListFetchStatus = null,
    Object? error = freezed,
    Object? bankList = freezed,
  }) {
    return _then(_value.copyWith(
      bankListFetchStatus: null == bankListFetchStatus
          ? _value.bankListFetchStatus
          : bankListFetchStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      bankList: freezed == bankList
          ? _value.bankList
          : bankList // ignore: cast_nullable_to_non_nullable
              as List<BankEntity>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BankListStateImplCopyWith<$Res>
    implements $BankListStateCopyWith<$Res> {
  factory _$$BankListStateImplCopyWith(
          _$BankListStateImpl value, $Res Function(_$BankListStateImpl) then) =
      __$$BankListStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DataStatus bankListFetchStatus,
      String? error,
      List<BankEntity>? bankList});
}

/// @nodoc
class __$$BankListStateImplCopyWithImpl<$Res>
    extends _$BankListStateCopyWithImpl<$Res, _$BankListStateImpl>
    implements _$$BankListStateImplCopyWith<$Res> {
  __$$BankListStateImplCopyWithImpl(
      _$BankListStateImpl _value, $Res Function(_$BankListStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankListFetchStatus = null,
    Object? error = freezed,
    Object? bankList = freezed,
  }) {
    return _then(_$BankListStateImpl(
      bankListFetchStatus: null == bankListFetchStatus
          ? _value.bankListFetchStatus
          : bankListFetchStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      bankList: freezed == bankList
          ? _value._bankList
          : bankList // ignore: cast_nullable_to_non_nullable
              as List<BankEntity>?,
    ));
  }
}

/// @nodoc

class _$BankListStateImpl implements _BankListState {
  const _$BankListStateImpl(
      {this.bankListFetchStatus = DataStatus.idle,
      this.error,
      final List<BankEntity>? bankList})
      : _bankList = bankList;

  @override
  @JsonKey()
  final DataStatus bankListFetchStatus;
  @override
  final String? error;
  final List<BankEntity>? _bankList;
  @override
  List<BankEntity>? get bankList {
    final value = _bankList;
    if (value == null) return null;
    if (_bankList is EqualUnmodifiableListView) return _bankList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'BankListState(bankListFetchStatus: $bankListFetchStatus, error: $error, bankList: $bankList)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankListStateImpl &&
            (identical(other.bankListFetchStatus, bankListFetchStatus) ||
                other.bankListFetchStatus == bankListFetchStatus) &&
            (identical(other.error, error) || other.error == error) &&
            const DeepCollectionEquality().equals(other._bankList, _bankList));
  }

  @override
  int get hashCode => Object.hash(runtimeType, bankListFetchStatus, error,
      const DeepCollectionEquality().hash(_bankList));

  /// Create a copy of BankListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankListStateImplCopyWith<_$BankListStateImpl> get copyWith =>
      __$$BankListStateImplCopyWithImpl<_$BankListStateImpl>(this, _$identity);
}

abstract class _BankListState implements BankListState {
  const factory _BankListState(
      {final DataStatus bankListFetchStatus,
      final String? error,
      final List<BankEntity>? bankList}) = _$BankListStateImpl;

  @override
  DataStatus get bankListFetchStatus;
  @override
  String? get error;
  @override
  List<BankEntity>? get bankList;

  /// Create a copy of BankListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankListStateImplCopyWith<_$BankListStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
