import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/features/account_v2/spot/sub_screen/account_spot_detail_screen.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import '../0_home/account_screen_cubit_v2.dart';
import '../0_home/account_screen_state_v2.dart';
import '../0_home/domain/view_models/market_category_state.dart';

/// 现货账户
class AccountSpotScreen extends StatelessWidget {
  const AccountSpotScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AccountScreenCubitV2>();
    return BlocSelector<AccountScreenCubitV2, AccountScreenStateV2, ({List<MarketCategoryState> list, int index})>(
      selector: (state) => (list: state.spotViewModels, index: state.spotScreenCurrentIndex),
      builder: (context, state) {
        if (state.list.isEmpty) return SizedBox.shrink();

        return Column(
          children: [
            CommonTabBar.withAutoKey(
              state.list.map((e) => tr(e.category.nameKey)).toList(),
              currentIndex: state.index,
              onTap: (tabIndex) async {
                cubit.updateSpotScreenIndex(tabIndex);
                // fetchSpotScreenCurrentData 内部已包含 fetchSpotAccountInfo 和所有订单列表的刷新
                cubit.fetchSpotScreenCurrentData(forceRefresh: true);
              },
              style: CommonTabBarStyle.line,
              padding: EdgeInsets.only(left: 8.gw),
              isScrollable: true,
            ),
            SizedBox(height: 10.gw),
            Expanded(
                child: DirectSlideView(
              pages: state.list.map((e) {
                return AccountSpotDetailScreen(viewModel: e);
              }).toList(),
              pageIndex: state.index,
              onPageChanged: (int pageIndex) => cubit.updateSpotScreenIndex(pageIndex),
            ))
          ],
        );
      },
    );
  }
}
