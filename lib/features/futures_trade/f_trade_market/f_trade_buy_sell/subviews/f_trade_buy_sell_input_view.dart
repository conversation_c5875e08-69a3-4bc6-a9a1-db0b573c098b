import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/stepper_text_field.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trade/trading_form_section/0_direction/trade_direction_button.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/logic/f_trade_buy_sell_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/subviews.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class FTradeBuySellInputView extends StatefulWidget {
  final double? accountUsableCash;
  final double latestPrice;
  final FTradeConfigModel? fTradeConfigModel;
  final UserInputController userInputState;
  final void Function(UserInputController oldFTradeUserInputController) onUserInputControllerChanged;
  final FtradeLongShortActionsController buyActionsController;
  final FtradeLongShortActionsController sellActionsController;

  const FTradeBuySellInputView({
    super.key,
    this.accountUsableCash,
    required this.latestPrice,
    required this.fTradeConfigModel,
    required this.userInputState,
    required this.onUserInputControllerChanged,
    required this.buyActionsController,
    required this.sellActionsController,
  });

  @override
  State<FTradeBuySellInputView> createState() => _FTradeBuySellInputViewState();
}

class _FTradeBuySellInputViewState extends State<FTradeBuySellInputView> {
  /// 选中的杠杆倍数 (leverage multiplier)
  int? selectedLeverageMultiple;

  @override
  void initState() {
    super.initState();
  }

  int _getQuantityDecimalDigits() {
    final minQty = widget.fTradeConfigModel?.minTradeQuantity ?? 0;
    if (minQty == 0) return 0;
    final str = minQty.toStringAsFixed(8).replaceFirst(RegExp(r'0+$'), '').replaceFirst(RegExp(r'\.$'), '');
    final idx = str.indexOf('.');
    if (idx == -1) return 0;
    return str.length - idx - 1;
  }

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: Column(
          children: AnimationConfiguration.toStaggeredList(
        duration: const Duration(milliseconds: 400),
        childAnimationBuilder: (widget) => SlideAnimation(
          horizontalOffset: 50.0,
          child: FadeInAnimation(
            child: widget,
          ),
        ),
        children: [
          _buildFrom(context),
          FtradeLongShortActionsView(
            longActionsController: widget.buyActionsController,
            shortActionsController: widget.sellActionsController,
          )
        ],
      )),
    );
  }

  Widget _buildFrom(BuildContext context) {
    var userInputState = widget.userInputState;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      spacing: 8,
      children: [
        _buildTradeDirectionSection(context: context),
        Row(
          spacing: 8,
          children: [
            Expanded(
              child: _buildLeverageMultiplierDropdown(context),
            ),
            Expanded(
              child: userInputState.priceType == OrderPriceType.market
                  ? Container(
                      height: 35.gw,
                      padding: EdgeInsets.symmetric(horizontal: 12.gw),
                      decoration: BoxDecoration(
                        color: context.theme.inputDecorationTheme.fillColor,
                        borderRadius: BorderRadius.circular(5.gr),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'realTimePrice'.tr(),
                            style: context.textTheme.primary,
                          ),
                        ],
                      ),
                    )
                  : StepperTextField(
                      height: 35.gw,
                      decimalDigits: 3,
                      displayValue: userInputState.price < 0 ? '' : userInputState.price.toStringAsFixed(3),
                      onIncrement: () {
                        double currentValue = userInputState.price + 0.001;
                        if (currentValue > 99999999) {
                          currentValue = 99999999;
                        }
                        widget.onUserInputControllerChanged(userInputState.copyWith(
                          price: currentValue,
                          inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                          orderFraction: null,
                          priceHasFocus: true,
                        ));
                      },
                      onDecrement: () {
                        double currentValue = userInputState.price - 0.001;
                        if (currentValue <= 0) {
                          currentValue = 0;
                        }
                        widget.onUserInputControllerChanged(userInputState.copyWith(
                          price: currentValue,
                          inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                          orderFraction: null,
                          priceHasFocus: true,
                        ));
                      },
                      onValueChanged: (value) {
                        final parsed = double.tryParse(value);
                        widget.onUserInputControllerChanged(userInputState.copyWith(
                          price: parsed ?? userInputState.price,
                          inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                          orderFraction: null,
                          priceHasFocus: true,
                        ));
                      },
                      onFocusChanged: (value) {
                        double currentValue = double.tryParse(value) ?? 0.0;
                        if (currentValue <= 0) {
                          currentValue = 0;
                        } else if (currentValue > 99999999) {
                          currentValue = 99999999;
                        }
                        widget.onUserInputControllerChanged(userInputState.copyWith(
                          price: currentValue,
                          inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                          orderFraction: null,
                          priceHasFocus: false,
                        ));
                        return currentValue.toStringAsFixed(3);
                      },
                    ),
            ),
          ],
        ),
        Row(
          spacing: 8,
          children: [
            Expanded(
              child: CommonDropdown(
                height: 35.gw,
                showSearchBox: false,
                isEnabled: widget.fTradeConfigModel == null ? false : true,
                selectedItem: userInputState.priceType == OrderPriceType.market
                    ? DropDownValue(id: 'marketOrder', value: 'marketOrder'.tr())
                    : DropDownValue(id: 'limitOrder', value: 'limitOrder'.tr()),
                dropDownValue: [
                  DropDownValue(id: 'marketOrder', value: 'marketOrder'.tr()),
                  DropDownValue(id: 'limitOrder', value: 'limitOrder'.tr()),
                ],
                onChanged: (value) {
                  if (value.id == 'marketOrder') {
                    final temp = userInputState.copyWith(
                      priceType: OrderPriceType.market,
                      inputNumberEnum: FTradeBuySellUserInputNumberEnum.init,
                    );
                    widget.onUserInputControllerChanged(temp);
                  }
                  if (value.id == 'limitOrder') {
                    final temp = userInputState.copyWith(
                      priceType: OrderPriceType.limit,
                      inputNumberEnum: FTradeBuySellUserInputNumberEnum.init,
                    );
                    widget.onUserInputControllerChanged(temp);
                  }
                },
                hintText: 'trading_methods'.tr(),
                borderRadius: 5.gr,
                textStyle: context.textTheme.regular.fs13.copyWith(color: context.colorTheme.textPrimary),
              ),
            ),
            if (widget.fTradeConfigModel == null || userInputState.number == -1)
              Expanded(
                child: Container(
                  height: 35.gw,
                  padding: EdgeInsets.symmetric(horizontal: 12.gw),
                  decoration: BoxDecoration(
                    color: context.theme.scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(5.gr),
                  ),
                ),
              ),
            // 网络延迟导致服务器数据不足 会先计算出0 然后计算出正确值 导致闪烁
            if (widget.latestPrice != 0 && widget.fTradeConfigModel != null && userInputState.number != -1)
              Expanded(
                child: StepperTextField(
                  height: 35.gw,
                  decimalDigits: _getQuantityDecimalDigits(),
                  displayValue: userInputState.number < 0 ? '' : userInputState.number.toStringAsFixed(_getQuantityDecimalDigits()),
                  canDecrement: userInputState.canNumberDecrement,
                  canIncrement: userInputState.canNumberIncrement,
                  onDecrement: () {
                    double currentValue = userInputState.number - (widget.fTradeConfigModel?.minTradeQuantity ?? 0);
                    widget.onUserInputControllerChanged(userInputState.copyWith(
                      inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                      orderFraction: null,
                      number: currentValue,
                      numberHasFocus: true,
                    ));
                  },
                  onIncrement: () {
                    double currentValue = userInputState.number + (widget.fTradeConfigModel?.minTradeQuantity ?? 0);
                    widget.onUserInputControllerChanged(userInputState.copyWith(
                      inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                      orderFraction: null,
                      number: currentValue,
                      numberHasFocus: true,
                    ));
                  },
                  onValueChanged: (value) {
                    final parsed = double.tryParse(value);
                    widget.onUserInputControllerChanged(userInputState.copyWith(
                      inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                      orderFraction: null,
                      number: parsed ?? userInputState.number,
                      numberHasFocus: true,
                    ));
                  },
                  onFocusChanged: (value) {
                    double currentValue = double.tryParse(value) ?? 0.0;
                    final minQty = widget.fTradeConfigModel?.minTradeQuantity ?? 0;
                    final maxQty = widget.fTradeConfigModel?.maxTradeQuantity ?? double.infinity;
                    if (currentValue <= minQty && minQty > 0) {
                      currentValue = minQty;
                    } else if (currentValue > maxQty) {
                      currentValue = maxQty;
                    }
                    widget.onUserInputControllerChanged(userInputState.copyWith(
                      inputNumberEnum: FTradeBuySellUserInputNumberEnum.input,
                      orderFraction: null,
                      number: currentValue,
                      numberHasFocus: false,
                    ));
                    final digits = _getQuantityDecimalDigits();
                    return currentValue.toStringAsFixed(digits);
                  },
                ),
              ),
          ],
        ),
        _BalanceLabel(balance: widget.accountUsableCash, currency: null),
        _FractionSection(
          selectedFraction: userInputState.orderFraction,
          onFractionSelected: (selected) {
            widget.onUserInputControllerChanged(userInputState.copyWith(
              inputNumberEnum: FTradeBuySellUserInputNumberEnum.selected,
              orderFraction: selected,
            ));
          },
        ),
      ],
    );
  }

  /// Build leverage multiplier dropdown
  Widget _buildLeverageMultiplierDropdown(BuildContext context) {
    final cubit = context.read<FTradeBuySellCubit>();
    final config = cubit.state.fTradeConfigModel;

    final availableMultiples = config?.multipleList.isNotEmpty == true
        ? config!.multipleList.map((e) => int.tryParse(e)).where((e) => e != null && e > 0).cast<int>().toList()
        : (config?.multiple != null && config!.multiple > 0 ? [config.multiple] : <int>[]);

    // Set default selected multiple
    if (selectedLeverageMultiple == null && availableMultiples.isNotEmpty) {
      selectedLeverageMultiple = availableMultiples.first;
    }

    final dropdownOptions = availableMultiples
        .map((multiple) => DropDownValue(
              id: multiple.toString(),
              value: '$multiple${'xTimes'.tr()}', // e.g., "30倍"
            ))
        .toList();

    return CommonDropdown(
      height: 35.gw,
      showSearchBox: false,
      isEnabled: dropdownOptions.isNotEmpty,
      selectedItem: dropdownOptions.isNotEmpty
          ? DropDownValue(
              id: selectedLeverageMultiple.toString(),
              value: '$selectedLeverageMultiple${'xTimes'.tr()}',
            )
          : null,
      dropDownValue: dropdownOptions,
      onChanged: (value) {
        final newMultiple = int.tryParse(value.id ?? '') ?? availableMultiples.first;
        setState(() {
          selectedLeverageMultiple = newMultiple;
        });
        cubit.onLeverageMultipleChanged(newMultiple);
      },
      hintText: 'leverage'.tr(),
      borderRadius: 5.gr,
      textStyle: context.textTheme.regular.fs13.copyWith(color: context.colorTheme.textPrimary),
    );
  }

  Widget _buildTradeDirectionSection({required BuildContext context}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('tradeDirection'.tr(), style: context.textTheme.primary),
        TradeDirectionButton(
          text: 'open'.tr(), // 开仓
          color: context.upColor,
          onPressed: () {}, // 空函数，不做任何操作
          isSelected: true, // 始终选中状态
        ),
      ],
    );
  }
}

/*
============================================================================================================================
BalanceLabel
============================================================================================================================
*/

class _BalanceLabel extends StatelessWidget {
  /// 余额
  final double? balance;

  /// 货币单位
  final String? currency;

  const _BalanceLabel({
    required this.balance,
    required this.currency,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'balance'.tr(),
              style: context.textTheme.regular,
            ),
            if (balance == null) ShimmerWidget(height: 18, width: 88),
            if (balance != null)
              AnimatedFlipCounter(
                thousandSeparator: ',',
                fractionDigits: 2,
                suffix: ' ${currency ?? 'CNY'}',
                textStyle: context.textTheme.primary.w700.ffAkz,
                value: balance!,
              )
          ],
        ),
      ],
    );
  }
}

/*
============================================================================================================================
FractionSection
============================================================================================================================
*/

class _FractionSection extends StatelessWidget {
  final OrderFraction? selectedFraction;
  final void Function(OrderFraction?) onFractionSelected;

  const _FractionSection({
    required this.selectedFraction,
    required this.onFractionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(OrderFraction.values.length, (index) {
        final fraction = OrderFraction.values[index];
        final isSelected = selectedFraction == fraction;
        return _FractionButton(
          fraction: fraction,
          isSelected: isSelected,
          onTap: () => onFractionSelected(isSelected ? null : fraction),
        );
      }),
    );
  }
}

class _FractionButton extends StatelessWidget {
  const _FractionButton({
    required this.fraction,
    required this.isSelected,
    required this.onTap,
  });

  final OrderFraction fraction;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 30.gw,
        width: 75.gw,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.gr),
          color: isSelected ? context.theme.primaryColor : context.theme.inputDecorationTheme.fillColor,
        ),
        child: Center(
          child: Text(
            fraction.translationKey?.tr() ?? fraction.label,
            style: context.textTheme.regular.copyWith(
              color: isSelected ? context.theme.cardColor : context.colorTheme.textPrimary,
            ),
          ),
        ),
      ),
    );
  }
}
