import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/funds_record_list.dart';

FundsRecordList $FundsRecordListFromJson(Map<String, dynamic> json) {
  return FundsRecordList(current: jsonConvert.convert<int>(json['current']) ?? 0,
      hasNext: jsonConvert.convert<bool>(json['hasNext']) ?? false,
      records: jsonConvert.convertListNotNull<FundsRecord>(json['records']) ?? <FundsRecord>[],
      total: jsonConvert.convert<int>(json['total']) ?? 0);
}

Map<String, dynamic> $FundsRecordListToJson(FundsRecordList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['hasNext'] = entity.hasNext;
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension FundsRecordListExtension on FundsRecordList {
  FundsRecordList copyWith({
    int? current,
    bool? hasNext,
    List<FundsRecord>? records,
    int? total,
  }) {
    return FundsRecordList(current: current ?? this.current,
        hasNext: hasNext ?? this.hasNext,
        records: records ?? this.records,
        total: total ?? this.total);
  }
}

FundsRecord $FundsRecordFromJson(Map<String, dynamic> json) {
  return FundsRecord(afterNum: jsonConvert.convert<double>(json['afterNum']) ?? 0.0,
      beforeNum: jsonConvert.convert<double>(json['beforeNum']) ?? 0.0,
      contractAccountId: jsonConvert.convert<int>(json['contractAccountId']) ?? 0,
      contractType: jsonConvert.convert<int>(json['contractType']) ?? 0,
      createTime: jsonConvert.convert<String>(json['createTime']) ?? '',
      currency: jsonConvert.convert<String>(json['currency']) ?? '',
      email: jsonConvert.convert<String>(json['email']) ?? '',
      fromType: jsonConvert.convert<int>(json['fromType']) ?? 0,
      id: jsonConvert.convert<int>(json['id']) ?? 0,
      marketType: jsonConvert.convert<String>(json['marketType']) ?? '',
      mobile: jsonConvert.convert<String>(json['mobile']) ?? '',
      multiple: jsonConvert.convert<String>(json['multiple']) ?? '',
      periodType: jsonConvert.convert<int>(json['periodType']) ?? 0,
      realName: jsonConvert.convert<String>(json['realName']) ?? '',
      refId: jsonConvert.convert<int>(json['refId']) ?? 0,
      serialNo: jsonConvert.convert<String>(json['serialNo']) ?? '',
      type: jsonConvert.convert<int>(json['type']) ?? 0,
      updateNum: jsonConvert.convert<double>(json['updateNum']) ?? 0.0,
      userId: jsonConvert.convert<int>(json['userId']) ?? 0,
      userType: jsonConvert.convert<int>(json['userType']) ?? 0);
}

Map<String, dynamic> $FundsRecordToJson(FundsRecord entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['afterNum'] = entity.afterNum;
  data['beforeNum'] = entity.beforeNum;
  data['contractAccountId'] = entity.contractAccountId;
  data['contractType'] = entity.contractType;
  data['createTime'] = entity.createTime;
  data['currency'] = entity.currency;
  data['email'] = entity.email;
  data['fromType'] = entity.fromType;
  data['id'] = entity.id;
  data['marketType'] = entity.marketType;
  data['mobile'] = entity.mobile;
  data['multiple'] = entity.multiple;
  data['periodType'] = entity.periodType;
  data['realName'] = entity.realName;
  data['refId'] = entity.refId;
  data['serialNo'] = entity.serialNo;
  data['type'] = entity.type;
  data['updateNum'] = entity.updateNum;
  data['userId'] = entity.userId;
  data['userType'] = entity.userType;
  return data;
}

extension FundsRecordExtension on FundsRecord {
  FundsRecord copyWith({
    double? afterNum,
    double? beforeNum,
    int? contractAccountId,
    int? contractType,
    String? createTime,
    String? currency,
    String? email,
    int? fromType,
    int? id,
    String? marketType,
    String? mobile,
    String? multiple,
    int? periodType,
    String? realName,
    int? refId,
    String? serialNo,
    int? type,
    double? updateNum,
    int? userId,
    int? userType,
  }) {
    return FundsRecord(afterNum: afterNum ?? this.afterNum,
        beforeNum: beforeNum ?? this.beforeNum,
        contractAccountId: contractAccountId ?? this.contractAccountId,
        contractType: contractType ?? this.contractType,
        createTime: createTime ?? this.createTime,
        currency: currency ?? this.currency,
        email: email ?? this.email,
        fromType: fromType ?? this.fromType,
        id: id ?? this.id,
        marketType: marketType ?? this.marketType,
        mobile: mobile ?? this.mobile,
        multiple: multiple ?? this.multiple,
        periodType: periodType ?? this.periodType,
        realName: realName ?? this.realName,
        refId: refId ?? this.refId,
        serialNo: serialNo ?? this.serialNo,
        type: type ?? this.type,
        updateNum: updateNum ?? this.updateNum,
        userId: userId ?? this.userId,
        userType: userType ?? this.userType);
  }
}