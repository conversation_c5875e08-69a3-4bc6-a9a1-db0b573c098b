import 'dart:async';
import 'dart:math' as math;

import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/market.dart';
import 'package:gp_stock_app/core/models/apis/trade.dart';
import 'package:gp_stock_app/core/models/entities/market/market_status_info.dart';
import 'package:gp_stock_app/core/models/entities/trade/stock_info_v2.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/web_socket_extension.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/web_socket_actions.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/models/web_socket_message.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_socket_interface.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown_v2.dart';

import '../trading_center_v2_state.dart';
import '../utils/balance_calculator.dart';
import 'trading_center_v2_core_mixin.dart';

/// 数据管理 Mixin
/// 负责数据获取、WebSocket订阅和余额计算
mixin TradingCenterV2DataManagementMixin on Cubit<TradingCenterV2State>, TradingCenterV2CoreMixin {
  // ========================================
  // #region WebSocket 管理 (from WsMixin)
  // ========================================

  WebSocketService get webSocketService;

  StreamSubscription<WebSocketMessage>? _stockInfoSubscription;
  StreamSubscription<WebSocketMessage>? _marketDepthSubscription;
  StreamSubscription<WebSocketMessage>? _tickDataSubscription;

  /// 初始化 WebSocket 订阅
  void initWsSubscriptions({required Security security}) {
    _stockInfoSubscription =
        webSocketService.onMessageWithAction('Q', loginRequired: true).listen(onWsQuote);
    _marketDepthSubscription =
        webSocketService.onMessageWithAction('O', loginRequired: true).listen(onWsDepth);
    _tickDataSubscription =
        webSocketService.onMessageWithAction('T', loginRequired: true).listen(onWsTick);
    subscribeToMarketSymbols(security: security);
  }

  /// 释放 WebSocket 订阅
  Future<void> disposeWsSubscriptions({required Security security}) async {
    try {
      await unsubscribeFromMarketSymbols(security: security);
    } finally {
      await Future.wait(
        [
          _stockInfoSubscription?.cancel() ?? Future.value(),
          _marketDepthSubscription?.cancel() ?? Future.value(),
          _tickDataSubscription?.cancel() ?? Future.value(),
        ],
        eagerError: false,
      );
    }
  }

  /// 构建 WebSocket 订阅符号字符串
  String buildSubscriptionSymbols({
    required Security security,
    List<String> actions = const ['Q', 'O', 'A', 'T'],
  }) {
    return actions
        .map((action) => [
              security.market,
              security.securityType,
              action,
              security.symbol,
            ].join('|'))
        .join(',');
  }

  /// 订阅标的的 WebSocket 消息
  Future<void> subscribeToMarketSymbols({
    required Security security,
    List<String> actions = const ['Q', 'O', 'A', 'T'],
  }) async {
    final symbols = buildSubscriptionSymbols(security: security, actions: actions);
    webSocketService.send({'action': SocketActions.subscribe, 'symbols': symbols});
  }

  /// 取消订阅标的的 WebSocket 消息
  Future<void> unsubscribeFromMarketSymbols({
    required Security security,
    List<String> actions = const ['Q', 'O', 'A', 'T'],
  }) async {
    final symbols = buildSubscriptionSymbols(security: security, actions: actions);
    webSocketService.send({'action': SocketActions.unsubscribe, 'symbols': symbols});
  }

  /// WS 消息校验：校验市场开盘、过滤当前标的
  WebSocketMessage? validateWebSocketMessage(
    WebSocketMessage message, {
    bool checkClosed = false,
    bool enableLogging = false,
  }) {
    if (checkClosed && isClosed) return null;
    final isCurrentMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(state.security.market);
    if (!isCurrentMarketOpen) return null;
    return message.withInstrument(state.security, enableLogging: enableLogging);
  }

  // 宿主需要实现的回调
  void onWsQuote(WebSocketMessage message);
  void onWsDepth(WebSocketMessage message);
  void onWsTick(WebSocketMessage message);

  // #endregion WebSocket 管理

  // ========================================
  // #region 数据获取 (from DataFetchingMixin)
  // ========================================

  /// 初始化交易账户
  void initializeTradeAccount(String? tradeAccountId);

  /// 获取股票详情和市场状态 / Fetch stock info and market status
  /// 失败或价格无效时会每2秒自动重试，直到成功
  Future<void> fetchStockInfo() async {
    if (state.stockInfoStatus == DataStatus.loading) return;

    emit(state.copyWith(stockInfoStatus: DataStatus.loading));

    // 自动重试直到成功 / Auto retry until success
    while (true) {
      if (isClosed) return; // Cubit已关闭，停止重试

      // 并行获取股票信息和市场状态 / Fetch stock info and market status in parallel
      final results = await Future.wait([
        TradeApi.fetchStockInfo(instrument: state.security.instrument),
        MarketApi.fetchMarketStatus(security: state.security),
      ]);

      // 检查cubit是否在await期间被关闭
      if (isClosed) return;

      final stockInfo = results[0] as StockInfoV2?;
      final marketStatus = results[1] as MarketStatus?;

      // 验证数据有效性：stockInfo存在且价格有效
      if (stockInfo != null && stockInfo.latestPrice > 0) {
        // 只有A股才保留涨跌停价格，其他市场设置为0和infinity（无限制）
        // Only A-Share keeps price limits, other markets set to 0 and infinity (no limit)
        final isAShare = state.security.marketCategory == MarketCategory.cnStocks;
        final updatedStockInfo = isAShare
            ? stockInfo
            : stockInfo.copyWith(
                priceDownLimited: 0,
                priceUpLimited: double.infinity,
              );

        // 在emit前再次检查cubit是否已关闭
        if (isClosed) return;

        emit(state.copyWith(
          stockInfoStatus: DataStatus.success,
          stockInfo: () => updatedStockInfo,
          marketStatus: () => marketStatus,
          tradeForm: state.tradeForm.copyWith(
            quantityStep: stockInfo.lotSize,
            pricePrecision: stockInfo.precision,
            priceTickSize: 1.0 / math.pow(10, stockInfo.precision),
          ),
        ));

        updateTradeSummaries();
        return; // 成功，停止重试
      }

      // 数据无效，等待2秒后重试
      LogD('⚠️ fetchStockInfo 失败，2秒后重试...\n'
          '  - instrument: ${state.security.instrument}\n'
          '  - stockInfo: ${stockInfo == null ? "null" : "存在"}\n'
          '  - latestPrice: ${stockInfo?.latestPrice}\n'
          '  - marketStatus: ${marketStatus == null ? "null" : "存在"}');
      await Future.delayed(const Duration(seconds: 2));

      // 延迟后检查cubit是否已关闭
      if (isClosed) return;
    }
  }

  /// 获取深度行情
  void fetchDepthQuote() async {
    if (state.depthQuoteStatus == DataStatus.loading) return;
    emit(state.copyWith(depthQuoteStatus: DataStatus.loading));
    final res = await TradeApi.fetchDepthQuote(instrument: state.security.instrument);

    // 检查cubit是否在await期间被关闭
    if (isClosed) return;

    if (res == null) {
      emit(state.copyWith(depthQuoteStatus: DataStatus.failed));
      return;
    }
    emit(state.copyWith(depthQuoteStatus: DataStatus.success, depthQuote: () => res));
  }

  /// 获取逐笔成交数据
  void fetchTickList({bool isLoadMore = false}) async {
    if (state.tickRecordStatus == DataStatus.loading) return;
    emit(state.copyWith(tickRecordStatus: DataStatus.loading));
    final pageNum = isLoadMore ? (state.tickRecordPageNum) + 1 : 1;
    final res = await TradeApi.fetchTickList(instrument: state.security.instrument, page: pageNum);

    // 检查cubit是否在await期间被关闭
    if (isClosed) return;

    if (res == null) {
      emit(state.copyWith(tickRecordStatus: DataStatus.failed));
      return;
    }

    // 过滤出有效的记录 / Filter valid records
    final tmpList = res.records.where((e) => e.isValid()).toList();

    final tickRecordList = pageNum == 1 ? tmpList : [...state.tickRecordList, ...tmpList];

    emit(state.copyWith(
      tickRecordStatus: DataStatus.success,
      tickRecordList: tickRecordList,
      tickRecordPageNum: pageNum,
    ));
  }

  /// 获取交易手续费配置
  Future<void> fetchTradeHandlingFeeConfig() async {
    if (state.handlingFeeConfigStatus == DataStatus.loading) return;
    final security = state.security;
    emit(state.copyWith(handlingFeeConfigStatus: DataStatus.loading));

    // 只获取开仓手续费配置 / Only fetch open position handling fee config
    final config = await TradeApi.fetchHandingFeeConfigList(
      market: security.market,
      direction: TradeDirection.open.value,
      securityType: security.securityType,
    );

    // 检查cubit是否在await期间被关闭
    if (isClosed) return;

    if (config == null) {
      emit(state.copyWith(handlingFeeConfigStatus: DataStatus.failed));
      return;
    }

    emit(state.copyWith(
      handlingFeeConfigStatus: DataStatus.success,
      handlingFeeConfigList: config,
    ));

    // 手续费配置加载完成后，重新计算交易汇总（确保手续费显示正确）
    updateTradeSummaries();
  }

  /// 根据「市场」和「是否指数交易」更新可用交易方法
  Future<void> updateAvailableTradeMethods({String? tradeAccountId}) async {
    final spotValue = DropDownValueV2(id: kDropDownValueSpotId, label: 'spotAccount'.tr());

    if (state.security.isIndexTrading) {
      // 股指交易只支持现货账户，固定设置为现货
      // Index trading only supports spot account, always set to spot
      emit(state.copyWith(tradeForm: state.tradeForm.copyWith(tradeAccountList: [spotValue])));
      initializeTradeAccount(kDropDownValueSpotId);
      return;
    }

    final accountScreenCubit = getIt<AccountScreenCubitV2>();
    var contractList = await accountScreenCubit.fetchContractList();
    if (contractList.isEmpty) contractList = accountScreenCubit.state.contractSummaryList;

    /// 从账户中心获取可用合约
    final contractValues = contractList
        .where((e) => e.marketType == state.security.marketCategory.marketType)
        .map((e) => DropDownValueV2(id: e.id.toString(), label: e.label));

    emit(state.copyWith(tradeForm: state.tradeForm.copyWith(tradeAccountList: [spotValue, ...contractValues])));

    // 账户列表准备好后，初始化交易账户
    final currentAccountId = tradeAccountId ?? state.tradeForm.currentTradeAccount?.id;
    initializeTradeAccount(currentAccountId);
  }

  // #endregion 数据获取

  // ========================================
  // #region 余额计算 (from BalanceCalculationMixin)
  // ========================================

  /// 判断是否为现货账户（统一逻辑）
  bool isSpotTradeAccount(DropDownValueV2? account) => BalanceCalculator.isSpotTradeAccount(account);

  /// 刷新账户余额 / Refresh account balance
  ///
  /// 根据当前账户类型自动选择刷新方式：
  /// - 现货账户：刷新用户账户信息（通过 BlocListener 自动同步）
  /// - 合约账户：刷新合约列表（通过 BlocListener 自动同步余额）
  ///
  /// [contractAccountId] 合约账户ID，null表示现货账户。
  ///   - 如果不传，则从 state.tradeForm 自动判断
  ///   - 如果传入，则直接使用传入值判断（用于TradeSellCubit等没有tradeForm的场景）
  ///
  /// 用于交易成功后刷新余额
  Future<void> refreshAccountBalance({int? contractAccountId}) async {
    final bool isSpotAccount;

    if (contractAccountId != null) {
      // 传入了contractAccountId参数，直接判断（null表示现货）
      isSpotAccount = false; // 有contractAccountId就是合约账户
    } else {
      // 未传入参数，从state.tradeForm判断
      isSpotAccount = state.tradeForm.currentTradeAccount?.id == kDropDownValueSpotId ||
          state.tradeForm.currentTradeAccount == null;
    }

    if (isSpotAccount) {
      // 现货账户：刷新账户信息（通过 BlocListener 自动同步）/ Spot account: refresh account info (auto-synced via BlocListener)
      await getIt<UserCubit>().fetchAccountInfo();
    } else {
      // 合约账户：刷新合约列表（通过 BlocListener 自动同步余额）/ Contract account: refresh contract list (balance auto-synced via BlocListener)
      await getIt<AccountScreenCubitV2>().fetchContractList();
    }
  }

  /// 获取最大可交易数量（基于余额）
  ///
  /// 使用 BalanceCalculator 工具类进行纯计算
  ///
  /// [price] 交易价格
  /// [availableBalance] 可用余额，null 时使用 state.tradeForm.availBal
  /// [isSpotAccount] 是否为现货账户，null 时自动判断
  ///   - true: 现货账户，需要进行币种转换
  ///   - false: 合约账户，余额已经是标的币种，无需转换
  double getMaxQuantityFromBalance({
    required double price,
    double? availableBalance,
    bool? isSpotAccount,
  }) {
    final rawBalance = availableBalance ?? state.tradeForm.availBal;
    final shouldConvert = isSpotAccount ?? isSpotTradeAccount(state.tradeForm.currentTradeAccount);

    try {
      return BalanceCalculator.calculateMaxQuantity(
        availableBalance: rawBalance,
        price: price,
        feeConfigs: state.handlingFeeConfigList,
        quantityStep: state.tradeForm.quantityStep.toDouble(),
        balanceCurrency: 'CNY', // 现货账户余额币种固定为 CNY
        instrumentCurrency: instrumentCurrency,
        exchangeRate: instrumentExchangeRate,
        isSpotAccount: shouldConvert,
        isStockIndex: state.security.isIndexTrading,
        tradeUnit: getTradeUnit() ?? 1.0,
      );
    } catch (e, s) {
      debugPrint('TradingCenterV2DataManagementMixin getMaxQuantity error: $e\n$s');
      return 0.0;
    }
  }

  /// 标的币种：只依赖 stockInfo，拿不到就返回空串
  String get instrumentCurrency => state.stockInfo?.currency.trim().toUpperCase() ?? '';

  double? _cachedExchangeRate;
  String? _cachedCurrency;

  /// 清除汇率缓存（切换标的时调用）
  void resetBalanceCalculationCache() {
    _cachedExchangeRate = null;
    _cachedCurrency = null;
  }

  /// 汇率（现货余额 -> 标的币种），缺失或 CNY 时返回 null；缓存按币种区分
  double? get instrumentExchangeRate {
    final currency = instrumentCurrency;
    if (currency.isEmpty || currency == 'CNY') {
      resetBalanceCalculationCache();
      return null;
    }

    if (_cachedCurrency != currency) {
      _cachedCurrency = currency;
      final exchangeRateCubit = getIt<ExchangeRateCubit>();
      final exchangeRate = exchangeRateCubit.getCurrencyRateConfig(currency);
      _cachedExchangeRate = exchangeRate.rate;
    }

    return _cachedExchangeRate;
  }

  // #endregion 余额计算
}
