import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/market/warn_response_entity.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';


WarnResponseEntity $WarnResponseEntityFromJson(Map<String, dynamic> json) {
  return WarnResponseEntity(current: jsonConvert.convert<int>(json['current']) ?? 0,
      total: jsonConvert.convert<int>(json['total']) ?? 0,
      pages: jsonConvert.convert<int>(json['pages']) ?? 0,
      size: jsonConvert.convert<int>(json['size']) ?? 0,
      hasNext: jsonConvert.convert<bool>(json['hasNext']) ?? false,
      records: jsonConvert.convertListNotNull<WarnResponse>(json['records']) ?? <WarnResponse>[]);
}

Map<String, dynamic> $WarnResponseEntityToJson(WarnResponseEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['total'] = entity.total;
  data['pages'] = entity.pages;
  data['size'] = entity.size;
  data['hasNext'] = entity.hasNext;
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  return data;
}

extension WarnResponseEntityExtension on WarnResponseEntity {
  WarnResponseEntity copyWith({
    int? current,
    int? total,
    int? pages,
    int? size,
    bool? hasNext,
    List<WarnResponse>? records,
  }) {
    return WarnResponseEntity(current: current ?? this.current,
        total: total ?? this.total,
        pages: pages ?? this.pages,
        size: size ?? this.size,
        hasNext: hasNext ?? this.hasNext,
        records: records ?? this.records);
  }
}

WarnResponse $WarnResponseFromJson(Map<String, dynamic> json) {
  return WarnResponse(createTime: jsonConvert.convert<String>(json['createTime']) ?? '',
      id: jsonConvert.convert<int>(json['id']) ?? 0,
      market: jsonConvert.convert<String>(json['market']) ?? '',
      name: jsonConvert.convert<String>(json['name']) ?? '',
      securityType: jsonConvert.convert<String>(json['securityType']) ?? '',
      symbol: jsonConvert.convert<String>(json['symbol']) ?? '',
      targetDownGain: jsonConvert.convert<double>(json['targetDownGain']) ?? 0.0,
      targetDownPrice: jsonConvert.convert<double>(json['targetDownPrice']) ?? 0.0,
      targetUpGain: jsonConvert.convert<double>(json['targetUpGain']) ?? 0.0,
      targetUpPrice: jsonConvert.convert<double>(json['targetUpPrice']) ?? 0.0,
      updateTime: jsonConvert.convert<String>(json['updateTime']) ?? '',
      userId: jsonConvert.convert<int>(json['userId']) ?? 0);
}

Map<String, dynamic> $WarnResponseToJson(WarnResponse entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['createTime'] = entity.createTime;
  data['id'] = entity.id;
  data['market'] = entity.market;
  data['name'] = entity.name;
  data['securityType'] = entity.securityType;
  data['symbol'] = entity.symbol;
  data['targetDownGain'] = entity.targetDownGain;
  data['targetDownPrice'] = entity.targetDownPrice;
  data['targetUpGain'] = entity.targetUpGain;
  data['targetUpPrice'] = entity.targetUpPrice;
  data['updateTime'] = entity.updateTime;
  data['userId'] = entity.userId;
  return data;
}

extension WarnResponseExtension on WarnResponse {
  WarnResponse copyWith({
    String? createTime,
    int? id,
    String? market,
    String? name,
    String? securityType,
    String? symbol,
    double? targetDownGain,
    double? targetDownPrice,
    double? targetUpGain,
    double? targetUpPrice,
    String? updateTime,
    int? userId,
  }) {
    return WarnResponse(createTime: createTime ?? this.createTime,
        id: id ?? this.id,
        market: market ?? this.market,
        name: name ?? this.name,
        securityType: securityType ?? this.securityType,
        symbol: symbol ?? this.symbol,
        targetDownGain: targetDownGain ?? this.targetDownGain,
        targetDownPrice: targetDownPrice ?? this.targetDownPrice,
        targetUpGain: targetUpGain ?? this.targetUpGain,
        targetUpPrice: targetUpPrice ?? this.targetUpPrice,
        updateTime: updateTime ?? this.updateTime,
        userId: userId ?? this.userId);
  }
}