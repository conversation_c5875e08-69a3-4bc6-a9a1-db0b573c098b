import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_normal_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/functions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/apply_normal_contract/application_summary_dialog.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/apply_normal_contract/apply_normal_contract_cubit.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_model.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/contract/widgets/app_info_bottom_sheet.dart';
import 'package:gp_stock_app/features/contract/widgets/future_funding_widget.dart';
import 'package:gp_stock_app/features/contract/widgets/selectable_button.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_radio_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/input_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

/// 申请普通合约
/// Page for applying for normal contract and futures contract
class ApplyNormalContractPage extends StatefulWidget {
  const ApplyNormalContractPage({super.key, required this.mainContractType, required this.contractType});

  final InstrumentType mainContractType;
  final ContractType contractType;
  @override
  State<ApplyNormalContractPage> createState() => _ApplyNormalContractPageState();
}

class _ApplyNormalContractPageState extends State<ApplyNormalContractPage> {
  final amountController = TextEditingController();

  @override
  void dispose() {
    amountController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig('CNY');
    Helper.afterInit(
        () => context.read<ApplyNormalContractCubit>().getConfigInfo(rates.rate, contractType: widget.mainContractType));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ApplyNormalContractCubit, ApplyNormalContractState>(
      listenWhen: (previous, current) {
        return current.selectedAmount != previous.selectedAmount;
      },
      listener: (context, state) {
        amountController.text = state.selectedAmount?.toString() ?? '';
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          elevation: 0,
          centerTitle: true,
          iconTheme: IconThemeData(
            color: context.colorTheme.textPrimary,
          ),
          title: Text(
            '${'applyFor'.tr()} [${widget.contractType.title(widget.mainContractType).tr()}]',
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: BlocBuilder<ApplyNormalContractCubit, ApplyNormalContractState>(
              builder: (context, state) {
                return Column(
                  children: [
                    ShadowBox(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _ContractTypeSection(configInfo: state.configInfo, mainContractType: widget.mainContractType),
                          _Divider(),
                          _PeriodSection(configInfo: state.configInfo),
                          _Divider(),
                          _LeverageSection(
                            configInfo: state.configInfo,
                            selectedContractConfig: state.selectedContractConfig,
                          ),
                          _Divider(),
                          _AmountSection(),
                        ],
                      ),
                    ),
                    10.verticalSpace,
                    ShadowBox(
                        child: Column(
                      children: [
                        _Divider(),
                        _ContractDetailsSection(
                          amountController: amountController,
                        ),
                      ],
                    )),
                    30.verticalSpace,
                    _SubmitSection(
                      state: state,
                      amountController: amountController,
                      contractType: widget.contractType,
                      mainContractType: widget.mainContractType,
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _ContractTypeSection extends StatelessWidget {
  final ApplyNormalContractConfigEntity? configInfo;
  final InstrumentType mainContractType;
  const _ContractTypeSection({required this.configInfo, required this.mainContractType});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'contractType'.tr(),
          style: context.textTheme.primary.fs16.w500,
        ),
        12.verticalSpace,
        SizedBox(
          height: 30.gw,
          child: mainContractType == InstrumentType.futures
              ? FutureFundingWidget()
              : (configInfo?.ruleMap.isNotEmpty ?? false)
                  ? GridView.builder(
                      shrinkWrap: true,
                      itemCount: configInfo?.ruleMap.length ?? 0,
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        crossAxisSpacing: 12,
                        mainAxisExtent: 30.gw,
                        mainAxisSpacing: 8,
                      ),
                      itemBuilder: (context, index) {
                        final item = configInfo?.ruleMap[index];
                        return BlocSelector<ApplyNormalContractCubit, ApplyNormalContractState, (ApplyNormalContractConfigRuleMap, String)>(
                          selector: (state) => (state.selectedMarket ?? ApplyNormalContractConfigRuleMap(), state.currency),
                          builder: (context, state) {
                            return SelectableButton(
                              title: contractMarketTranslation[item?.market ?? '']?.tr() ?? '',
                              isSelected: state.$1.market == item?.market,
                              onTap: () {
                                // Get exchange rate for the market being selected, not current one
                                final newMarketCurrency = switch (item?.market) {
                                  'CN' => 'CNY',
                                  'HK' => 'HKD',
                                  'US' => 'USD',
                                  _ => state.$2,
                                };
                                final newRate =
                                    context.read<ExchangeRateCubit>().getCurrencyRateConfig(newMarketCurrency);
                                context.read<ApplyNormalContractCubit>().selectMarket(item ?? ApplyNormalContractConfigRuleMap(), newRate.rate);
                                context.read<ApplyNormalContractCubit>().calculateContractAmount(newRate.rate);
                              },
                            );
                          },
                        );
                      },
                    )
                  : ShimmerWidget(
                      width: double.infinity,
                      height: 30.gw,
                      child: Container(
                        decoration: BoxDecoration(color: Colors.grey[300]),
                      ),
                    ),
        ),
      ],
    );
  }
}

class _PeriodSection extends StatelessWidget {
  final ApplyNormalContractConfigEntity? configInfo;
  const _PeriodSection({required this.configInfo});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'period'.tr(),
          style: context.textTheme.primary.fs16.w500,
        ),
        12.verticalSpace,
        SizedBox(
          height: 30.gw,
          child: configInfo?.contractConfigMap.isNotEmpty == true
              ? GridView.builder(
                  shrinkWrap: true,
                  itemCount: configInfo?.contractConfigMap.length ?? 0,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    crossAxisSpacing: 12,
                    mainAxisExtent: 30.gw,
                    mainAxisSpacing: 8,
                  ),
                  itemBuilder: (context, index) {
                    final item = configInfo?.contractConfigMap[index];
                    return BlocSelector<ApplyNormalContractCubit, ApplyNormalContractState, (int, String)>(
                      selector: (state) => (state.selectedContractConfig?.periodType ?? 0, state.currency),
                      builder: (context, state) {
                        return SelectableButton(
                          title: getPeriodType(item?.periodType ?? 0),
                          isSelected: state.$1 == item?.periodType,
                          onTap: () {
                            final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(state.$2);
                            context.read<ApplyNormalContractCubit>().selectContractConfig(item ?? ApplyNormalContractConfigContractConfigMap(), rates.rate);
                          },
                        );
                      },
                    );
                  },
                )
              : ShimmerWidget(
                  width: double.infinity,
                  height: 30.gw,
                  child: Container(
                    decoration: BoxDecoration(color: Colors.grey[300]),
                  ),
                ),
        ),
      ],
    );
  }
}

class _LeverageSection extends StatelessWidget {
  final ApplyNormalContractConfigEntity? configInfo;
  final ApplyNormalContractConfigContractConfigMap? selectedContractConfig;
  const _LeverageSection({required this.configInfo, required this.selectedContractConfig});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'leverage'.tr(),
          style: context.textTheme.primary.w500,
        ),
        12.verticalSpace,
        configInfo?.contractConfigMap.isNotEmpty == true
            ? GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  crossAxisSpacing: 12,
                  mainAxisExtent: 30.gw,
                  mainAxisSpacing: 8,
                ),
                itemCount: selectedContractConfig?.configList.length ?? 0,
                itemBuilder: (context, index) {
                  final item = selectedContractConfig?.configList[index];
                  return BlocSelector<ApplyNormalContractCubit, ApplyNormalContractState, (ApplyNormalContractConfigContractConfigMapConfigList, String)>(
                    selector: (state) => (state.selectedConfigList ?? ApplyNormalContractConfigContractConfigMapConfigList(), state.currency),
                    builder: (context, state) {
                      return SelectableButton(
                        title: '${'${item?.multiple}'}${'xTimes'.tr()}',
                        isSelected: state.$1.id == item?.id,
                        onTap: () {
                          final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(state.$2);
                          context.read<ApplyNormalContractCubit>().selectConfigList(item ?? ApplyNormalContractConfigContractConfigMapConfigList(), rates.rate);
                        },
                      );
                    },
                  );
                },
              )
            : ShimmerWidget(
                width: double.infinity,
                height: 30.gw,
                child: Container(
                  decoration: BoxDecoration(color: Colors.grey[300]),
                ),
              ),
      ],
    );
  }
}

class _AmountSection extends StatelessWidget {
  const _AmountSection();
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserCubit, UserState>(
      builder: (context, state) {
        return Column(
          children: [
            AmountRow(
              title: 'availableBalance'.tr(),
              amount: state.accountInfo?.usableCash ?? 0,
              currency: state.accountInfo?.currency ?? '',
            ),
            12.verticalSpace,
            AmountRow(
              title: 'interestCoupon'.tr(),
              amount: state.accountInfo?.interestCash ?? 0,
              currency: state.accountInfo?.currency ?? '',
            ),
          ],
        );
      },
    );
  }
}

class _ContractDetailsSection extends StatelessWidget {
  final TextEditingController amountController;

  const _ContractDetailsSection({required this.amountController});
  @override
  Widget build(BuildContext context) {
    return BlocSelector<
        ApplyNormalContractCubit,
        ApplyNormalContractState,
        ({
          int? selectedAmount,
          List<ApplyNormalContractConfigAmountList> amountList,
          ContractModelAmount contractModelAmount,
          ApplyNormalContractConfigContractConfigMap selectedContractConfig,
          String currency,
          ContractApplyAmountEntity? contractCalculation,
        })>(
      selector: (state) => (
        selectedAmount: state.selectedAmount,
        amountList: state.configInfo?.amountList ?? [],
        contractModelAmount: state.contractModelAmount ?? ContractModelAmount(),
        selectedContractConfig: state.selectedContractConfig ?? ApplyNormalContractConfigContractConfigMap(),
        currency: state.currency,
        contractCalculation: state.contractCalculation,
      ),
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'contractMargin'.tr(),
              style: context.textTheme.primary.fs16.w500,
            ),
            12.verticalSpace,
            InputDropdownWidget<int>(
              items: state.amountList.map((e) => e.applyAmount).nonNulls.toList(),
              textController: amountController,
              itemBuilder: (dynamic item) =>
                  Center(child: Text(item.toString(), style: context.textTheme.primary.fs16.ffAkz)),
              onTextChanged: (value) {
                try {
                  final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(state.currency);
                  context.read<ApplyNormalContractCubit>().selectAmount(
                        int.parse(value),
                        rates.rate,
                      );
                } catch (e) {
                  // Handle error silently
                }
              },
              onDropdownChanged: (value) {
                final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(state.currency);
                context.read<ApplyNormalContractCubit>().selectAmount(
                      value ?? 0,
                      rates.rate,
                    );
              },
            ),
            16.verticalSpace,
            AmountRow(
              title: 'totalMargin'.tr(),
              amount: state.contractModelAmount.totalTadingFunds ?? 0,
              currency: state.currency,
            ),
            12.verticalSpace,
            AmountRow(
              title: 'warningLine'.tr(),
              amount: state.contractModelAmount.lossWarningLine ?? 0,
              currency: state.currency,
            ),
            12.verticalSpace,
            AmountRow(
              title: 'stopLossLine'.tr(),
              amount: state.contractModelAmount.lossFlatLine ?? 0,
              currency: state.currency,
            ),
            12.verticalSpace,
            AmountRow(
              title: 'interestRate'.tr(),
              amount: state.contractCalculation?.rateAmount ?? 0,
              currency:
                  '${state.currency} / ${contractTypeTranslation[state.selectedContractConfig.periodType]?.$2.tr() ?? ''}',
            ),
            12.verticalSpace,
            AmountRow(
              title: 'interestReduceAmount'.tr(),
              amount: state.contractCalculation?.deductInterestCashCNY ?? 0,
              isCurrency: true,
              currency: 'CNY',
            ),
            12.verticalSpace,
            AmountRow(
              title: 'actualPaymentAmount'.tr(),
              amount: state.contractCalculation?.deductCanUseCashCNY ?? 0,
              isCurrency: true,
              prefix: state.currency != 'CNY' ? '≈ ' : '',
              currency: 'CNY',
            ),
            12.verticalSpace,
          ],
        );
      },
    );
  }
}

class _SubmitSection extends StatelessWidget {
  final ApplyNormalContractState state;
  final TextEditingController amountController;
  final ContractType contractType;
  final InstrumentType mainContractType;
  const _SubmitSection(
      {required this.state,
      required this.amountController,
      required this.contractType,
      required this.mainContractType});
  @override
  Widget build(BuildContext context) {
    final period = {
      1: 1,
      2: 7,
      3: 30,
    };
    return Column(
      children: [
        GestureDetector(
          onTap: () => context.read<ApplyNormalContractCubit>().updateIsAgree(!state.isAgree),
          child: Row(
            children: [
              CustomRadioButton(
                isSelected: state.isAgree,
                onChange: (value) => context.read<ApplyNormalContractCubit>().updateIsAgree(value),
              ),
              9.horizontalSpace,
              Text.rich(
                TextSpan(
                  text: '${'readAndAgree'.tr()} ',
                  style: context.textTheme.regular.fs10,
                  children: [
                    TextSpan(
                      text: 'marginAgreement'.tr(),
                      style: context.textTheme.primary.w500.fs10,
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          showAppInfoBottomSheet(
                            context,
                            capitalA: ((state.contractModelAmount?.totalTadingFunds ?? 0) - (state.selectedAmount ?? 0))
                                .toStringAsFixed(0),
                            capitalB: (state.selectedAmount ?? 0).toStringAsFixed(0),
                            interestRate: '${state.contractCalculation?.rate ?? 0}',
                            interestAmount: '${state.contractCalculation?.rateAmount ?? 0}',
                            period: period[state.selectedContractConfig?.periodType ?? 0] ?? 0,
                          );
                        },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        9.verticalSpace,
        CommonButton(
          enable: state.isAgree,
          onPressed: () {
            showDialog(
              context: context,
              builder: (_) => BlocProvider.value(
                value: context.read<ApplyNormalContractCubit>(),
                child: ApplicationSummaryDialog(
                  contractType: contractType,
                  mainContractType: mainContractType,
                ),
              ),
            );
          },
          title: 'verify'.tr(),
        ),
        37.verticalSpace,
      ],
    );
  }
}

class _Divider extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.gw),
      child: Divider(
        color: Colors.grey.withAlpha(26),
        height: 1,
      ),
    );
  }
}
