import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

class FTradeListTabBar extends StatelessWidget {
  final FuturesScope scope;
  final int selectedTabIdx;
  final void Function(int) onTap;

  const FTradeListTabBar({
    super.key,
    required this.scope,
    required this.selectedTabIdx,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            childAnimationBuilder: (widget) => SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: widget,
              ),
            ),
            children: [_marketTabBar()],
          ),
        ),
      ),
    );
  }

  Widget _marketTabBar() {
    final List<FuturesMarketType> marketTypes = scope == FuturesScope.cn
        ? [
            CNFuturesMarketType.dalian,
            CNFuturesMarketType.zhengzhou,
            CNFuturesMarketType.shanghai,
            CNFuturesMarketType.shanghaiEnergy,
            CNFuturesMarketType.china,
          ]
        : [
            GlobalFuturesMarketType.american,
          ];

    final tabData = marketTypes.map((type) => type.name.tr()).toList();
    final currentIndex = marketTypes.indexWhere((type) => type.idx == selectedTabIdx);

    return CommonTabBar.withAutoKey(
      tabData,
      currentIndex: currentIndex.clamp(0, tabData.length - 1),
      onTap: (index) => onTap(marketTypes[index].idx),
      style: CommonTabBarStyle.line,
      isScrollable: true,
    );
  }
}
