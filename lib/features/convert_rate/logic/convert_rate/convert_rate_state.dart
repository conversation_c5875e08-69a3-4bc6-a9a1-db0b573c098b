import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/core/models/entities/common/exchange_rate_entity.dart';

import '../../../../shared/constants/enums.dart';

part 'convert_rate_state.freezed.dart';

@freezed
class ConvertRateState with _$ConvertRateState {
  const factory ConvertRateState({
    @Default(DataStatus.idle) DataStatus status,
    List<ExchangeRate>? rates,
    String? error,
    @Default("1") String customAmount,
    @Default("CNY") String selectedCurrency,
    @Default(false) bool isEditingAmount,
    @Default(false) bool isCalculating,
    double? convertedUSD,
    double? convertedCNY,
    double? convertedHKD,
  }) = _ConvertRateState;
}