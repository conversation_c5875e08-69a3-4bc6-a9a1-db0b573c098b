import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/alipay_account_entity.dart';

AlipayAccountEntityList $AlipayAccountEntityListFromJson(Map<String, dynamic> json) {
  final AlipayAccountEntityList alipayAccountEntityList = AlipayAccountEntityList();
  final List<AlipayAccountEntity>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<AlipayAccountEntity>(e) as AlipayAccountEntity).toList();
  if (list != null) {
    alipayAccountEntityList.list = list;
  }
  return alipayAccountEntityList;
}

Map<String, dynamic> $AlipayAccountEntityListToJson(AlipayAccountEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension AlipayAccountEntityListExtension on AlipayAccountEntityList {
  AlipayAccountEntityList copyWith({
    List<AlipayAccountEntity>? list,
  }) {
    return AlipayAccountEntityList()
      ..list = list ?? this.list;
  }
}

AlipayAccountEntity $AlipayAccountEntityFromJson(Map<String, dynamic> json) {
  final AlipayAccountEntity alipayAccountEntity = AlipayAccountEntity();
  final String? bankAccount = jsonConvert.convert<String>(json['bankAccount']);
  if (bankAccount != null) {
    alipayAccountEntity.bankAccount = bankAccount;
  }
  final String? bankAddress = jsonConvert.convert<String>(json['bankAddress']);
  if (bankAddress != null) {
    alipayAccountEntity.bankAddress = bankAddress;
  }
  final String? bankCode = jsonConvert.convert<String>(json['bankCode']);
  if (bankCode != null) {
    alipayAccountEntity.bankCode = bankCode;
  }
  final String? bankRegion = jsonConvert.convert<String>(json['bankRegion']);
  if (bankRegion != null) {
    alipayAccountEntity.bankRegion = bankRegion;
  }
  final String? channelName = jsonConvert.convert<String>(json['channelName']);
  if (channelName != null) {
    alipayAccountEntity.channelName = channelName;
  }
  final int? constantGiveRate = jsonConvert.convert<int>(json['constantGiveRate']);
  if (constantGiveRate != null) {
    alipayAccountEntity.constantGiveRate = constantGiveRate;
  }
  final List<AlipayAccountEntityCustomizeGiveRate>? customizeGiveRate = (json['customizeGiveRate'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<AlipayAccountEntityCustomizeGiveRate>(e) as AlipayAccountEntityCustomizeGiveRate)
      .toList();
  if (customizeGiveRate != null) {
    alipayAccountEntity.customizeGiveRate = customizeGiveRate;
  }
  final int? giveGiftType = jsonConvert.convert<int>(json['giveGiftType']);
  if (giveGiftType != null) {
    alipayAccountEntity.giveGiftType = giveGiftType;
  }
  final int? giveRuleType = jsonConvert.convert<int>(json['giveRuleType']);
  if (giveRuleType != null) {
    alipayAccountEntity.giveRuleType = giveRuleType;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    alipayAccountEntity.id = id;
  }
  final bool? isOnlyOffline = jsonConvert.convert<bool>(json['isOnlyOffline']);
  if (isOnlyOffline != null) {
    alipayAccountEntity.isOnlyOffline = isOnlyOffline;
  }
  final double? maxAmount = jsonConvert.convert<double>(json['maxAmount']);
  if (maxAmount != null) {
    alipayAccountEntity.maxAmount = maxAmount;
  }
  final double? minAmount = jsonConvert.convert<double>(json['minAmount']);
  if (minAmount != null) {
    alipayAccountEntity.minAmount = minAmount;
  }
  final String? ownerName = jsonConvert.convert<String>(json['ownerName']);
  if (ownerName != null) {
    alipayAccountEntity.ownerName = ownerName;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    alipayAccountEntity.status = status;
  }
  return alipayAccountEntity;
}

Map<String, dynamic> $AlipayAccountEntityToJson(AlipayAccountEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['bankAccount'] = entity.bankAccount;
  data['bankAddress'] = entity.bankAddress;
  data['bankCode'] = entity.bankCode;
  data['bankRegion'] = entity.bankRegion;
  data['channelName'] = entity.channelName;
  data['constantGiveRate'] = entity.constantGiveRate;
  data['customizeGiveRate'] = entity.customizeGiveRate.map((v) => v.toJson()).toList();
  data['giveGiftType'] = entity.giveGiftType;
  data['giveRuleType'] = entity.giveRuleType;
  data['id'] = entity.id;
  data['isOnlyOffline'] = entity.isOnlyOffline;
  data['maxAmount'] = entity.maxAmount;
  data['minAmount'] = entity.minAmount;
  data['ownerName'] = entity.ownerName;
  data['status'] = entity.status;
  return data;
}

extension AlipayAccountEntityExtension on AlipayAccountEntity {
  AlipayAccountEntity copyWith({
    String? bankAccount,
    String? bankAddress,
    String? bankCode,
    String? bankRegion,
    String? channelName,
    int? constantGiveRate,
    List<AlipayAccountEntityCustomizeGiveRate>? customizeGiveRate,
    int? giveGiftType,
    int? giveRuleType,
    int? id,
    bool? isOnlyOffline,
    double? maxAmount,
    double? minAmount,
    String? ownerName,
    int? status,
  }) {
    return AlipayAccountEntity()
      ..bankAccount = bankAccount ?? this.bankAccount
      ..bankAddress = bankAddress ?? this.bankAddress
      ..bankCode = bankCode ?? this.bankCode
      ..bankRegion = bankRegion ?? this.bankRegion
      ..channelName = channelName ?? this.channelName
      ..constantGiveRate = constantGiveRate ?? this.constantGiveRate
      ..customizeGiveRate = customizeGiveRate ?? this.customizeGiveRate
      ..giveGiftType = giveGiftType ?? this.giveGiftType
      ..giveRuleType = giveRuleType ?? this.giveRuleType
      ..id = id ?? this.id
      ..isOnlyOffline = isOnlyOffline ?? this.isOnlyOffline
      ..maxAmount = maxAmount ?? this.maxAmount
      ..minAmount = minAmount ?? this.minAmount
      ..ownerName = ownerName ?? this.ownerName
      ..status = status ?? this.status;
  }
}

AlipayAccountEntityCustomizeGiveRate $AlipayAccountEntityCustomizeGiveRateFromJson(Map<String, dynamic> json) {
  final AlipayAccountEntityCustomizeGiveRate alipayAccountEntityCustomizeGiveRate = AlipayAccountEntityCustomizeGiveRate();
  final int? amount = jsonConvert.convert<int>(json['amount']);
  if (amount != null) {
    alipayAccountEntityCustomizeGiveRate.amount = amount;
  }
  final int? rate = jsonConvert.convert<int>(json['rate']);
  if (rate != null) {
    alipayAccountEntityCustomizeGiveRate.rate = rate;
  }
  return alipayAccountEntityCustomizeGiveRate;
}

Map<String, dynamic> $AlipayAccountEntityCustomizeGiveRateToJson(AlipayAccountEntityCustomizeGiveRate entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['amount'] = entity.amount;
  data['rate'] = entity.rate;
  return data;
}

extension AlipayAccountEntityCustomizeGiveRateExtension on AlipayAccountEntityCustomizeGiveRate {
  AlipayAccountEntityCustomizeGiveRate copyWith({
    int? amount,
    int? rate,
  }) {
    return AlipayAccountEntityCustomizeGiveRate()
      ..amount = amount ?? this.amount
      ..rate = rate ?? this.rate;
  }
}