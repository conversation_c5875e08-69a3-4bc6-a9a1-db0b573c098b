import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/features/chat/logic/chat/chat_cubit.dart';
import 'package:gp_stock_app/features/chat/screens/conversation_screen.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/widgets/alert_dilaog/custom_alert_dialog.dart';



String getInstrumentIdFromMarketType(String marketType) {
  final market = MarketSymbol.fromMarketType(marketType);
  return '${market.market}|${market.securityType}|${market.symbol}';
}

Security getTradingArguments(String marketType) {
  final instrument = getInstrumentIdFromMarketType(marketType);
  return Security(instrument: instrument);
}




class TimeFormat {
  static const String full = 'yyyy-MM-dd HH:mm:ss';
  static const String ymd = 'yyyy-MM-dd';
  static const String hm = 'HH:mm';
  static const String md = 'MM-dd';
  static const String ym = 'yyyy-MM';
  static const String y = 'yyyy';
}

bool isDarkMode(BuildContext context) {
  return Theme.of(context).brightness == Brightness.dark;
}

///是否是空
bool isEmpty(dynamic res) {
  if (res == null) return true;

  if (res is String || res is Iterable || res is Map) {
    return res.isEmpty;
  }

  return false;
}

void launchChatService(BuildContext context) {
  AuthUtils.verifyAuth(() async {
    final conversation = await context.read<ChatCubit>().getChatServicesAccount();
    if (conversation != null) {
      if (conversation.pImAccount.isEmpty) {
        Helper.showDialogPopUp(
          context,
          CustomAlertDialog(
            message: 'noExclusiveSupport'.tr(),
            actionButtonText: 'ok'.tr(),
            buttonBackGroundColor: context.theme.primaryColor,
            onActionButtonPressed: () {
              Navigator.pop(context);
            },
            isLoading: false,
            messageTextStyle: context.textTheme.primary.fs20.w600,
          ),
          dialogKey: "noExclusiveSupport",
          barrierDismissible: true,
        );
        return;
      }
      Navigator.push(context, MaterialPageRoute(builder: (context) => ConversationScreen(conversation: conversation)));
    }
  });
}

