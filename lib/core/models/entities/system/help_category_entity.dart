import 'dart:convert';

import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/help_category_entity.g.dart';

export 'package:gp_stock_app/generated/json/help_category_entity.g.dart';

@JsonSerializable()
class HelpCategoryEntityList {
  List<HelpCategoryEntity> list = [];

  HelpCategoryEntityList();

  factory HelpCategoryEntityList.fromJson(Map<String, dynamic> json) => $HelpCategoryEntityListFromJson(json);

  Map<String, dynamic> toJson() => $HelpCategoryEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HelpCategoryEntity {
  final String icon;
  final String name;
  final List<HelpQuestion> questions;
  final int typeId;

  const HelpCategoryEntity({
    this.icon = "",
    this.name = "",
    this.questions = const [],
    this.typeId = 0,
  });

  factory HelpCategoryEntity.fromJson(Map<String, dynamic> json) => $HelpCategoryEntityFromJson(json);

  Map<String, dynamic> toJson() => $HelpCategoryEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HelpQuestionList {
  List<HelpQuestion> list = [];

  HelpQuestionList();

  factory HelpQuestionList.fromJson(Map<String, dynamic> json) => $HelpQuestionListFromJson(json);

  Map<String, dynamic> toJson() => $HelpQuestionListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}


@JsonSerializable()
class HelpQuestion {
  final String content;
  final int hitNum;
  final int id;
  final int sort;
  final String title;
  final int typeId;

  const HelpQuestion({
    this.content = "",
    this.hitNum = 0,
    this.id = 0,
    this.sort = 0,
    this.title = "",
    this.typeId = 0,
  });

  factory HelpQuestion.fromJson(Map<String, dynamic> json) => $HelpQuestionFromJson(json);

  Map<String, dynamic> toJson() => $HelpQuestionToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
