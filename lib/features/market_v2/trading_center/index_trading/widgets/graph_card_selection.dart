import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/trade_navigation_helper.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';

class GraphCardSelection extends StatelessWidget {
  const GraphCardSelection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IndexTradeCubit, IndexTradeState>(
      builder: (context, state) {
        if (state.indexStocks.isEmpty) return SizedBox.shrink();
        return Padding(
          padding: EdgeInsets.only(left: 16.gw, top: 0.gw, right: 16.gw, bottom: 0.gw),
          child: Row(
            spacing: 8,
            children: [
              Expanded(
                flex: 1,
                child: Text(
                  state.indexStocks[state.selectedIndex].stockInfo.name,
                  style: context.textTheme.primary.w700,
                ),
              ),
              SymbolChip(
                name: state.indexStocks[state.selectedIndex].stockInfo.market,
                chipColor: context.theme.primaryColor,
              ),
              Text(
                state.indexStockConfigList[state.selectedIndex].symbol,
                style: context.textTheme.primary.w700,
              ),
              Text(
                '${(state.indexStocks[state.selectedIndex].stockInfo.chg).toStringAsFixed(2)} %',
                style: context.textTheme.primary.w700.ffAkz.copyWith(
                    color: state.indexStocks[state.selectedIndex].stockInfo.chg.getValueColor(context)),
              ),

              TextButton(
                onPressed: () {
                  final i = state.selectedIndex;
                  final data = state.indexStocks;
                  AuthUtils.verifyAuth(() {
                    final stockInfo = data[i].stockInfo;
                    TradeNavigationHelper.goToTradingCenterV2(
                      context,
                      security: stockInfo.instrumentInfo,
                      tabType: TradeTabType.Quotes,
                      isIndexTradingOverride: true,
                    );
                  });
                },
                style: TextButton.styleFrom(
                  foregroundColor: context.theme.primaryColor,
                  padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 4.gw),
                  minimumSize: const Size(0, 0),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text("detail".tr()),
              ),
            ],
          ),
        );
      },
    );
  }
}
