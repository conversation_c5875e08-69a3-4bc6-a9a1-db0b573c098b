import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/wallet/bank_entity_list.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/add_bank/add_bank_cubit.dart';
import 'package:gp_stock_app/features/account/logic/add_bank/add_bank_state.dart';
import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_state.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_state.dart';
import 'package:gp_stock_app/features/profile/logic/auth_n/auth_n_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/app/utilities/card_input_formatter.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

import '../../../shared/constants/enums.dart';
import '../../../shared/widgets/buttons/custom_material_button.dart';
import '../logic/otp/otp_cubit.dart';
import '../logic/otp/otp_state.dart';

class AddBankScreen extends StatefulWidget {
  const AddBankScreen({super.key});

  @override
  State<AddBankScreen> createState() => _AddBankScreenState();
}

class _AddBankScreenState extends State<AddBankScreen> {
  final cardNumberController = TextEditingController();
  final cardholderController = TextEditingController();
  final idNumberController = TextEditingController();
  final phoneNumberController = TextEditingController();
  final otpController = TextEditingController();

  BankEntity? _selectedBank;

  @override
  void dispose() {
    cardNumberController.dispose();
    cardholderController.dispose();
    idNumberController.dispose();
    phoneNumberController.dispose();
    otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = context.watch<AuthNCubit>().state;
    final idNumber = authState.authNInfo?.idCard ?? '';
    final realName = authState.authNInfo?.realName ?? '';
    final mobileNumber = context.watch<UserCubit>().state.userInfo?.mobile ?? '';
    final isUserRegisteredViaEmail = context.watch<UserCubit>().isUserRegisteredViaEmail;
    return MultiBlocListener(
      listeners: [
        BlocListener<UserBankListCubit, UserBankListState>(
          listenWhen: (previous, current) => previous.bankListUpdateStatus != current.bankListUpdateStatus,
          listener: (context, state) {
            if (state.bankListUpdateStatus.isSuccess) {
              Navigator.of(context).pop();
              Helper.showFlutterToast('bankAddedSuccessfully'.tr());
            }
          },
        ),
        BlocListener<OtpCubit, OtpState>(
          listenWhen: (previous, current) => previous.sendStatus != current.sendStatus,
          listener: (context, state) {
            if (state.sendStatus.isSuccess) {
              Helper.showFlutterToast('codeSent'.tr());
            }
          },
        ),
      ],
      child: Scaffold(
        appBar: AppBar(title: Text('addBank'.tr())),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: ShadowBox(
              child: Column(
                spacing: 16.gw,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextFieldWidget(
                    controller: cardNumberController,
                    topLabelText: 'cardNumber'.tr(),
                    hintText: 'enterCardNumber'.tr(),
                    onChanged: (value) => context.read<AddBankCubit>().updateCardNumber(value),
                    textInputType: TextInputType.number,
                    inputFormatters: [CardNumberInputFormatter()],
                  ),
                  TextFieldWidget(
                    controller: cardholderController..text = realName,
                    topLabelText: 'cardholder'.tr(),
                    hintText: 'enterCardholder'.tr(),
                    enabled: true,
                  ),
                  TextFieldWidget(
                    controller: idNumberController..text = idNumber,
                    topLabelText: 'idNumber'.tr(),
                    hintText: 'pleaseEnterIdNumber'.tr(),
                    enabled: false,
                  ),
                  Row(
                    spacing: 4,
                    children: [
                      Text('openingBank'.tr(), style: context.textTheme.regular),
                      Text('*', style: context.textTheme.regular.w500.copyWith(color: context.colorTheme.stockRed)),
                    ],
                  ),
                  BlocBuilder<BankListCubit, BankListState>(
                    builder: (context, state) {
                      return CommonDropdown(
                        hintText: 'selectOpeningBank'.tr(),
                        onChanged: (DropDownValue value) {
                          _selectedBank = state.bankList?.firstWhere((e) => e.id == int.parse(value.id!));
                          context.read<AddBankCubit>().updateSelectedBank(_selectedBank);
                        },
                        dropDownValue: (state.bankList ?? [])
                            .map((e) => DropDownValue(id: e.id.toString(), value: e.bankName, icon: e.icon))
                            .toList(),
                        showSearchBox: true,
                        emptyBuilder: (p0, p1) => SizedBox(
                          height: 100.gw,
                          child: Center(
                            child: Text(
                              'noBanksFound'.tr(),
                              style: context.textTheme.regular,
                            ),
                          ),
                        ),
                        itemBuilder: (context, item, isDisabled, isSelected) {
                          return Container(
                            margin: EdgeInsets.symmetric(horizontal: 10.0.gw),
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0.gw),
                              child: Row(
                                spacing: 10,
                                children: [
                                  Image.network(
                                    item.icon ?? '',
                                    width: 40.gw,
                                    height: 40.gw,
                                    fit: BoxFit.cover,
                                  ),
                                  Expanded(
                                    child: Text(
                                      item.value ?? '',
                                      style: context.textTheme.regular,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                  if (!isUserRegisteredViaEmail) ...[
                    TextFieldWidget(
                      controller: phoneNumberController..text = mobileNumber,
                      topLabelText: 'phone'.tr(),
                      hintText: 'enterPhone'.tr(),
                      textInputType: TextInputType.phone,
                      enabled: false,
                    ),
                    _buildOtpSection(context, mobileNumber),
                  ],
                  BlocBuilder<OtpCubit, OtpState>(
                    builder: (context, otpState) {
                      return BlocSelector<AddBankCubit, AddBankState, bool>(
                        selector: (state) =>
                            state.isFormValid && (otpController.text.isNotEmpty || isUserRegisteredViaEmail),
                        builder: (context, isFormValid) {
                          return BlocSelector<UserBankListCubit, UserBankListState, bool>(
                            selector: (state) => state.bankListUpdateStatus.isLoading,
                            builder: (context, isLoading) {
                              return CustomMaterialButton(
                                buttonText: 'addBank'.tr(),
                                isLoading: isLoading,
                                loadingColor: Colors.white,
                                isEnabled: isFormValid && _selectedBank != null,
                                onPressed: (isFormValid && _selectedBank != null)
                                    ? () {
                                        FocusScope.of(context).unfocus();
                                        final cardNum = cardNumberController.text.replaceAll(' ', '');
                                        if (!(cardNum.length == 16 || cardNum.length == 19)) {
                                          return GPEasyLoading.showToast('pls_enter_correct_card_number'.tr());
                                        } else if (otpController.text.isEmpty && !isUserRegisteredViaEmail) {
                                          return GPEasyLoading.showToast('sms_code_cannot_be_empty'.tr());
                                        } else if (_selectedBank == null) {
                                          return GPEasyLoading.showToast('pls_select_the_bank'.tr());
                                        }
                                        context.read<UserBankListCubit>().addUserBank(
                                              bankCardNo: cardNumberController.text,
                                              systemBankId: _selectedBank!.id,
                                              smsCode: otpController.text,
                                              mobile: mobileNumber,
                                            );
                                      }
                                    : null,
                                height: 40.gw,
                                borderRadius: 8.gr,
                              );
                            },
                          );
                        },
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the OTP input field and "Get Verification Code" button.
  Widget _buildOtpSection(BuildContext context, String mobileNumber) {
    return Container(
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('smsCode'.tr(), style: context.textTheme.regular),
          8.verticalSpace,
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                flex: 3,
                child: TextFieldWidget(
                  controller: otpController,
                  hintText: 'enterCode'.tr(),
                  textInputType: TextInputType.number,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(6),
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  onChanged: (value) => context.read<AddBankCubit>().updateOtp(value),
                ),
              ),
              8.horizontalSpace,
              Expanded(
                flex: 2,
                child: BlocBuilder<OtpCubit, OtpState>(
                  builder: (context, otpState) {
                    return CustomMaterialButton(
                      height: 35.gw,
                      buttonText: otpState.isTimerActive
                          ? '${'resend'.tr()} (${otpState.timerDuration}s)'
                          : 'loginVerificationCode'.tr(),
                      fontSize: 12.gsp,
                      borderRadius: 8.gr,
                      onPressed: otpState.isTimerActive && otpState.sendStatus == DataStatus.loading
                          ? null
                          : () => context.read<OtpCubit>().sendOtp(mobileNumber, type: OtpType.bindBankCard),
                      isEnabled: !otpState.isTimerActive,
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
