// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_bank_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AddBankState {
  String? get cardNumber => throw _privateConstructorUsedError;
  String? get otp => throw _privateConstructorUsedError;
  BankEntity? get selectedBank => throw _privateConstructorUsedError;
  bool get isFormValid => throw _privateConstructorUsedError;

  /// Create a copy of AddBankState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddBankStateCopyWith<AddBankState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddBankStateCopyWith<$Res> {
  factory $AddBankStateCopyWith(
          AddBankState value, $Res Function(AddBankState) then) =
      _$AddBankStateCopyWithImpl<$Res, AddBankState>;
  @useResult
  $Res call(
      {String? cardNumber,
      String? otp,
      BankEntity? selectedBank,
      bool isFormValid});
}

/// @nodoc
class _$AddBankStateCopyWithImpl<$Res, $Val extends AddBankState>
    implements $AddBankStateCopyWith<$Res> {
  _$AddBankStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddBankState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardNumber = freezed,
    Object? otp = freezed,
    Object? selectedBank = freezed,
    Object? isFormValid = null,
  }) {
    return _then(_value.copyWith(
      cardNumber: freezed == cardNumber
          ? _value.cardNumber
          : cardNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as BankEntity?,
      isFormValid: null == isFormValid
          ? _value.isFormValid
          : isFormValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddBankStateImplCopyWith<$Res>
    implements $AddBankStateCopyWith<$Res> {
  factory _$$AddBankStateImplCopyWith(
          _$AddBankStateImpl value, $Res Function(_$AddBankStateImpl) then) =
      __$$AddBankStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? cardNumber,
      String? otp,
      BankEntity? selectedBank,
      bool isFormValid});
}

/// @nodoc
class __$$AddBankStateImplCopyWithImpl<$Res>
    extends _$AddBankStateCopyWithImpl<$Res, _$AddBankStateImpl>
    implements _$$AddBankStateImplCopyWith<$Res> {
  __$$AddBankStateImplCopyWithImpl(
      _$AddBankStateImpl _value, $Res Function(_$AddBankStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddBankState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardNumber = freezed,
    Object? otp = freezed,
    Object? selectedBank = freezed,
    Object? isFormValid = null,
  }) {
    return _then(_$AddBankStateImpl(
      cardNumber: freezed == cardNumber
          ? _value.cardNumber
          : cardNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as BankEntity?,
      isFormValid: null == isFormValid
          ? _value.isFormValid
          : isFormValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$AddBankStateImpl implements _AddBankState {
  const _$AddBankStateImpl(
      {this.cardNumber, this.otp, this.selectedBank, this.isFormValid = false});

  @override
  final String? cardNumber;
  @override
  final String? otp;
  @override
  final BankEntity? selectedBank;
  @override
  @JsonKey()
  final bool isFormValid;

  @override
  String toString() {
    return 'AddBankState(cardNumber: $cardNumber, otp: $otp, selectedBank: $selectedBank, isFormValid: $isFormValid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddBankStateImpl &&
            (identical(other.cardNumber, cardNumber) ||
                other.cardNumber == cardNumber) &&
            (identical(other.otp, otp) || other.otp == otp) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            (identical(other.isFormValid, isFormValid) ||
                other.isFormValid == isFormValid));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, cardNumber, otp, selectedBank, isFormValid);

  /// Create a copy of AddBankState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddBankStateImplCopyWith<_$AddBankStateImpl> get copyWith =>
      __$$AddBankStateImplCopyWithImpl<_$AddBankStateImpl>(this, _$identity);
}

abstract class _AddBankState implements AddBankState {
  const factory _AddBankState(
      {final String? cardNumber,
      final String? otp,
      final BankEntity? selectedBank,
      final bool isFormValid}) = _$AddBankStateImpl;

  @override
  String? get cardNumber;
  @override
  String? get otp;
  @override
  BankEntity? get selectedBank;
  @override
  bool get isFormValid;

  /// Create a copy of AddBankState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddBankStateImplCopyWith<_$AddBankStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
