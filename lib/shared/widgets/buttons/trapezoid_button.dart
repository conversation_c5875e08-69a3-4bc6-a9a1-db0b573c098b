import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

class TrapezoidButton extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;
  final bool isLeft; // true = 左斜, false = 右斜

  const TrapezoidButton({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
    required this.isLeft,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: ClipPath(
        clipper: isLeft ? LeftTabClipper() : RightTabClipper(),
        child: Container(
          padding: isLeft ? EdgeInsets.only(left: 25.gw, right: 28.gw) : EdgeInsets.only(left: 28.gw, right: 25.gw),
          color: context.theme.primaryColor.withNewOpacity(isSelected ? 1 : 0.15),
          alignment: Alignment.center,
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? Colors.white : context.theme.primaryColor,
            ).fs15,
          ),
        ),
      ),
    );
  }
}

