import 'package:gp_stock_app/core/utils/contract_utils.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/contract.g.dart';
import 'dart:convert';

import 'package:gp_stock_app/shared/constants/enums.dart';

@JsonSerializable()
class ContractListEntity {
  int current = 0;
  bool hasNext = false;
  List<Contract> records = [];
  int total = 0;

  ContractListEntity();

  factory ContractListEntity.fromJson(Map<String, dynamic> json) => $ContractListEntityFromJson(json);

  Map<String, dynamic> toJson() => $ContractListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class Contract {
  double accountWinAmount = 0.0;
  double allAsset = 0.0;

//backType字段： 1.强平线 2.预警线
  int backType = 0;
  double closeRemindAmount = 0.0;
  double contractAssetAmount = 0.0;
  double coverLossAmount = 0.0;
  String currency = '';
  double expendAmount = 0.0;
  String expireTime = '';
  double freezePower = 0.0;
  double gapCloseRemindAmount = 0.0;
  double gapWarnRemindAmount = 0.0;
  double giveAmount = 0.0;
  int id = 0;
  double initCash = 0.0;
  double interestAmount = 0.0;
  double interestRate = 0.0;
  String marketType = '';
  int multiple = 0;
  double negativeAmount = 0.0;
  String openTime = '';
  int periodType = 0;
  double positionAmount = 0.0;
  double receivableInterest = 0.0;
  int settlementStatus = 0;
  double todayWinAmount = 0.0;
  double todayWinRate = 0.0;
  int totalAccountAmount = 0;
  double totalCash = 0.0;
  double totalFinance = 0.0;
  double totalPower = 0.0;
  int type = 0;
  double useAmount = 0.0;
  double warnRemindAmount = 0.0;
  double winRate = 0.0;
  double winAmount = 0.0;
  double withdrawAmount = 0.0;
  int yesterdayAsset = 0;
  bool isAutoRenew = false;

  /// 合约标签
  String get label => ContractUtils.buildLabel(
    id: id,
    type: type,
    periodType: periodType,
    marketType: marketType,
    multiple: multiple,
  );

  /// 合约类型 1标准 2体验 3彩金
  ContractType get contractType => ContractType.fromValue(type);

  Contract();

  factory Contract.fromJson(Map<String, dynamic> json) => $ContractFromJson(json);

  Map<String, dynamic> toJson() => $ContractToJson(this);

  String get tradingLine => switch (backType) {
        1 => 'liquidationLine2',
        2 => 'warningLine2',
        _ => '',
      };

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ContractMarginEntity {
  /// 金额数据(普通合约)
  List<ContractMarginAmountList> amountList = [];

  /// 金额数据（彩金合约）
  List<int> bonusAmountList = [];

  /// 强平金额
  double closeAmount = 0;

  /// 平仓线百分比
  double closeValue = 0;

  /// 天数
  int giveDay = 0;

  /// id
  int id = 0;

  /// 初始保证金
  double initCash = 0;

  /// 利率
  double interestRate = 0.0;

  /// 市场类型
  String marketType = '';

  /// 倍数
  int multiple = 0;

  /// 穿仓金额
  int negativeAmount = 0;

  /// 期限类型1：按天 2：按周 3：按月 4：免息
  int periodType = 0;

  /// 总现金(保证金，包含追加的),用户实际支付的钱
  double totalCash = 0;

  /// 总融资金额
  double totalFinance = 0;

  /// 总操盘资金
  double totalPower = 0;

  /// 合约类型1：普通 2：体验 3：彩金
  int type = 0;

  /// 警戒金额
  double warnAmount = 0;

  /// 预警线百分比
  double warnValue = 0;

  /// 过期时间
  String expireTime = '';


  String? get contractTypeText => contractMarketTranslation[marketType];

  String? get periodTypeText => 'contract.period_$periodType';


  ContractType get contractType => ContractType.fromValue(type);

  ContractMarginEntity();

  factory ContractMarginEntity.fromJson(Map<String, dynamic> json) => $ContractMarginEntityFromJson(json);

  Map<String, dynamic> toJson() => $ContractMarginEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ContractMarginAmountList {
  /// 保证金金额
  int applyAmount = 0;

  /// 唯一标识
  int id = 0;

  ContractMarginAmountList();

  factory ContractMarginAmountList.fromJson(Map<String, dynamic> json) => $ContractMarginAmountListFromJson(json);

  Map<String, dynamic> toJson() => $ContractMarginAmountListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ProfitWithdrawalConfigEntity {
  double amount = 0;
  bool canUse = false;
  double dailyProfitWithdrawalCount = 0;
  String endTime = '';
  double maxProfitWithdrawalAmount = 0;
  double minProfitWithdrawalAmount = 0;
  String startTime = '';

  ProfitWithdrawalConfigEntity();

  factory ProfitWithdrawalConfigEntity.fromJson(Map<String, dynamic> json) =>
      $ProfitWithdrawalConfigEntityFromJson(json);

  Map<String, dynamic> toJson() => $ProfitWithdrawalConfigEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ContractApplyAmountEntity {
  double canUserCashCNY = 0;
  double deductCanUseCashCNY = 0;
  double deductInterestCashCNY = 0;
  double discountInterestCNY = 0;
  double exchangeRate = 0;
  double interestCashCNY = 0;
  double rate = 0;
  double rateAmount = 0;

  ContractApplyAmountEntity();

  factory ContractApplyAmountEntity.fromJson(Map<String, dynamic> json) => $ContractApplyAmountEntityFromJson(json);

  Map<String, dynamic> toJson() => $ContractApplyAmountEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
