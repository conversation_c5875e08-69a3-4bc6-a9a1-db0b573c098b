import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/tab_indicator/tc_underline_tab_indicator.dart';

import 'tab_clipper.dart';

/// TabBar 样式配置 / TabBar style configuration
class TabBarStyleConfig {
  /// Tab 对齐方式 / Tab alignment
  final TabAlignment? tabAlignment;

  /// 标签内边距 / Label padding
  final EdgeInsets? labelPadding;

  /// 指示器装饰 / Indicator decoration
  final Decoration? indicator;

  /// 选中样式 / Selected text style
  final TextStyle? selectedStyle;

  /// 未选中样式 / Unselected text style
  final TextStyle? unselectedStyle;

  /// 高度 / Height
  final double height;

  /// 是否可滚动 / Is scrollable
  final bool isScrollable;

  /// 自定义构建器 / Custom builder
  final Widget? Function(BuildContext, List<String>, int, Function(int)?)? customBuilder;

  /// 圆角半径 / Border radius
  final double? radius;

  /// 边框宽度 / Border width
  final double? borderWidth;

  /// 边框颜色 / Border color
  final Color? borderColor;

  /// 是否显示背景 / Show background
  final bool showBackground;

  const TabBarStyleConfig({
    this.tabAlignment,
    this.labelPadding,
    this.indicator,
    this.selectedStyle,
    this.unselectedStyle,
    required this.height,
    required this.isScrollable,
    this.customBuilder,
    this.radius,
    this.borderWidth,
    this.borderColor,
    this.showBackground = true,
  });

  // ==================== 预设样式 / Preset Styles ====================

  /// 圆角样式 / Round style
  factory TabBarStyleConfig.round(BuildContext context, {bool isScrollable = true}) {
    return TabBarStyleConfig(
      tabAlignment: isScrollable ? TabAlignment.start : null,
      labelPadding: EdgeInsets.symmetric(horizontal: 15.gw),
      height: 40.gw,
      isScrollable: isScrollable,
      radius: 20.0.gr,
      indicator: BoxDecoration(
        color: context.theme.primaryColor,
        borderRadius: BorderRadius.circular(20.gr),
        border: Border.all(color: context.theme.primaryColor, width: 1.0),
      ),
      selectedStyle: context.textTheme.secondary,
      unselectedStyle: context.textTheme.primary.copyWith(color: context.theme.primaryColor),
    );
  }

  /// 矩形样式 / Rectangular style
  factory TabBarStyleConfig.rectangular(BuildContext context, {bool isScrollable = true}) {
    return TabBarStyleConfig(
      tabAlignment: isScrollable ? TabAlignment.start : null,
      labelPadding: EdgeInsets.symmetric(horizontal: 15.gw),
      height: 40.gw,
      isScrollable: isScrollable,
      radius: 8.0.gr,
      indicator: BoxDecoration(
        color: context.theme.primaryColor,
        borderRadius: BorderRadius.circular(8.gw),
        border: Border.all(color: context.theme.primaryColor, width: 1.0),
      ),
      selectedStyle: context.textTheme.secondary,
      unselectedStyle: context.textTheme.primary.copyWith(color: context.theme.primaryColor),
    );
  }

  /// 下划线样式 / Line/underline style
  factory TabBarStyleConfig.line(BuildContext context, {bool isScrollable = true}) {
    return TabBarStyleConfig(
      tabAlignment: isScrollable ? TabAlignment.start : null,
      labelPadding: EdgeInsets.symmetric(horizontal: 10.gw),
      indicator: TCUnderlineTabIndicator(
        indicatorHeight: 2,
        isRound: true,
        indicatorBottom: -6,
        insets: EdgeInsets.fromLTRB(0, 0, 0, 8),
        borderSide: BorderSide(width: 2.0.gw, color: context.theme.primaryColor),
      ),
      selectedStyle: context.textTheme.primary,
      unselectedStyle: context.textTheme.regular,
      height: 40.gw,
      isScrollable: isScrollable,
      showBackground: false,
    );
  }

  /// 梯形样式 / Trapezoid style
  factory TabBarStyleConfig.trapezoid(BuildContext context, {bool isScrollable = true}) {
    return TabBarStyleConfig(
      tabAlignment: isScrollable ? TabAlignment.start : null,
      labelPadding: EdgeInsets.symmetric(horizontal: 15.gw),
      height: 48.gw,
      isScrollable: isScrollable,
      customBuilder: (ctx, data, currentIndex, onTap) =>
          TrapezoidTabBuilder.build(ctx, data, currentIndex, onTap, isScrollable),
    );
  }

  // ==================== 复制方法 / Copy Methods ====================

  /// 复制并覆盖属性 / Copy with overrides
  TabBarStyleConfig copyWith({
    TabAlignment? tabAlignment,
    EdgeInsets? labelPadding,
    Decoration? indicator,
    TextStyle? selectedStyle,
    TextStyle? unselectedStyle,
    double? height,
    bool? isScrollable,
    Widget? Function(BuildContext, List<String>, int, Function(int)?)? customBuilder,
    double? radius,
    double? borderWidth,
    Color? borderColor,
    bool? showBackground,
  }) {
    return TabBarStyleConfig(
      tabAlignment: tabAlignment ?? this.tabAlignment,
      labelPadding: labelPadding ?? this.labelPadding,
      indicator: indicator ?? this.indicator,
      selectedStyle: selectedStyle ?? this.selectedStyle,
      unselectedStyle: unselectedStyle ?? this.unselectedStyle,
      height: height ?? this.height,
      isScrollable: isScrollable ?? this.isScrollable,
      customBuilder: customBuilder ?? this.customBuilder,
      radius: radius ?? this.radius,
      borderWidth: borderWidth ?? this.borderWidth,
      borderColor: borderColor ?? this.borderColor,
      showBackground: showBackground ?? this.showBackground,
    );
  }

  // ==================== 便捷方法 / Convenience Methods ====================

  /// 修改指示器属性 / Modify indicator properties
  TabBarStyleConfig withIndicator({
    Color? color,
    double? width,
    double? height,
    double? bottom,
  }) {
    Decoration? newIndicator = indicator;

    if (newIndicator is BoxDecoration && color != null) {
      newIndicator = newIndicator.copyWith(
        color: color,
        border: newIndicator.border != null ? Border.all(color: color, width: 1.0) : null,
      );
    } else if (newIndicator is TCUnderlineTabIndicator) {
      newIndicator = TCUnderlineTabIndicator(
        borderSide: color != null
            ? BorderSide(width: newIndicator.borderSide.width, color: color)
            : newIndicator.borderSide,
        insets: newIndicator.insets,
        indicatorBottom: bottom ?? newIndicator.indicatorBottom,
        indicatorWidth: width ?? newIndicator.indicatorWidth,
        indicatorHeight: height ?? newIndicator.indicatorHeight,
        isRound: newIndicator.isRound,
      );
    }

    return copyWith(indicator: newIndicator);
  }

  /// 修改文字颜色 / Modify label colors
  TabBarStyleConfig withLabelColors({Color? selected, Color? unselected}) {
    return copyWith(
      selectedStyle: selected != null ? selectedStyle?.copyWith(color: selected) : null,
      unselectedStyle: unselected != null ? unselectedStyle?.copyWith(color: unselected) : null,
    );
  }
}

/// 样式覆盖回调类型 / Style overrides callback type
typedef TabBarStyleOverrides = TabBarStyleConfig Function(TabBarStyleConfig config);
