import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/account/account.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AccountPositionDetailState extends Equatable {
  final PositionEntity? model;
  final int? id;
  final DataStatus dataStatus;

  const AccountPositionDetailState({
    this.model,
    this.id,
    this.dataStatus = DataStatus.idle,
  });

  AccountPositionDetailState copyWith({
    PositionEntity? model,
    int? id,
    DataStatus? dataStatus,
  }) {
    return AccountPositionDetailState(
      model: model ?? this.model,
      id: id ?? this.id,
      dataStatus: dataStatus ?? this.dataStatus,
    );
  }

  @override
  List<Object?> get props => [
    model,
    id,
    dataStatus,
  ];
}