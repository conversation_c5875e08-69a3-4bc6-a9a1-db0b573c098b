import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/features/account_v2/1_common/order_history/order_history_sub_view/order_history_sub_view.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import 'order_history_cubit.dart';
import 'order_history_state.dart';

class OrderHistoryPage extends StatelessWidget {
  final bool isHistory;
  const OrderHistoryPage({super.key, this.isHistory = false});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<OrderHistoryCubit>();
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: context.theme.cardColor,
        title: Text('transactionHistory'.tr()),
      ),
      body: BlocBuilder<OrderHistoryCubit, OrderHistoryState>(
        builder: (context, state) {
          return Column(
            children: [
              CommonTabBar.withAutoKey(
                state.details.entries.map((e) => "${tr(isHistory ? e.key.historyKey : e.key.nameKey)}${e.value.countIfNotEmpty}").toList(),
                currentIndex: state.selectedIndex,
                onTap: (index) => cubit.updateSelIndex(index),
                style: CommonTabBarStyle.line,
                isScrollable: false,
              ),
              Expanded(
                  child: DirectSlideView(
                pages: state.details.entries.map((e) {
                  return OrderHistorySubView(
                    type: e.key,
                    orderListState: e.value,
                    onFetch: (isLoadMore) =>
                        cubit.fetchMarketOrderList(type: e.key, orderListState: e.value, isLoadMore: isLoadMore),
                  );
                }).toList(),
                pageIndex: state.selectedIndex,
                onPageChanged: (int pageIndex) => cubit.updateSelIndex(pageIndex),
              ))
            ],
          );
        },
      ),
    );
  }
}
