import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/assets_card.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';

class BalanceCard extends StatelessWidget {
  const BalanceCard({
    super.key,
    required this.balance,
    required this.min,
    required this.max,
    this.onTapAdd,
  });

  final double balance;
  final double min;
  final double max;
  final VoidCallback? onTapAdd;

  @override
  Widget build(BuildContext context) {
    final effectiveStyle = AssetsCardStyle.fromSkin();
    final textColor = effectiveStyle.textColor;
    return Container(
      height: 160.gw,
      decoration: BoxDecoration(
        image: DecorationImage(image: AssetImage(effectiveStyle.bgImgPath!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.fromLTRB(12.gw, 16.gw, 8.gw, 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "availableBalance".tr(),
                        style: TextStyle(
                          color: textColor,
                          fontSize: 15.gsp,
                        ),
                      ),
                      SizedBox(height: 8.gw),
                      FlipText(
                        balance,
                        isCurrency: true,
                        style: context.textTheme.secondary.copyWith(color: textColor, fontSize: 22.gsp),
                        dropdownIconColor: textColor ?? context.colorTheme.buttonPrimary,
                      ),
                    ],
                  ),
                  OutlinedButton(
                    onPressed: onTapAdd ?? () {},
                    style: OutlinedButton.styleFrom(
                      foregroundColor: textColor,
                      side: BorderSide(color: textColor ?? context.colorTheme.buttonPrimary),
                    ),
                    child: Row(
                      children: [
                        Text('addBank'.tr()),
                        Icon(Icons.add_rounded, color: textColor),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            height: 63.gw,
            padding: EdgeInsets.symmetric(horizontal: 12.gw),
            decoration: effectiveStyle.bgImgPath != null
                ? null
                : BoxDecoration(
                    color: Colors.white.withNewOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.gr),
                  ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 4.gw,
                  children: [
                    Text(
                      'min'.tr(),
                      style: context.textTheme.secondary.copyWith(
                        fontSize: 14.gsp,
                        color: textColor,
                      ),
                    ),
                    FlipText(
                      min,
                      isCurrency: false,
                      style: context.textTheme.secondary.copyWith(color: textColor, fontSize: 18.gsp),
                      dropdownIconColor: textColor ?? context.colorTheme.buttonPrimary,
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  spacing: 4.gw,
                  children: [
                    Text(
                      'max'.tr(),
                      style: context.textTheme.secondary.copyWith(
                        fontSize: 14.gsp,
                        color: textColor,
                      ),
                    ),
                    FlipText(
                      max,
                      isCurrency: false,
                      style: context.textTheme.secondary.copyWith(color: textColor, fontSize: 18.gsp),
                      dropdownIconColor: textColor ?? context.colorTheme.buttonPrimary,
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
