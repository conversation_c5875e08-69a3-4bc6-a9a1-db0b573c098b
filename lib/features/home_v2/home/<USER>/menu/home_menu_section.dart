import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/system_util.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

import 'home_menu_cell.dart';

/// 首页菜单区域（通用样式）
class HomeMenuSection extends StatelessWidget {
  const HomeMenuSection({super.key});

  @override
  Widget build(BuildContext context) {
    final data = _buildMenuData(context);

    return Container(
      margin: _getContainerMargin(),
      padding: _getContainerPadding(),
      decoration: _getContainerDecoration(context),
      child: AnimationLimiter(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            for (int i = 0; i < data.length; i++) ...[
              Expanded(
                child: AnimationConfiguration.staggeredList(
                  position: i,
                  duration: const Duration(milliseconds: 400),
                  child: SlideAnimation(
                    horizontalOffset: 50.0,
                    child: FadeInAnimation(
                      child: Bounceable(
                        onTap: data[i].onTap,
                        child: HomeMenuCell(
                          icon: data[i].icon,
                          title: data[i].title,
                          iconAlt: data[i].iconAlt,
                          iconAltDark: data[i].iconAltDark,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              if (i < data.length - 1) SizedBox(width: 5.gw),
            ],
          ],
        ),
      ),
    );
  }

  List<_MenuItemData> _buildMenuData(BuildContext context) {
    final h5 = _MenuItemData(
      icon: 'assets/svg/home/<USER>',
      title: 'homeH5Title',
      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeAboutUs)),
      iconAlt: Assets.homeH5Icon,
      iconAltDark: Assets.homeH5IconDark,
    );
    final ai = _MenuItemData(
      icon: 'assets/svg/home/<USER>',
      title: 'aiAnalysis',
      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeAIChat)),
      iconAlt: Assets.homeH2Icon,
      iconAltDark: Assets.homeH2IconDark,
    );
    final service = _MenuItemData(
      icon: 'assets/svg/home/<USER>',
      title: 'homeH4Title',
      onTap: () => SystemUtil.contactService(context),
      iconAlt: Assets.homeH4Icon,
      iconAltDark: Assets.homeH4IconDark,
    );
    final serviceAlt = _MenuItemData(
      icon: 'assets/svg/home/<USER>',
      title: 'homeH4Title',
      onTap: () => SystemUtil.contactService(context),
    );
    final vip = _MenuItemData(
      icon: 'assets/svg/home/<USER>',
      title: 'vip',
      onTap: () =>
          AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeMissionCenter, arguments: true)),
      iconAlt: Assets.homeH5Icon,
      iconAltDark: Assets.homeH5IconDark,
    );
    final task = _MenuItemData(
      icon: 'assets/svg/home/<USER>',
      title: 'homeH2Title',
      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeMissionCenter)),
    );

    // 先按 flavor 决定组合，再回落到皮肤
    switch (AppConfig.instance.flavor) {
      case Flavor.yhxt:
        return [h5, ai, serviceAlt]; // yhxt: H5, AI, Service Alt
      case Flavor.dyzb:
        return [h5, serviceAlt, vip, task]; // dyzb: H5, Service Alt, VIP, Task
      case Flavor.xyzq:
        return [h5, ai, serviceAlt, vip, task]; // xyzq: 去掉 service 正常版
      default:
        break;
    }

    switch (AppConfig.instance.skinStyle) {
      case AppSkinStyle.kTemplateD:
        return [h5, ai, vip, task]; // bszb: H5, AI, VIP, Task
      case AppSkinStyle.kTemplateA:
        return [h5, ai, task, service]; // tempa: H5, AI, Task, Service
      case AppSkinStyle.kGP:
        return [h5, ai, service]; // GP/rsyp/pre/tempb 等
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
        return [h5, ai, service, serviceAlt, vip, task];
      case AppSkinStyle.kZangGolden:
        return [];
    }
  }

  EdgeInsetsGeometry? _getContainerMargin() {
    return switch (AppConfig.instance.skinStyle) {
      _ when AppConfig.instance.flavor == Flavor.yhxt => EdgeInsets.symmetric(horizontal: 15.gw),
      AppSkinStyle.kTemplateA => EdgeInsets.symmetric(horizontal: 10.gw),
      _ => null,
    };
  }

  EdgeInsetsGeometry? _getContainerPadding() {
    return switch (AppConfig.instance.skinStyle) {
      _ when AppConfig.instance.flavor == Flavor.yhxt => EdgeInsets.symmetric(vertical: 8.gw),
      AppSkinStyle.kTemplateA ||
      AppSkinStyle.kTemplateC ||
      AppSkinStyle.kTemplateD =>
        const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      AppSkinStyle.kGP => EdgeInsets.symmetric(horizontal: 16.gw),
      _ => null,
    };
  }

  BoxDecoration? _getContainerDecoration(BuildContext context) {
    return switch (AppConfig.instance.skinStyle) {
      _ when AppConfig.instance.flavor == Flavor.yhxt => BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.gw),
        ),
      AppSkinStyle.kTemplateA => BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(4.gr),
          boxShadow: const [
            BoxShadow(
              color: Color(0x0F354677),
              offset: Offset(0, 4),
              blurRadius: 10,
              spreadRadius: 0,
            ),
          ],
        ),
      _ => null,
    };
  }
}

/// 菜单项数据
class _MenuItemData {
  final String icon;
  final String title;
  final VoidCallback onTap;
  final String? iconAlt;
  final String? iconAltDark;

  const _MenuItemData({
    required this.icon,
    required this.title,
    required this.onTap,
    this.iconAlt,
    this.iconAltDark,
  });
}
