import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/app_navigation_handler.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class NotificationPopup extends StatefulWidget {
  final List<NotificationEntity> notifications;

  const NotificationPopup({super.key, required this.notifications});

  @override
  State<NotificationPopup> createState() => _NotificationPopupState();
}

class _NotificationPopupState extends State<NotificationPopup> {
  int currentIndex = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.notifications.length,
        (index) => Container(
          width: 8.0,
          height: 8.0,
          margin: EdgeInsets.symmetric(horizontal: 4.0.gw),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: currentIndex == index ? context.theme.primaryColor : Colors.white.withNewOpacity(0.5),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.zero,
      child: SizedBox(
        width: 0.9.gsw,
        height: 0.45.gsh,
        child: Stack(
          children: [
            // Background PageView
            PageView.builder(
              controller: _pageController,
              itemCount: widget.notifications.length,
              onPageChanged: (index) => setState(() => currentIndex = index),
              itemBuilder: (context, index) {
                return Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(Assets.notificationBG),
                      fit: BoxFit.fill,
                    ),
                  ),
                );
              },
            ),

            // Content
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.gw),
                  child: Row(
                    children: [
                      Image.asset(
                        Assets.notificationTitleLogo,
                        width: 50.gw,
                        height: 50.gw,
                      ),
                      Text(
                        'latestActivities'.tr(),
                        style: context.textTheme.primary.w600.copyWith(
                          color: context.theme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                16.verticalSpace,
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    itemCount: widget.notifications.length,
                    onPageChanged: (index) => setState(() => currentIndex = index),
                    itemBuilder: (context, index) {
                      final notification = widget.notifications[index];
                      return SingleChildScrollView(
                        padding: EdgeInsets.symmetric(horizontal: 16.gw),
                        child: GestureDetector(
                          onTap: () => AppNavigationHandler.handleNavigation(context,
                              jumpType: notification.jumpType, jumpUrl: notification.jumpUrl),
                          child: Column(
                            children: [
                              Text(
                                notification.title,
                                style: context.textTheme.primary.fs16.w600,
                                textAlign: TextAlign.center,
                              ),
                              16.verticalSpace,
                              if (notification.imageUrl.isNotEmpty)
                                CachedNetworkImage(
                                  width: 0.70.gsw,
                                  imageUrl: notification.imageUrl,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => ShimmerWidget(
                                    width: double.infinity,
                                    color: Colors.transparent,
                                    height: 200.gw,
                                  ),
                                  errorWidget: (context, url, error) => Icon(
                                    Icons.broken_image_outlined,
                                    size: 48.gw,
                                    color: Colors.transparent,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                8.verticalSpace,
                _buildPageIndicator(),
                20.verticalSpace,
              ],
            ),
          ],
        ),
      ),
    );
  }
}
