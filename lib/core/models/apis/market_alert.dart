import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/market/warn_response_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

class MarketAlertApi {
  // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/getSymbolWarnBySymbolUsingGET_1
  static Future<WarnResponse?> getWarnByInstrument(String instrument) async {
    final res = await Http().request<WarnResponse>(
      ApiEndpoints.getWarnBySymbol,
      queryParameters: {
        "market": instrument.split('|')[0],
        "securityType": instrument.split('|')[1],
        "symbol": instrument.split('|')[2],
      },
      method: HttpMethod.get,
    );
    return res.data;
  }

  // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/getSymbolWarnListUsingGET_1
  static Future<WarnResponseEntity?> getWarnList({
    required String pageNumber,
    required String pageSize,
    required String market,
  }) async {
    final res = await Http().request<WarnResponseEntity>(
      ApiEndpoints.getWarnList,
      queryParameters: {
        'pageNumber': pageNumber,
        'pageSize': pageSize,
        'market': market,
      },
      method: HttpMethod.get,
    );
    return res.data;
  }

  // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/addSymbolWarnUsingPOST_1
  static Future<bool> addWarn(WarnResponse warnResponse) async {
    final res = await Http().request<bool>(
      ApiEndpoints.addWarn,
      method: HttpMethod.post,
      params: warnResponse.toJson(),
    );
    return res.code == 0;
  }

  // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/updateSymbolWarnUsingPUT_1
  static Future<bool> updateWarn(WarnResponse warnResponse) async {
    final res = await Http().request<bool>(
      ApiEndpoints.updateWarn,
      method: HttpMethod.put,
      params: warnResponse.toJson(),
    );
    return res.code == 0;
  }

  // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/deleteSymbolWarnUsingDELETE_1
  static Future<bool> deleteWarn(String id) async {
    final res = await Http().request<bool>(
      ApiEndpoints.deleteWarn,
      method: HttpMethod.delete,
      queryParameters: {'id': id},
    );
    return res.code == 0;
  }
}
