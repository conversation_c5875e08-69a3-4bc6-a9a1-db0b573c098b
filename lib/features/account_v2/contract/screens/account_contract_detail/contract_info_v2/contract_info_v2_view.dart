import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/convert_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/contract/widgets/pop_over.dart';
import 'package:gp_stock_app/features/contract/widgets/app_info_bottom_sheet.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import 'contract_info_v2_cubit.dart';
import 'contract_info_v2_state.dart';

/// 合约详细信息
class ContractInfoV2Page extends StatelessWidget {
  const ContractInfoV2Page({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ContractInfoV2Cubit, ContractInfoV2State>(
      builder: (context, state) {
        final model = state.data;
        // Only show shimmer during initial loading, not during polling updates
        if (state.summaryDataState.isLoading) {
          return _buildShimmerLayout(context);
        }

        final totalTradingCapitalRow = _buildInfoRowEx(context, 'totalTradingCapital', model.totalPower); // 总操盘资金
        final initialMarginRow = _buildInfoRowEx(context, 'initialMargin', model.initCash); // 起始保证金
        final lossWarningLineRow = _buildInfoRowEx(context, 'lossWarningLine', model.warnRemindAmount); // 亏损警戒线
        final liquidationLineRow = _buildInfoRowEx(context, 'liquidationLine', model.closeRemindAmount); // 亏损平仓线
        final interestRateInterestRow =
            _buildInfoRowEx(context, 'interestRateInterest', model.interestRate, isPercent: true); // 利率利息
        final interestAmountRow = _buildInfoRowEx(context, 'interestAmount', model.receivableInterest); // 累计利息
        final marketValueRow = _buildInfoRowEx(context, 'marketValue', model.positionAmount); // 市值
        final expandedContractRow = _buildInfoRowEx(context, 'expandedContract', model.expendAmount); // 扩大保证金
        final supplementLossRow = _buildInfoRowEx(context, 'supplementLoss', model.coverLossAmount); // 追加保证金
        final floatingProfitLossRow = _buildInfoRowEx(
          context,
          'floatingProfitLoss',
          model.accountWinAmount,
          labelSuffix: PopOver(note: "floating_profit_loss_note".tr()),
        ); // 浮动盈亏
        final profitLossRatioRow = _buildInfoRowEx(context, 'profitLossRatio', model.winRate, isPercent: true); // 亏损率
        final withdrawalAmountRow = _buildInfoRowEx(context, 'withdrawalAmount', model.withdrawAmount); // 提现金额
        final frozenRow = _buildInfoRowEx(context, 'frozen', model.freezePower); // 冻结金额
        // final liquidationAmountRow = _buildInfoRowEx(context, 'liquidationAmount', model.negativeAmount); // 穿仓金额

        // 体验和彩金 model.type == 2 || model.type == 3
        var renewalStatusRow = _buildInfoRowEx(context, 'contractRenewalStatus', "maturitySettlement".tr());
        if (model.type == 1) {
          // 普通合约 general contract
          renewalStatusRow = _buildInfoRowEx(
            context,
            'contractRenewalStatus',
            model.isAutoRenew ? "autoRenewal".tr() : "maturitySettlement".tr(),
          );
        }
        final giftAmountRow = _buildInfoRowEx(context, 'giftAmount', model.giveAmount); // 彩金金额
        final contractNetAssetsRow = _buildInfoRowEx(context, 'contractNetAssets', model.contractAssetAmount); // 合约净资产
        final distanceToWarningLineRow =
            _buildInfoRowEx(context, 'distanceToWarningLine', model.gapWarnRemindAmount); // 距离预警线金额
        final distanceToLiquidationLineRow =
            _buildInfoRowEx(context, 'distanceToLiquidationLine', model.gapCloseRemindAmount); // 距离强平线金额

        final tradingLineRow = _buildInfoRowEx(context, 'tradingLine', model.tradingLine.tr(),
            labelSuffix: model.backType == 2 ? PopOver(note: "tradingLineNote".tr()) : null); // 操盘线
        List<Widget> rows = [];

        if (model.type == 2) {
          // 体验类型
          rows = [
            totalTradingCapitalRow, // 总操盘资金
            _buildInfoRowEx(context, 'giftAmount', model.totalFinance), // 彩金资金
            lossWarningLineRow, // 亏损警戒线
            liquidationLineRow, // 亏损平仓线
            // marketValueRow, // 市值
            floatingProfitLossRow, // 浮动盈亏
            profitLossRatioRow, // 亏损率 base rate 0.2  * config
            frozenRow, // 冻结金额
            contractNetAssetsRow, // 合约净资产
            distanceToWarningLineRow, // 距离预警线金额
            distanceToLiquidationLineRow, // 距离强平线金额
            renewalStatusRow, // 合约续期状态
          ];
        } else {
          // 普通类型
          rows = [
            totalTradingCapitalRow, // 总操盘资金
            initialMarginRow, // 起始保证金
            lossWarningLineRow, // 亏损警戒线
            liquidationLineRow, // 亏损平仓线
            interestRateInterestRow, // 利率利息
            interestAmountRow, // 利息总额
            marketValueRow, // 市值
            expandedContractRow, // 扩大保证金
            supplementLossRow, // 追加保证金
            floatingProfitLossRow, // 浮动盈亏
            profitLossRatioRow, // 亏损率
            withdrawalAmountRow, // 提现金额
            frozenRow, // 冻结金额
            // liquidationAmountRow, // 穿仓金额
            contractNetAssetsRow, // 合约净资产
            distanceToWarningLineRow, // 距离预警线金额
            distanceToLiquidationLineRow, // 距离强平线金额
            renewalStatusRow, // 合约续期状态
          ];

          // 彩金类型
          if (model.type == 3) {
            rows.insert(2, giftAmountRow); // 插入到亏损警戒线之前
          }

          rows.add(tradingLineRow);
        }

        return _buildContentLayout(context, model: model, rows: rows);
      },
    );
  }

  Widget _buildContentLayout(BuildContext context,
      {required Contract model, required List<Widget> rows}) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: context.theme.cardColor,
        title: Text('contractDetails'.tr()),
        actions: [
          TextButton(
            onPressed: () {
              final periods = {
                1: 1,
                2: 7,
                3: 30,
              };
              showAppInfoBottomSheet(
                context,
                capitalA: (model.totalPower - model.initCash).toStringAsFixed(0),
                capitalB: model.initCash.toStringAsFixed(0),
                interestRate: '${model.interestRate}',
                interestAmount: '${model.interestAmount}',
                period: periods[model.periodType] ?? 1,
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: context.colorTheme.tabActive,
            ),
            child: Text(
              'tradingAgreement'.tr(),
              style: context.textTheme.title.w500.fs10,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(12.gr),
        child: Column(
          spacing: 12.gw,
          children: [
            ShadowBox(
              child: Column(
                children: [
                  _buildInfoRowEx(context, 'contractType', model.label),
                  _buildInfoRowEx(context, 'period', 'contract.period_${model.periodType}'.tr()),
                  _buildInfoRowEx(context, 'date',
                      '${ConvertHelper.formatDateGeneral(model.openTime)} - ${ConvertHelper.formatDateGeneral(model.expireTime)}'),
                  _buildInfoRowEx(context, 'contractMultiple', '${model.multiple}${'x'.tr()}'),
                ],
              ),
            ),
            ShadowBox(
              child: Column(
                children: rows,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Shimmer layout for loading state
  Widget _buildShimmerLayout(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: context.theme.cardColor,
        title: Text('contractDetails'.tr()),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.all(16.gr),
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(12.gr),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(16.gr),
                child: Column(
                  children: List.generate(4, (index) => _buildShimmerRow(context)),
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.all(16.gr),
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(12.gr),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(16.gr),
                child: Column(
                  children: List.generate(12, (index) => _buildShimmerRow(context)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerRow(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShimmerWidget(
            height: 16.gw,
            width: 100.gw,
            radius: 4.gr,
            color: context.theme.scaffoldBackgroundColor,
          ),
          ShimmerWidget(
            height: 16.gw,
            width: 80.gw,
            radius: 4.gr,
            color: context.theme.scaffoldBackgroundColor,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRowEx(BuildContext context, String labelKey, dynamic value,
      {Widget? labelSuffix, bool isPercent = false}) {
    String textValue;

    if (value is num) {
      textValue = value.toStringAsFixed(2);
      if (isPercent) textValue += '%';
    } else if (value != null) {
      textValue = value.toString();
    } else {
      textValue = '0.00';
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                labelKey.tr(), // 内部自动翻译
                style: context.textTheme.regular,
              ),
              if (labelSuffix != null) labelSuffix
            ],
          ),
          Expanded(
            child: Text(
              textValue,
              style: context.textTheme.primary.w700.ffAkz,
              maxLines: 2,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
