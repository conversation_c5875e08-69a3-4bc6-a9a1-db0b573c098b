import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/logic/theme/theme_cubit.dart';

/// 菜单单元格组件（通用样式）
class HomeMenuCell extends StatelessWidget {
  final String icon;
  final String title;
  final String? iconAlt;
  final String? iconAltDark;

  const HomeMenuCell({
    super.key,
    required this.icon,
    required this.title,
    this.iconAlt,
    this.iconAltDark,
  });

  @override
  Widget build(BuildContext context) {
    final isYhxt = AppConfig.instance.flavor == Flavor.yhxt;

    // GP 皮肤使用主题感知的图标 + 文字堆叠布局
    if (!isYhxt && AppConfig.instance.skinStyle == AppSkinStyle.kGP) {
      return _buildGpLayout(context);
    }

    // 其他皮肤使用标准布局
    return _buildStandardLayout(context);
  }

  /// GP 样式布局（主题感知图标 + 文字堆叠）
  Widget _buildGpLayout(BuildContext context) {
    return BlocSelector<ThemeCubit, ThemeState, ThemeMode>(
      selector: (state) => state.themeMode,
      builder: (context, themeMode) {
        final iconPath = _getThemeAwareIcon(
          context,
          themeMode,
          iconAlt ?? icon,
          iconAltDark ?? icon,
        );
        return Stack(
          children: [
            Image.asset(iconPath, width: 110.gw, height: 32.gw),
            Positioned(
              left: 50.gw,
              bottom: 10.gw,
              top: 10.gw,
              child: FittedBox(
                child: Text(
                  title.tr(),
                  style: context.textTheme.primary.fs18.w700
                      .copyWith(color: context.theme.primaryColor),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 标准样式布局
  Widget _buildStandardLayout(BuildContext context) {
    final shouldUseIconButton =
        AppConfig.instance.skinStyle == AppSkinStyle.kTemplateA;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        if (shouldUseIconButton)
          IconButton(
            iconSize: 40.gr,
            style: IconButton.styleFrom(
              backgroundColor:
                  context.theme.primaryColorLight.withValues(alpha: 0.1),
            ),
            icon: IconHelper.loadAsset(
              icon,
              width: 24.gw,
              height: 24.gw,
              color: null,
              shouldEnableThemeGradient: true,
            ),
            onPressed: () {},
          )
        else
          IconHelper.loadAsset(
            icon,
            width: 30.gw,
            height: 30.gw,
            color: _getIconColor(context),
          ),
        Text(
          title.tr(),
          style: _getTextStyle(context),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _getThemeAwareIcon(
    BuildContext context,
    ThemeMode themeMode,
    String lightIcon,
    String darkIcon,
  ) {
    if (themeMode == ThemeMode.system) {
      return MediaQuery.of(context).platformBrightness == Brightness.light
          ? lightIcon
          : darkIcon;
    }
    return themeMode == ThemeMode.light ? lightIcon : darkIcon;
  }

  Color? _getIconColor(BuildContext context) {
    return switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateC || AppSkinStyle.kTemplateD =>
        context.theme.primaryColor,
      _ => null,
    };
  }

  TextStyle _getTextStyle(BuildContext context) {
    return switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateA => context.textTheme.active.fs12,
      _ => context.textTheme.primary.fs12,
    };
  }
}
