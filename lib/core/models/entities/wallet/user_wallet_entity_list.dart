import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/user_wallet_entity_list.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/user_wallet_entity_list.g.dart';

@JsonSerializable()
class UserWalletEntityList {
  final List<UserWalletEntity> list;

  const UserWalletEntityList({
    this.list = const [],
  });

  factory UserWalletEntityList.fromJson(Map<String, dynamic> json) => $UserWalletEntityListFromJson(json);

  Map<String, dynamic> toJson() => $UserWalletEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class UserWalletEntity {
  final String bankCode;
  final String createTime;
  final String creator;
  final String icon;
  final int id;
  final String payAddress;
  final String payTypeCode;
  final String payWayCode;
  final int siteId;
  final String type;
  final String updateTime;
  final String updater;
  final int userId;

  const UserWalletEntity({
    this.bankCode = '',
    this.createTime = '',
    this.creator = '',
    this.icon = '',
    this.id = 0,
    this.payAddress = '',
    this.payTypeCode = '',
    this.payWayCode = '',
    this.siteId = 0,
    this.type = '',
    this.updateTime = '',
    this.updater = '',
    this.userId = 0,
  });

  factory UserWalletEntity.fromJson(Map<String, dynamic> json) => $UserWalletEntityFromJson(json);

  Map<String, dynamic> toJson() => $UserWalletEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}