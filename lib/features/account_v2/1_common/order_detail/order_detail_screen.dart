import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/trade/order_detail.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/trade_action_helper.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';

import 'order_detail_cubit.dart';

class OrderDetailScreen extends StatelessWidget {
  const OrderDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('commissionDetails'.tr()),
      ),
      body: BlocBuilder<OrderDetailCubit, OrderDetailState>(
        builder: (context, state) {
          if (state.fetchStatus == DataStatus.loading) {
            return const Center(child: CircularProgressIndicator.adaptive());
          } else if (state.fetchStatus == DataStatus.failed) {
            return TableEmptyWidget();
          } else if (state.data != null) {
            return _buildOrderDetails(context, state.data!);
          } else {
            return TableEmptyWidget();
          }
        },
      ),
    );
  }

  Widget _buildOrderDetails(BuildContext context, OrderDetail order) {
    final isContract = order.contractId != 0;
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.gr),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShadowBox(
            child: Column(
              spacing: 10,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  spacing: 8,
                  children: [
                    Text(
                      order.symbolName,
                      style: context.textTheme.primary.w700.copyWith(color: context.colorTheme.textRegular),
                    ),
                    SymbolChip(name: order.market),
                    Text(
                      '(${order.symbol})',
                      style: context.textTheme.regular,
                    ),
                  ],
                ),
                _buildDetailRow(
                  context,
                  'accountType'.tr(),
                  isContract ? order.contractLabel : "spotTrading".tr(),
                ),
                _buildDetailRow(
                  context,
                  'status'.tr(),
                  EntrustStatus.fromValueByValue(order.status).label.tr(),
                  valueColor: EntrustStatus.fromValueByValue(order.status).color(context),
                ),
                if (order.dealTime.isNotEmpty) ...[
                  _buildDetailRow(
                    context,
                    'entrustTime'.tr(),
                    order.tradeTime,
                  ),
                ],
                if (order.tradeTime.isNotEmpty) ...[
                  _buildDetailRow(
                    context,
                    'transactionTime'.tr(),
                    order.dealTime,
                  ),
                ],
              ],
            ),
          ),
          20.verticalSpace,
          ShadowBox(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 10,
              children: [
                _buildDetailRow(
                  context,
                  'direction'.tr(),
                  TradeDirection.fromValue(order.direction).translationKey.tr(),
                  valueColor: TradeDirection.getColor(context, tradeDirection: TradeDirection.fromValue(order.direction)),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  context,
                  'orderType'.tr(),
                  TradeActionHelper.getActionLabel(order.direction, order.tradeType),
                  valueColor: TradeDirection.getColor(context, tradeDirection: TradeDirection.fromValue(order.direction)),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  context,
                  'orderPrice'.tr(),
                  order.tradePrice.toStringAsFixed(3),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  context,
                  'dealPrice'.tr(),
                  order.dealPrice.toStringAsFixed(3),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  context,
                  'total'.tr(),
                  order.tradeNum.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  context,
                  'filled'.tr(),
                  order.dealNum.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  context,
                  'totalProfitAndLoss'.tr(),
                  order.winAmount.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  context,
                  'transaction_amount'.tr(),
                  order.transactionAmount.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  context,
                  'service_charge'.tr(),
                  order.tradeFee.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                ...order.chargeDetailList.map(
                  (item) => _buildDetailRow(
                    context,
                    item.feeName,
                    item.fee.toString(),
                    valueFontWeight: FontWeight.bold,
                  ),
                ),
                _buildDetailRow(
                  context,
                  'sellType'.tr(),
                  order.direction == 1 ? "-" : _getSettleTypeLabel(order.settleType).tr(),
                  valueFontWeight: FontWeight.bold,
                ),
              ],
            ),
          ),
          20.verticalSpace,
        ],
      ),
    );
  }

  /// 结算类型 1:卖出 2:赎回 3:强平
  String _getSettleTypeLabel(int settleType) {
    return switch (settleType) {
      1 => 'sell',
      2 => 'redeem',
      3 => 'forceClose',
      _ => '-',
    };
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String? value, {
    Color? valueColor,
    FontWeight? valueFontWeight,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          maxLines: 3,
          style: context.textTheme.regular,
        ),
        Text(
          value ?? '',
          style: context.textTheme.regular.ffAkz.copyWith(
            color: valueColor ?? context.theme.primaryColor,
            fontWeight: valueFontWeight ?? FontWeight.normal,
          ),
        ),
      ],
    );
  }
}
