import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

/// 合约工具类
class ContractUtils {
  ContractUtils._();

  /// 生成合约标签
  /// 格式: (合约类型)期限倍数市场[ID]
  static String buildLabel({
    required int id,
    required int type,
    required int periodType,
    required String marketType,
    required int multiple,
  }) {
    final contractType = switch (type) {
      1 => ContractType.standard,
      2 => ContractType.experience,
      3 => ContractType.bonus,
      _ => ContractType.standard,
    };
    final typeName = contractType.translationKey.tr();
    final periodName = 'contract.period_$periodType'.tr();
    final multipleName = '$multiple${'times'.tr()}';
    final market = contractMarketTranslation[marketType]?.tr() ?? '';
    return '($typeName)$periodName$multipleName$market[$id]';
  }
}
