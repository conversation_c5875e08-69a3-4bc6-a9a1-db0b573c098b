import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/account/account.dart';
import 'package:gp_stock_app/core/models/entities/wallet/funds_record_list.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_records/interest_records_model.dart';

class AccountApi {


  /// 获取账号金额相关信息
  static Future<AccountInfo?> fetchAccountInfo({MarketCategory? category}) async {
    final res = await Http().request<AccountInfo>(
      ApiEndpoints.getAccountInfo,
      method: HttpMethod.get,
      queryParameters: {
        if (category != null) "dataType": category.code,
      },
      needShowToast: false,
    );
    return res.data;
  }

  static Future<List<int>> fetchMarketOpenAsset() async {
    final res = await Http().request(
      ApiEndpoints.getMarketOpenAsset,
      method: HttpMethod.get,
    );
    final x = (res.data is Map && res.data['list'] is List) ? res.data['list'] as List : [];

    final list = x.map((e) => int.tryParse(e.toString())).where((e) => e != null).cast<int>().toList();

    return list;
  }

  /// 更新推送token
  static Future<bool> updatePushToken(String deviceToken) async {
    final res = await Http().request(
      ApiEndpoints.updatePushToken,
      params: {"deviceToken": deviceToken},
    );
    return res.isSuccess;
  }


  /// 获取利息券列表
  static Future<InterestRecordsModel?> fetchInterestRecordList({
    required int pageNumber,
    required int? fromType,
  }) async {
    final response = await Http().request<InterestRecordsModel>(
      ApiEndpoints.getInterestRecord,
      method: HttpMethod.get,
      queryParameters: {
        if (fromType != null) 'fromType': fromType,
        'pageNum': pageNumber,
        'pageSize': 20,
      },
      needShowToast: false,
    );
    return response.data;
  }

  /// 获取现金资金记录
  static Future<FundsRecordList?> fetchFundsRecordList({
    required int? contractId,
    required int pageNum,
    required String? commentAssetId,
    String? fromType,
    String? startTime,
    String? endTime,
  }) async {
    final response = await Http().request<FundsRecordList>(
      ApiEndpoints.getAssetRecord,
      method: HttpMethod.get,
      queryParameters: {
        'contractId': contractId,
        'pageNumber': pageNum,
        'pageSize': 20,
        if (commentAssetId != null && contractId == null) 'commentAssetId': commentAssetId,
        if (fromType != null ) 'fromType': fromType,
        if (startTime != null) 'createTimeStart': startTime,
        if (endTime != null) 'createTimeEnd': endTime,
      },
      needShowToast: false,
    );
    return response.data;
  }
}
