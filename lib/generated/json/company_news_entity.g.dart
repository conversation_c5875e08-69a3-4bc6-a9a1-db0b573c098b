import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';

CompanyNewsEntity $CompanyNewsEntityFromJson(Map<String, dynamic> json) {
  final String? articleTitle = jsonConvert.convert<String>(json['article_title']);
  final String? articleMarket = jsonConvert.convert<String>(json['article_market']);
  final String? articleSymbol = jsonConvert.convert<String>(json['article_symbol']);
  final String? articleAuth = jsonConvert.convert<String>(json['article_auth']);
  final String? articleContent = jsonConvert.convert<String>(json['article_content']);
  final String? id = jsonConvert.convert<String>(json['id']);
  final String? articleDate = jsonConvert.convert<String>(json['article_date']);
  return CompanyNewsEntity(
    articleTitle: articleTitle ?? '',
    articleMarket: articleMarket ?? '',
    articleSymbol: articleSymbol ?? '',
    articleAuth: articleAuth ?? '',
    articleContent: articleContent ?? '',
    id: id ?? '',
    articleDate: articleDate ?? '',
  );
}

Map<String, dynamic> $CompanyNewsEntityToJson(CompanyNewsEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['article_title'] = entity.articleTitle;
  data['article_market'] = entity.articleMarket;
  data['article_symbol'] = entity.articleSymbol;
  data['article_auth'] = entity.articleAuth;
  data['article_content'] = entity.articleContent;
  data['id'] = entity.id;
  data['article_date'] = entity.articleDate;
  return data;
}

extension CompanyNewsEntityExtension on CompanyNewsEntity {
  CompanyNewsEntity copyWith({
    String? articleTitle,
    String? articleMarket,
    String? articleSymbol,
    String? articleAuth,
    String? articleContent,
    String? id,
    String? articleDate,
  }) {
    return CompanyNewsEntity(
      articleTitle: articleTitle ?? this.articleTitle,
      articleMarket: articleMarket ?? this.articleMarket,
      articleSymbol: articleSymbol ?? this.articleSymbol,
      articleAuth: articleAuth ?? this.articleAuth,
      articleContent: articleContent ?? this.articleContent,
      id: id ?? this.id,
      articleDate: articleDate ?? this.articleDate,
    );
  }
}