import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../activity_cubit.dart';

class TaskItemCard extends StatelessWidget {
  final TaskEntity task;

  const TaskItemCard({super.key, required this.task});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 12.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          8.verticalSpace,
          _buildContent(context),
          12.verticalSpace,
          _buildFooter(context),
          16.verticalSpace,
          _buildActionButton(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(getLocalizedText(task.name, context), style: context.textTheme.regular),
        Expanded(child: Text(task.name, style: context.textTheme.regular)),
        SizedBox(width: 10.gw),
        Row(
          children: [
            Icon(
              task.isCompleted != true ? Icons.close_outlined : Icons.check_circle,
              size: 16.gw,
              color: task.isCompleted != true ? context.colorTheme.stockRed : context.colorTheme.stockGreen,
            ),
            4.horizontalSpace,
            Text(
              task.isCompleted != true ? 'unfinished'.tr() : 'done'.tr(),
              style: context.textTheme.regular.fs12.copyWith(
                color: task.isCompleted != true ? context.colorTheme.stockRed : context.colorTheme.stockGreen,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'activityContent'.tr(),
              style: context.textTheme.regular.fs12.copyWith(
                color: context.colorTheme.textRegular,
              ),
            ),
            Spacer(),
            Expanded(
              flex: 7,
              child: Text(
                task.content,
                style: context.textTheme.primary.fs12.w700,
                softWrap: true,
                textAlign: TextAlign.right,
              ),
            ),
          ],
        ),
        8.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'taskReward'.tr(),
              style: context.textTheme.regular.fs12.copyWith(
                color: context.colorTheme.textRegular,
              ),
            ),
            Text(
              // if tab index is coupon change cash to '利息券'
              '${task.amount.toStringAsFixed(2)} ${task.amountType == 2 ? 'interestCoupon'.tr() : 'cash'.tr()}',
              style: context.textTheme.primary.w600.copyWith(
                color: context.theme.primaryColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${'completableTimes'.tr()}: ',
            style: context.textTheme.regular.fs12.copyWith(
              color: context.colorTheme.textRegular,
            ),
          ),
          Text(
            '${task.availableNum ?? 1}',
            style: context.textTheme.primary.w600.copyWith(
              color: context.theme.primaryColor,
            ),
          ),
        ],
      ),
      8.verticalSpace,
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${'validityPeriod'.tr()}: ',
            style: context.textTheme.regular.fs12.copyWith(
              color: context.colorTheme.textRegular,
            ),
          ),

          /// expireType	过期类型 1: 长期, 2: 单日, 3: 自定义
          if (task.expireType == 1) ...[
            Text(
              'permanent'.tr(),
              style: context.textTheme.regular.fs12,
            ),
          ] else if (task.expireType == 2) ...[
            Text(
              'valid_today'.tr(),
              style: context.textTheme.regular.fs12,
            ),
          ] else ...[
            Text(
              _formatDateRange(task.startTime, task.endTime),
              style: context.textTheme.regular.fs12,
            ),
          ]
        ],
      ),
    ]);
  }

  Widget _buildActionButton(BuildContext context) {
    final bool canCollect = task.isCompleted == true && task.isReceived != true;
    final taskReceived = task.isReceived ?? false;
    final taskCompleted = task.isCompleted ?? false;
    return SizedBox(
      width: double.infinity,
      height: 35.gw,
      child: TextButton(
        onPressed: canCollect ? () => context.read<ActivityCubit>().collectReward(task) : null,
        style: TextButton.styleFrom(
          backgroundColor:
              !taskReceived ? context.theme.primaryColor : context.theme.primaryColor.withValues(alpha: 0.5),
          padding: EdgeInsets.symmetric(vertical: 8.gw),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6.gr),
          ),
        ),
        child: Text(
          taskReceived
              ? 'received'.tr()
              : taskCompleted
                  ? 'get'.tr()
                  : 'unfinished'.tr(),
          style: context.textTheme.regular.copyWith(
            color: !taskReceived
                ? context.colorTheme.buttonPrimary
                : context.colorTheme.buttonPrimary.withValues(alpha: 0.5),
          ),
        ),
      ),
    );
  }
}

String _formatDateRange(String? start, String? end) {
  if (start == null && end == null) return 'longTerm'.tr();
  if (start == end) return 'range_today'.tr();
  if (start == null || end == null) return '';
  return '${DateFormat('yyyy-MM-dd').format(DateTime.parse(start))} - '
      '${DateFormat('yyyy-MM-dd').format(DateTime.parse(end))}';
}
