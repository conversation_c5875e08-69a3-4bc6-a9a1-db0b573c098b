import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/models/entities/account/account.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/balance_card.dart';

class WalletStyle {
  static Widget balanceCard({
    double min = 0.0,
    double max = 0.0,
    VoidCallback? onTapAdd,
  }) =>
      switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kZangGolden => BlocSelector<UserCubit, UserState, AccountInfo?>(
            selector: (state) => state.accountInfo,
            builder: (context, state) {
              return BalanceCard(balance: state?.usableCash ?? 0.0, min: min, max: max, onTapAdd: onTapAdd);
            },
          ),
        _ => SizedBox(),
      };

  static BorderRadius get borderRadius => BorderRadius.circular(8.gr);

  static TextStyle titleStyle(BuildContext context) => switch (AppConfig.instance.skinStyle) {
        _ => context.textTheme.title.w600,
      };
}
