import 'dart:io';

import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/models/apis/system.dart';
import 'package:gp_stock_app/core/models/entities/system/app_version_entity.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:gp_stock_app/shared/widgets/alert_dilaog/app_update_dialog.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import 'auth/auth_utils.dart';

class SystemUtil {
  /// 使用系统浏览器访问url
  static void openUrlOnSystemBrowser({required String url, LaunchMode mode = LaunchMode.platformDefault,}) async {
    try {
      // 1. URL预处理和检查
      url = StringUtil.fixUrl(url);

      final uri = Uri.tryParse(url);
      if (uri == null) {
        GPEasyLoading.showToast("invalid_address".tr());
        return;
      }

      GPEasyLoading.showLoading();

      final canLaunch = await canLaunchUrl(uri);

      if (canLaunch) {
        await launchUrl(uri, mode: mode);
      } else {
        throw '$uri load failed';
      }

      GPEasyLoading.dismiss();
    } catch (e) {
      GPEasyLoading.showToast("${'open_browser_failed'.tr()} $e");
      GPEasyLoading.dismiss();
      rethrow; // 使用rethrow而不是throw Exception(e)以保留原始堆栈跟踪
    }
  }

  /// 联系在线客服
  static void contactService(BuildContext context) {
    AuthUtils.verifyAuth(() {
      final state = context.read<SysSettingsCubit>().state;
      state.maybeWhen(
        loaded: (sysSettings, _) {
          final serviceUrl = sysSettings.service;
          if ( serviceUrl.isNotEmpty) {
            launchUrl(Uri.parse(serviceUrl), mode: LaunchMode.inAppBrowserView);
          } else {
            GPEasyLoading.showToast('something_went_wrong'.tr());
          }
        },
        orElse: () => GPEasyLoading.showToast('something_went_wrong'.tr()),
      );
    });
  }

  /// 检查 App 更新
  static Future<void> checkAppUpdate() async {
    try {
      final result = await SystemApi.queryAppUpdateInfo();
      if (result.isEmpty) return;

      // 优先选择通用版本(type=2)，否则根据平台选择
      AppVersionEntity? selectedUpdate = result.firstWhereOrNull((e) => e.type == 2);
      if (selectedUpdate == null) {
        if (Platform.isIOS) {
          selectedUpdate = result.firstWhereOrNull((e) => e.type == 1); // 苹果
        } else if (Platform.isAndroid) {
          selectedUpdate = result.firstWhereOrNull((e) => e.type == 0); // 安卓
        }
      }
      if (selectedUpdate == null) return;

      final shouldShow = await _shouldShowUpdateDialog(selectedUpdate.version);
      
      if (shouldShow && selectedUpdate.status == 1) {
        AppUpdateDialog(model: selectedUpdate).show();
      }
    } catch (_) {
      // 忽略更新检查错误
    }
  }

  /// 判断是否需要显示更新弹窗
  static Future<bool> _shouldShowUpdateDialog(String newVersion) async {
    // 如果是 x.x 的形式，补成 x.x.0
    if (RegExp(r'^\d+\.\d+$').hasMatch(newVersion)) {
      newVersion = "$newVersion.0";
    }
    // 检查 newVersion 格式是否符合 xx.xx.xx
    final versionRegex = RegExp(r'^\d+\.\d+\.\d+$');
    if (!versionRegex.hasMatch(newVersion)) {
      return false;
    }

    // 获取本地应用信息
    final packageInfo = await PackageInfo.fromPlatform();
    final currentVersion = packageInfo.version;

    // 将版本号按 "." 分割
    final currentParts = currentVersion.split('.');
    final newParts = newVersion.split('.');

    // 将每个部分转换成数字
    final currentMajor = int.parse(currentParts[0]);
    final currentMinor = int.parse(currentParts[1]);
    final currentPatch = int.parse(currentParts[2]);

    final newMajor = int.parse(newParts[0]);
    final newMinor = int.parse(newParts[1]);
    final newPatch = int.parse(newParts[2]);

    // 依次比较主版本号、次版本号和修订号
    if (newMajor > currentMajor) return true;
    if (newMajor == currentMajor) {
      if (newMinor > currentMinor) return true;
      if (newMinor == currentMinor && newPatch > currentPatch) return true;
    }
    return false;
  }
}
