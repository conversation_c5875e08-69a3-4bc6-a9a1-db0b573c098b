import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../home_v2_cubit.dart';
import '../home_v2_state.dart';
import '../widgets/banner/home_banner_section.dart';
import '../widgets/home_section_style.dart';
import '../widgets/marquee/home_marquee_section.dart';
import '../widgets/menu/home_menu_section.dart';
import '../widgets/market/home_market_section.dart';
import '../widgets/news/home_news_section.dart';

/// 通用首页布局
/// 根据 skinStyle 区分不同的布局样式和组件顺序
class HomePage extends StatelessWidget {
  final HomeV2State state;

  const HomePage({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HomeV2Cubit>();

    return _buildMainLayout(
      context,
      children: _buildChildren(context, cubit),
      onRefresh: cubit.loadAll,
    );
  }

  /// 构建主布局
  Widget _buildMainLayout(
    BuildContext context, {
    required List<Widget> children,
    required Future<void> Function() onRefresh,
  }) {
    final commonScaffold = Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        spacing: 10.gw,
        children: [
          Expanded(
            child: RefreshIndicator.adaptive(
              backgroundColor: context.theme.cardColor,
              onRefresh: onRefresh,
              child: AnimationLimiter(
                child: SingleChildScrollView(
                  child: AnimationLimiter(
                    child: Column(
                      spacing: 10.gw,
                      children: AnimationConfiguration.toStaggeredList(
                        children: children,
                        duration: const Duration(milliseconds: 300),
                        childAnimationBuilder: (widget) => SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: widget,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    // xyzq, dyzb 需要顶部弧形背景
    return switch (AppConfig.instance.flavor) {
      Flavor.xyzq || Flavor.dyzb => Stack(
          children: [
            ClipPath(
              clipper: _BottomArcClipper(),
              child: Container(
                height: 100,
                decoration: BoxDecoration(
                  color: context.theme.appBarTheme.backgroundColor,
                ),
              ),
            ),
            commonScaffold,
          ],
        ),
      _ => commonScaffold,
    };
  }

  /// 构建子组件列表
  List<Widget> _buildChildren(BuildContext context, HomeV2Cubit cubit) {
    return [
      SizedBox(height: 5.gw),
      ..._sortList.map((e) => _buildWidget(context, e, cubit)),
    ];
  }

  /// 根据类型构建对应的 widget
  Widget _buildWidget(BuildContext context, HomeSortType type, HomeV2Cubit cubit) {
    return switch (type) {
      HomeSortType.banner => HomeBannerSection(
          bannerList: state.bannerList,
          status: state.bannerStatus,
          currentIndex: state.bannerIndex,
          onIndexChanged: cubit.updateBannerIndex,
          style: _getBannerStyle(),
        ),
      HomeSortType.marquee => HomeMarqueeSection(
          notificationList: state.notifications,
          status: state.notificationStatus,
          style: _getMarqueeStyle(),
        ),
      HomeSortType.menu => const HomeMenuSection(),
      HomeSortType.marketTabs => HomeMarketSection(
          tabIndex: state.marketTabIndex,
          tabTypes: state.marketTabTypes,
          onTabChanged: cubit.updateMarketTabIndex,
          style: _getMarketStyle(context),
        ),
      HomeSortType.newsAndEvents => HomeNewsSection(
          newsData: state.newsData,
          status: state.newsStatus,
          style: _getNewsStyle(),
        ),
    };
  }

  /// Banner 样式
  HomeSectionStyle _getBannerStyle() {
    return HomeSectionStyle(
      margin: switch (AppConfig.instance.skinStyle) {
        _ when AppConfig.instance.flavor == Flavor.yhxt => EdgeInsets.symmetric(horizontal: 15.gw),
        AppSkinStyle.kGP => EdgeInsets.symmetric(horizontal: 16.gw),
        AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateC || AppSkinStyle.kTemplateD => EdgeInsets.symmetric(horizontal: 10.gw),
        _ => EdgeInsets.symmetric(horizontal: 16.gw),
      },
    );
  }

  /// Marquee 样式
  HomeSectionStyle _getMarqueeStyle() {
    return HomeSectionStyle(
      margin: switch (AppConfig.instance.skinStyle) {
        _ when AppConfig.instance.flavor == Flavor.yhxt => EdgeInsets.symmetric(horizontal: 15.gw),
        AppSkinStyle.kGP => EdgeInsets.symmetric(horizontal: 16.gw),
        _ => EdgeInsets.zero,
      },
      borderRadius: switch (AppConfig.instance.skinStyle) {
        _ when AppConfig.instance.flavor == Flavor.yhxt => BorderRadius.circular(10.gw),
        AppSkinStyle.kGP => BorderRadius.circular(10.gw),
        _ => null,
      },
    );
  }

  /// Market 样式
  HomeSectionStyle _getMarketStyle(BuildContext context) {
    return switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateA => HomeSectionStyle(
          margin: EdgeInsets.symmetric(horizontal: 12.gw),
          padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gw),
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(4.gw),
            boxShadow: const [
              BoxShadow(
                color: Color(0x0F354677),
                offset: Offset(0, 4),
                blurRadius: 10,
                spreadRadius: 0,
              ),
            ],
          ),
        ),
      AppSkinStyle.kTemplateC => HomeSectionStyle(
          margin: EdgeInsets.symmetric(horizontal: 12.gw),
          padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gw),
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(8.gw),
          ),
        ),
      AppSkinStyle.kTemplateD => HomeSectionStyle(
          padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gw),
          decoration: BoxDecoration(color: context.theme.cardColor),
        ),
      AppSkinStyle.kGP => const HomeSectionStyle(
          decoration: BoxDecoration(color: Colors.transparent),
        ),
      _ => HomeSectionStyle.none,
    };
  }

  /// News 样式
  HomeSectionStyle _getNewsStyle() {
    return HomeSectionStyle(
      margin: switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateC => EdgeInsets.symmetric(horizontal: 12.gw),
        _ => null,
      },
      borderRadius: switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kTemplateD => BorderRadius.circular(0),
        _ => null,
      },
    );
  }

  /// 组件排序列表（根据 skinStyle）
  List<HomeSortType> get _sortList {
    return switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kGP || AppSkinStyle.kTemplateD => [
          HomeSortType.menu,
          HomeSortType.banner,
          HomeSortType.marquee,
          HomeSortType.marketTabs,
          HomeSortType.newsAndEvents,
        ],
      _ => [
          HomeSortType.banner,
          HomeSortType.menu,
          HomeSortType.marquee,
          HomeSortType.marketTabs,
          HomeSortType.newsAndEvents,
        ],
    };
  }
}

/// 首页组件排序类型
enum HomeSortType {
  banner,
  marquee,
  menu,
  marketTabs,
  newsAndEvents,
}

/// 底部弧形裁剪器（用于 xyzq, dyzb）
class _BottomArcClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();

    path.moveTo(0, 0);
    path.lineTo(size.width, 0);
    path.lineTo(size.width, 50);

    path.quadraticBezierTo(
      size.width / 2,
      100,
      0,
      50,
    );

    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
