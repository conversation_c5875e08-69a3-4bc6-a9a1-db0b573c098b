# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Flutter Version Management
This project uses FVM (Flutter Version Manager) with Flutter 3.29.3:
```bash
fvm use 3.29.3
fvm flutter pub get
fvm flutter run
```

### Common Development Commands
```bash
# Install dependencies
fvm flutter pub get

# Run the app (default flavor)
fvm flutter run

# Run with specific flavor
fvm flutter run --flavor gp -t lib/main_gp.dart

# Build for production
fvm flutter build apk --flavor gp -t lib/main_gp.dart --release
fvm flutter build ios --flavor gp -t lib/main_gp.dart --release

# Code generation
fvm flutter packages pub run build_runner build --delete-conflicting-outputs

# Analyze code
fvm flutter analyze

# Run tests
fvm flutter test
```

### Build Scripts
- `./scripts/apk.sh` - Automated APK building with flavor support
- `./scripts/ipa.sh` - Automated IPA building with flavor support
- `./scripts/switch_flavor.sh` - Switch between app flavors and themes

### Flavor Management
Switch flavor assets and configuration:
```bash
./scripts/switch_flavor.sh --flavor gp
./scripts/switch_flavor.sh --skin gp --color-scheme default
```

## Project Architecture

### Multi-Flavor Architecture
This is a white-label stock trading app supporting 8 different brands/flavors:
- `gp` - GP Stock (main brand)
- `pre` - GP Pre (preview/staging)
- `rsyp` - 荣顺优配
- `yhxt` - 沅和信投
- `tempa` - tempa
- `bszb` - 宝石资本
- `dyzb` - 德盈资本
- `xyzq` - 鑫元優策略

Each flavor has its own:
- Entry point: `lib/main_[flavor].dart`
- App configuration in `flavorizr.yaml`
- Asset directory: `assets/flavors/[flavor]/`
- Theme/skin mapping via `switch_flavor.sh`

### Feature-Based Architecture
```
lib/
├── core/                    # Core utilities, DI, API, theme
├── features/                # Feature modules (clean architecture)
│   ├── account_v2/         # Account management
│   ├── auth/               # Authentication
│   ├── contract/           # Trading contracts
│   ├── futures_trade/      # Futures trading
│   ├── home/               # Home screen
│   └── ...
├── shared/                 # Shared components across features
│   ├── logic/              # Shared state management (Cubits)
│   ├── models/             # Data models
│   ├── widgets/            # Reusable UI components
│   ├── services/           # WebSocket, polling services
│   └── routes/             # Navigation
└── generated/              # Auto-generated files (JSON, etc.)
```

### State Management
- **BLoC/Cubit pattern** using `flutter_bloc` and `hydrated_bloc`
- **Dependency Injection** with `get_it` and `injectable`
- **Persistent state** via `hydrated_bloc` for theme, settings
- **Repository pattern** for data access

### Theme System
- Custom theming with semantic color names (not hardcoded colors)
- Support for light/dark modes via `ThemeCubit`
- Asset/skin switching system for multi-brand support
- Theme-aware components using context extensions

### Key Technologies
- **Flutter 3.29.3** (managed via FVM)
- **WebSocket** for real-time market data
- **Charts**: Multiple chart libraries (fl_chart, syncfusion_flutter_charts, k_chart_plus)
- **Internationalization**: easy_localization with JSON translation files
- **Network**: Dio with retry logic
- **UI**: Staggered animations, custom components
- **Push notifications**: EngageLab integration

## Development Guidelines

### Network Requests
- Use `Http().request<T>()` for all API calls (do NOT use deprecated `NetworkProvider`)
- `Http().request` handles errors internally, so **do NOT wrap calls in try-catch**
- API methods should be defined as static methods in `lib/core/models/apis/` directory
- Return type is usually `T?` (nullable), check for null instead of catching exceptions

Example:
```dart
// Good - no try-catch needed
final result = await ContractApi.fetchData(id: 123);
if (result != null) {
  emit(state.copyWith(data: result));
} else {
  emit(state.copyWith(status: DataStatus.failed));
}

// Bad - unnecessary try-catch
try {
  final result = await ContractApi.fetchData(id: 123);
  // ...
} catch (e) {
  // This is not needed
}
```

### Code Generation
When modifying data models with `@freezed` or `@JsonSerializable` annotations:
```bash
fvm flutter packages pub run build_runner build --delete-conflicting-outputs
```

### Flavor Development
1. Use the appropriate main file: `lib/main_[flavor].dart`
2. Switch assets before development: `./scripts/switch_flavor.sh --flavor [flavor]`
3. Test builds use specific commands in build scripts

### Theme Usage
- Always use semantic color names from the theme system
- Access theme colors via context extensions
- Avoid hardcoded colors in favor of theme-aware alternatives

### WebSocket Integration
- Real-time market data flows through `WebSocketService`
- Use `WebSocketMixin` for components needing live data
- Polling services available as fallback via `PollingService`

### Testing
- Unit tests should be placed in appropriate test directories
- Integration tests for critical trading flows
- Always test across different flavors when making shared changes

### Asset Management
- Flavor-specific assets go in `assets/flavors/[flavor]/`
- Shared assets in `assets/` root directories
- Use the skin/theme switching system for dynamic asset swapping

## Common Pitfalls

1. **Flavor switching**: Always run `switch_flavor.sh` before building different flavors
2. **Code generation**: Remember to run build_runner after modifying models
3. **Theme consistency**: Use theme system instead of hardcoded colors
4. **WebSocket cleanup**: Properly dispose WebSocket connections in components
5. **FVM usage**: Always prefix Flutter commands with `fvm` in this project