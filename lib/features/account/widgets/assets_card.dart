import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/logic/selected_exchange_cubit/selected_exchange_cubit.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';

/// 资产卡片样式配置 / Assets card style configuration
class AssetsCardStyle {
  /// 卡片高度 / Card height
  final double? height;

  /// 背景图片路径 / Background image path
  final String? bgImgPath;

  /// 文字颜色 / Text color
  final Color? textColor;

  /// 分隔线颜色 / Divider color
  final Color? dividerColor;

  const AssetsCardStyle({
    this.height,
    this.bgImgPath,
    this.textColor,
    this.dividerColor,
  });

  /// 根据当前皮肤自动获取样式 / Auto get style based on current skin
  factory AssetsCardStyle.fromSkin() {
    return switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kZangGolden => AssetsCardStyle.kZangGolden(),
      _ => const AssetsCardStyle(),
    };
  }

  /// 藏金皮肤 / ZangGolden skin style
  factory AssetsCardStyle.kZangGolden() => AssetsCardStyle(
        height: 154.gw,
        bgImgPath: "assets/images/bg_asset_card.png",
        textColor: const Color(0xffFCD696),
        dividerColor: const Color(0xffFCD696),
      );
}

/// 资产卡片 / Assets card widget
class AssetsCard extends StatelessWidget {
  /// 样式配置 / Style configuration
  final AssetsCardStyle? style;

  /// 总资产 / Total assets
  final double totalAssets;

  /// 今日收益/浮动盈亏 / Today's earnings / Floating PnL
  final double todayEarnings;

  /// 可用余额 / Available balance
  final double availableBalance;

  /// 利息 / Interest
  final double myInterest;

  /// 冻结金额 / Frozen amount
  final double frozenAmount;

  /// 是否为合约账户 / Is contract account
  final bool isContract;

  /// 货币单位 / Currency unit
  final String currency;

  /// 资产标题 / Asset title
  final String? assetTitle;

  /// 收益标题 / Profit title
  final String? profitTitle;

  const AssetsCard({
    super.key,
    this.style,
    double? totalAssets,
    double? todayEarnings,
    double? availableBalance,
    double? myInterest,
    double? frozenAmount,
    bool? isContract,
    String? currency,
    this.assetTitle,
    this.profitTitle,
  })  : totalAssets = totalAssets ?? 0,
        todayEarnings = todayEarnings ?? 0,
        availableBalance = availableBalance ?? 0,
        myInterest = myInterest ?? 0,
        frozenAmount = frozenAmount ?? 0,
        isContract = isContract ?? false,
        currency = currency ?? 'CNY';

  @override
  Widget build(BuildContext context) {
    final selectedExchangeCubit = !isContract ? context.read<SelectedExchangeCubit>() : null;
    final effectiveStyle = style ?? AssetsCardStyle.fromSkin();
    final textColor = effectiveStyle.textColor;
    final dividerColor = effectiveStyle.dividerColor;

    return Container(
      height: effectiveStyle.height ?? 160.gw,
      decoration: effectiveStyle.bgImgPath != null
          ? BoxDecoration(
              image: DecorationImage(image: AssetImage(effectiveStyle.bgImgPath!)),
            )
          : BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  context.theme.primaryColorLight,
                  context.theme.primaryColor,
                ],
              ),
              borderRadius: BorderRadius.circular(12.gr),
            ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.fromLTRB(16.gw, 22.gw, 16.gw, 0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        assetTitle ?? 'totalAssets'.tr(),
                        style: TextStyle(
                          color: textColor ?? Colors.white,
                          fontSize: 16.gsp,
                        ),
                      ),
                      Text(
                        profitTitle ?? 'unrealizedPnl'.tr(),
                        style: TextStyle(
                          color: textColor ?? Colors.white70,
                          fontSize: 14.gsp,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.gw),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      FlipText(
                        totalAssets,
                        showCurrencyDropdown: !isContract,
                        isCurrency: true,
                        suffix: isContract ? ' $currency' : null,
                        selectedExchangeCubit: isContract ? null : selectedExchangeCubit,
                        style: context.textTheme.secondary.copyWith(color: textColor),
                        dropdownIconColor: textColor ?? context.colorTheme.buttonPrimary,
                      ),
                      FlipText(
                        todayEarnings,
                        isCurrency: true,
                        selectedExchangeCubit: isContract ? null : selectedExchangeCubit,
                        suffix: '',
                        style: context.textTheme.primary.fs18.w700.copyWith(color: textColor ?? Colors.white),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Container(
            height: 64.gw,
            decoration: effectiveStyle.bgImgPath != null
                ? null
                : BoxDecoration(
                    color: Colors.white.withNewOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.gr),
                  ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _BuildBalanceItem(
                  label: 'availableBalance'.tr(),
                  amount: availableBalance,
                  selectedExchangeCubit: isContract ? null : selectedExchangeCubit,
                  textColor: textColor,
                ),
                _Divider(color: textColor ?? dividerColor),
                _BuildBalanceItem(
                  label: 'interest'.tr(),
                  amount: myInterest,
                  textColor: textColor,
                ),
                _Divider(color: textColor ?? dividerColor),
                _BuildBalanceItem(
                  label: 'frozenAmount'.tr(),
                  amount: frozenAmount,
                  selectedExchangeCubit: isContract ? null : selectedExchangeCubit,
                  textColor: textColor,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class _BuildBalanceItem extends StatelessWidget {
  const _BuildBalanceItem({
    required this.label,
    required this.amount,
    this.selectedExchangeCubit,
    this.textColor,
  });

  final String label;
  final num amount;
  final SelectedExchangeCubit? selectedExchangeCubit;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          label,
          style: TextStyle(
            color: textColor ?? Colors.white70,
            fontSize: 12.gsp,
          ),
        ),
        SizedBox(height: 5.gw),
        FlipText(
          amount.toDouble(),
          fractionDigits: 2,
          isCurrency: true,
          suffix: '',
          style: context.textTheme.regular.fs16.ffAkz.copyWith(
            color: textColor ?? Colors.white,
          ),
          selectedExchangeCubit: selectedExchangeCubit,
        )
      ],
    );
  }
}

class _Divider extends StatelessWidget {
  const _Divider({this.color});

  final Color? color;

  @override
  Widget build(BuildContext context) {
    final baseColor = color ?? Colors.white;
    return Container(
      width: 1,
      height: 30.gw,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            baseColor.withNewOpacity(0.24),
            baseColor,
            baseColor.withNewOpacity(0.24),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
    );
  }
}
