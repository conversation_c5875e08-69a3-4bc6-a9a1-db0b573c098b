import 'dart:convert';

import 'package:gp_stock_app/generated/json/alipay_account_entity.g.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';

export 'package:gp_stock_app/generated/json/alipay_account_entity.g.dart';

@JsonSerializable()
class AlipayAccountEntityList {
  List<AlipayAccountEntity> list = [];

  AlipayAccountEntityList();

  factory AlipayAccountEntityList.fromJson(Map<String, dynamic> json) => $AlipayAccountEntityListFromJson(json);

  Map<String, dynamic> toJson() => $AlipayAccountEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AlipayAccountEntity {
  String bankAccount = "";
  String bankAddress = "";
  String bankCode = "";
  String bankRegion = "";
  String channelName = "";
  int constantGiveRate = 0;
  List<AlipayAccountEntityCustomizeGiveRate> customizeGiveRate = [];
  int giveGiftType = 0;
  int giveRuleType = 0;
  int id = 0;
  bool isOnlyOffline = false;
  double maxAmount = 0;
  double minAmount = 0;
  String ownerName = "";
  int status = 0;

  AlipayAccountEntity();

  factory AlipayAccountEntity.fromJson(Map<String, dynamic> json) => $AlipayAccountEntityFromJson(json);

  Map<String, dynamic> toJson() => $AlipayAccountEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AlipayAccountEntityCustomizeGiveRate {
  int amount = 0;
  int rate = 0;

  AlipayAccountEntityCustomizeGiveRate();

  factory AlipayAccountEntityCustomizeGiveRate.fromJson(Map<String, dynamic> json) =>
      $AlipayAccountEntityCustomizeGiveRateFromJson(json);

  Map<String, dynamic> toJson() => $AlipayAccountEntityCustomizeGiveRateToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
