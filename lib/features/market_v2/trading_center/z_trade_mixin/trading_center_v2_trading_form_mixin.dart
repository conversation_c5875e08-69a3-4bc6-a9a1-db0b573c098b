import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/trade/trade_handling_fee_config.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/utils/trade_calculator.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import '../trading_center_v2_state.dart';
import '../z_trade_mixin/trading_center_v2_core_mixin.dart';

/// 交易表单 Mixin / Trade Form Mixin
/// 负责价格、数量、仓位、汇总的统一管理
/// Manages price, quantity, fraction, and trade summaries
///
/// **模块 / Modules**：
/// - 价格同步 / Price Sync
/// - 数量处理 / Quantity Handling
/// - 协调层 / Coordination (syncTradeForm)
/// - 交易汇总 / Trade Summary
mixin TradingCenterV2TradingFormMixin on Cubit<TradingCenterV2State>, TradingCenterV2CoreMixin {

  // ========================================
  // #region 价格同步 / Price Sync
  // ========================================

  /// 改变订单类型（市价单/限价单）
  void onChangeOrderType(OrderPriceType orderType) {
    // 当切换到限价单时，使用当前价格作为初始输入价格
    if (orderType == OrderPriceType.limit) {
      final currentPrice = state.stockInfo?.latestPrice ?? 0.0;
      if (currentPrice > 0) {
        emit(state.copyWith(
          tradeForm: state.tradeForm.copyWith(
            orderType: orderType,
            inputPrice: currentPrice,
          ),
        ));
        return;
      }
    }

    emit(state.copyWith(
      tradeForm: state.tradeForm.copyWith(
        orderType: orderType,
      ),
    ));
  }

  /// 设置输入价格
  ///
  /// 包含价格验证和四舍五入，并根据仓位选择同步数量
  ///
  /// [needClamp] 是否需要 clamp 到涨跌停范围，默认 true
  /// 实时输入时传 false，失焦验证时传 true
  double onInputPriceChanged(double price, {bool needClamp = true}) {
    // 使用辅助方法统一处理价格更新和数量同步
    _updateLimitPriceAndSyncQuantity(price, needClamp: needClamp);

    // 返回最终的四舍五入后的价格
    final effectivePrice = needClamp ? validatePriceRange(price) : price;
    return TradeCalculator.roundToTick(
      effectivePrice,
      state.tradeForm.pricePrecision,
    );
  }

  /// 增加价格（按 tick size）
  void incrementPrice() => _adjustPriceByTick(increase: true);

  /// 减少价格（按 tick size）
  void decrementPrice() => _adjustPriceByTick(increase: false);

  /// 通用的按 tick 调整价格逻辑
  void _adjustPriceByTick({required bool increase}) {
    if (!requireTradeAccount()) return;

    final tickSize = state.tradeForm.priceTickSize;
    if (tickSize <= 0) return;

    final delta = increase ? tickSize : -tickSize;
    final newPrice = state.tradeForm.inputPrice + delta;

    // 使用辅助方法统一处理价格更新和数量同步
    _updateLimitPriceAndSyncQuantity(newPrice);
  }

  /// 更新限价单价格并同步数量
  ///
  /// 处理三种情况：
  /// 1. 全仓模式：重新计算数量（保持全仓选中）
  /// 2. 其他仓位（1/2, 1/3, 1/4）：取消仓位选择，保持数量不变
  /// 3. 无仓位选择：检查是否超额，超额则自动切换到全仓
  void _updateLimitPriceAndSyncQuantity(double newPrice, {bool needClamp = true}) {
    final effectivePrice = needClamp ? validatePriceRange(newPrice) : newPrice;
    final roundedPrice = TradeCalculator.roundToTick(effectivePrice, state.tradeForm.pricePrecision);

    // 先更新价格
    emit(state.copyWith(
      tradeForm: state.tradeForm.copyWith(inputPrice: roundedPrice),
    ));

    // 根据当前仓位状态决定同步方式
    final currentFraction = state.tradeForm.selectedFraction;
    if (currentFraction == OrderFraction.full) {
      // 全仓模式：保持全仓重算
      syncTradeForm();
    } else if (currentFraction != null) {
      // 其他仓位（1/2, 1/3, 1/4）：取消仓位，保持数量
      syncTradeForm(cancelFraction: true);
    } else {
      // 无仓位：检查超额（由 syncTradeForm 内部处理）
      syncTradeForm();
    }
  }

  // #endregion


  // ========================================
  // #region 数量处理 (QuantityMixin)
  // ========================================

  /// 设置交易数量
  ///
  /// 包含数量验证和对齐，超额时自动切换到全仓
  double onQuantityInputChanged(double quantity) {
    // 统一入口处理
    syncTradeForm(inputQuantity: quantity);
    return state.tradeForm.quantity;
  }

  /// 增加数量（按步进值）
  void incrementQuantity() => _adjustQuantityByStep(increase: true);

  /// 减少数量（按步进值）
  void decrementQuantity() => _adjustQuantityByStep(increase: false);

  /// 通用的按步进调整数量逻辑
  void _adjustQuantityByStep({required bool increase}) {
    if (!requireTradeAccount()) return;

    final step = state.tradeForm.quantityStep;
    if (step <= 0) return;

    final currentQty = state.tradeForm.quantity;
    final delta = increase ? step : -step;
    final newQty = currentQty + delta;

    if (!increase && newQty < step) return;

    onQuantityInputChanged(newQty);
  }

  /// 仓位比例改变回调
  ///
  /// 当用户点击仓位比例按钮时调用
  void onFractionChanged(OrderFraction fraction) {
    if (!requireTradeAccount()) return;
    if (state.tradeForm.selectedFraction == fraction) return;

    // 统一入口处理
    syncTradeForm(selectFraction: fraction);
  }

  // #endregion


  // ========================================
  // #region 协调层 (CoordinationMixin)
  // ========================================

  /// 统一的交易表单同步入口 / Unified trade form sync entry point
  ///
  /// 根据不同场景计算并更新数量和仓位
  /// Calculates and updates quantity and fraction based on different scenarios
  ///
  /// **调用场景 / Usage Scenarios**:
  /// 1. `syncTradeForm(selectFraction: f)` - 用户点击仓位按钮 / User clicks fraction button
  /// 2. `syncTradeForm(inputQuantity: q)` - 用户输入数量 / User inputs quantity
  /// 3. `syncTradeForm(forceFullPosition: true)` - 切换标的/账户 / Switch security/account
  /// 4. `syncTradeForm(cancelFraction: true)` - 限价单调价 / Limit order price change
  /// 5. `syncTradeForm()` - 余额/价格变化 / Balance/price change
  void syncTradeForm({
    OrderFraction? selectFraction,
    double? inputQuantity,
    bool forceFullPosition = false,
    bool cancelFraction = false,
  }) {
    final price = getEffectivePrice();
    final maxQty = price > 0 ? getMaxQuantity(price: price) : 0.0;
    final lotSize = state.tradeForm.quantityStep;

    // 场景1：用户点击仓位按钮 / User clicks fraction button
    if (selectFraction != null) {
      _applyFraction(selectFraction, maxQty, lotSize);
      return;
    }

    // 场景2：用户手动输入数量 / User manually inputs quantity
    if (inputQuantity != null) {
      _applyInputQuantity(inputQuantity, maxQty, lotSize);
      return;
    }

    // 场景3：取消仓位选择（限价单调价时） / Cancel fraction (on limit order price change)
    if (cancelFraction) {
      _applyCancelFraction(maxQty);
      return;
    }

    // 场景4：强制全仓（切换标的/账户） / Force full position (switch security/account)
    if (forceFullPosition) {
      _applyFraction(OrderFraction.full, maxQty, lotSize, allowNullWhenEmpty: true);
      return;
    }

    // 场景5：已选中仓位，按仓位重算 / Has fraction selected, recalculate by fraction
    final currentFraction = state.tradeForm.selectedFraction;
    if (currentFraction != null) {
      _applyFraction(currentFraction, maxQty, lotSize);
      return;
    }

    // 场景6：未选中仓位，检查超额 / No fraction selected, check excess
    if (maxQty > 0 && state.tradeForm.quantity > maxQty) {
      _applyFraction(OrderFraction.full, maxQty, lotSize);
      return;
    }

    // 无变化，只刷新汇总 / No change, just refresh summaries
    updateTradeSummaries();
  }

  // ==================== 私有方法 / Private Methods ====================

  /// 按仓位比例设置数量 / Set quantity by fraction ratio
  void _applyFraction(
    OrderFraction fraction,
    double maxQty,
    double lotSize, {
    bool allowNullWhenEmpty = false,
  }) {
    final qty = TradeCalculator.calculateQuantityByRatio(
      maxQuantity: maxQty,
      ratio: fraction.fraction,
      lotSize: lotSize,
    );
    // allowNullWhenEmpty: 余额为0时仓位设为null / Set fraction to null when balance is 0
    final newFraction = (allowNullWhenEmpty && maxQty <= 0) ? null : fraction;

    _emitIfChanged(qty, newFraction);
  }

  /// 按用户输入设置数量（含超额检查和对齐） / Set quantity by user input (with excess check and alignment)
  void _applyInputQuantity(double inputQty, double maxQty, double lotSize) {
    // 超额时切换到全仓 / Switch to full position when exceeded
    if (maxQty > 0 && inputQty > maxQty) {
      _applyFraction(OrderFraction.full, maxQty, lotSize);
      return;
    }

    // 对齐到步进值 / Align to lot size
    final effectiveMax = maxQty > 0 ? maxQty : double.infinity;
    var alignedQty = TradeCalculator.alignToLotSize(
      quantity: inputQty.clamp(0, effectiveMax),
      lotSize: lotSize,
    );

    // 确保至少一个 lotSize / Ensure at least one lot size
    if (alignedQty <= 0 && inputQty > 0 && maxQty >= lotSize) {
      alignedQty = lotSize;
    }

    // 手动输入不设置仓位，避免价格刷新时被覆盖
    // Manual input clears fraction to prevent being overwritten on price refresh
    _emitIfChanged(alignedQty, null);
  }

  /// 取消仓位选择，保持数量（clamp 到 maxQty） / Cancel fraction, keep quantity (clamped to maxQty)
  void _applyCancelFraction(double maxQty) {
    final qty = maxQty > 0
        ? state.tradeForm.quantity.clamp(0.0, maxQty)
        : state.tradeForm.quantity;

    _emitIfChanged(qty, null);
  }

  /// 幂等更新：有变化才 emit / Idempotent update: only emit when changed
  void _emitIfChanged(double quantity, OrderFraction? fraction) {
    if (quantity == state.tradeForm.quantity &&
        fraction == state.tradeForm.selectedFraction) {
      updateTradeSummaries();
      return;
    }

    updateTradeForm((form) => form.copyWith(
      quantity: quantity,
      selectedFraction: () => fraction,
    ));
  }

  // #endregion


  // ========================================
  // #region 交易汇总 / Trade Summary
  // ========================================

  /// 计算交易汇总（统一入口）
  ///
  /// 计算开多和开空汇总（根据是否允许做空）
  @override
  void updateTradeSummaries() {
    final price = getEffectivePrice();
    final maxOpenQty = price > 0 ? getMaxQuantity(price: price) : 0.0;

    // 判断是否允许做空
    final allowShort = _getAllowShort();

    // 计算开仓 summaries
    final summaries = _calculateOpenSummaries(
      price: price,
      quantity: state.tradeForm.quantity,
      allowShort: allowShort,
      maxOpenQty: maxOpenQty,
    );

    emit(state.copyWith(
      tradeForm: state.tradeForm.copyWith(
        openLongSummary: () => summaries.openLong,
        openShortSummary: () => summaries.openShort,
      ),
    ));
  }

  /// 更新股票最新价格并刷新交易汇总（用于股指交易从 IndexTradeCubit 同步价格）
  void updateStockPrice(double latestPrice) {
    // 只在价格真正变化时更新
    if (state.stockInfo?.latestPrice == latestPrice) return;

    final updatedStockInfo = state.stockInfo?.copyWith(latestPrice: latestPrice);
    if (updatedStockInfo == null) return;

    emit(state.copyWith(stockInfo: () => updatedStockInfo));

    // 市价单时同步数量和汇总
    if (state.tradeForm.orderPriceType == OrderPriceType.market) {
      syncTradeForm();
    }
  }

  /// 判断是否允许做空
  ///
  /// 返回是否允许做空的判断结果
  bool _getAllowShort() {
    // A股：不允许做空
    if (state.security.marketCategory == MarketCategory.cnStocks) return false;

    // 股指：从 IndexTradeCubit 单例获取配置
    if (state.security.isIndexTrading) {
      try {
        final indexTradeCubit = getIt<IndexTradeCubit>();
        // 根据当前 instrument 找到对应的股指配置
        final indexStock = indexTradeCubit.state.indexStockConfigList.firstWhereOrNull(
          (index) => index.instrument == state.security.instrument,
        );
        // 返回配置中的 allowShortSell，未找到则默认允许
        return indexStock?.isToShort ?? true;
      } catch (e) {
        debugPrint('Error getting allowShortSell from IndexTradeCubit: $e');
        return true; // 兜底：默认允许，后端会判断
      }
    }

    // 合约：允许做空
    return true;
  }

  /// 计算开仓汇总
  ///
  /// 根据是否允许做空，计算开多和开空汇总
  ({
    TradeSummary openLong,
    TradeSummary? openShort,
  }) _calculateOpenSummaries({
    required double price,
    required double quantity,
    required bool allowShort,
    required double maxOpenQty,
  }) {
    final isAStock = state.security.marketCategory == MarketCategory.cnStocks;

    // 开多（买入开仓）
    final openLong = _createSummary(
      price: price,
      quantity: quantity,
      feeConfigs: state.handlingFeeConfigList,
      actionLabel: isAStock ? 'buy'.tr() : 'openLong'.tr(),
      availableLabel: 'availableToOpen'.tr(),
      availableQuantity: maxOpenQty,
      enabled: maxOpenQty > 0 && quantity > 0,
      tradeType: 1, // 多单
    );

    // 开空（卖出开仓，如果允许做空）
    final openShort = allowShort
        ? _createSummary(
            price: price,
            quantity: quantity,
            feeConfigs: state.handlingFeeConfigList,
            actionLabel: 'openShort'.tr(),
            availableLabel: 'availableToOpen'.tr(),
            availableQuantity: maxOpenQty,
            enabled: maxOpenQty > 0 && quantity > 0,
            tradeType: 2, // 空单
          )
        : null;

    return (
      openLong: openLong,
      openShort: openShort,
    );
  }

  /// 创建交易汇总
  TradeSummary _createSummary({
    required double price,
    required double quantity,
    required List<TradeHandlingFeeConfig> feeConfigs,
    required String actionLabel,
    required String availableLabel,
    required int tradeType, // 1: 多单, 2: 空单
    double? availableQuantity,
    bool enabled = true,
  }) {
    final summary = TradeCalculator.calculateTradeSummary(
      price: price,
      quantity: quantity,
      feeConfigs: feeConfigs,
      quantityStep: state.tradeForm.quantityStep,
      isStockIndex: state.security.isIndexTrading,
      currency: instrumentCurrency,
      exchangeRate: instrumentExchangeRate,
      tradeUnit: getTradeUnit() ?? 1.0,
    );

    return summary.copyWith(
      actionLabel: actionLabel,
      availableLabel: availableLabel,
      quantitySuffix: 'lotForSecurities'.tr() /* 股 */,
      enabled: enabled,
      availableQty: availableQuantity ?? summary.availableQty,
      tradeType: tradeType,
    );
  }

  /// 需要宿主 Cubit 提供的抽象属性
  String get instrumentCurrency;
  double? get instrumentExchangeRate;

  // #endregion
}
