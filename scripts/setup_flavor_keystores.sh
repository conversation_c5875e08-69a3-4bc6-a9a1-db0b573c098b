#!/bin/bash

# ================================================================
# setup_flavor_keystores.sh
# Script to create flavor-specific JKS keystores for Android signing
# ================================================================

# Common keystore settings
STORE_PASSWORD="gp@2025"
KEY_PASSWORD="gp@2025"
KEY_ALIAS="upload"
VALIDITY_DAYS=10000

# Base directory for keystores
JKS_BASE_DIR="$HOME/jks"

# Flavor configurations (compatible with older bash versions)
FLAVORS="PRE:GP Pre,GP:GP Stock,RSYP:荣顺优配,YHXT:沅和信投,TEMPA:tempa,BSZB:宝石资本,DYZB:德盈资本,XYZQ:鑫元優策略"

# Function to get app name for flavor
get_app_name() {
    local flavor=$1
    echo "$FLAVORS" | tr ',' '\n' | grep "^$flavor:" | cut -d':' -f2
}

# Function to get all flavor names
get_all_flavors() {
    echo "$FLAVORS" | tr ',' '\n' | cut -d':' -f1
}

# Function to create keystore for a flavor
create_keystore() {
    local flavor=$1
    local app_name=$2
    local flavor_dir="$JKS_BASE_DIR/$flavor"
    local keystore_path="$flavor_dir/key.jks"
    
    echo "Creating keystore for $flavor ($app_name)..."
    
    # Create directory if it doesn't exist
    mkdir -p "$flavor_dir"
    
    # Check if keystore already exists
    if [ -f "$keystore_path" ]; then
        echo "⚠️  Keystore already exists: $keystore_path"
        read -p "Do you want to overwrite it? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Skipping $flavor..."
            return
        fi
        rm -f "$keystore_path"
    fi
    
    # Generate keystore
    keytool -genkey \
        -v \
        -keystore "$keystore_path" \
        -alias "$KEY_ALIAS" \
        -keyalg RSA \
        -keysize 2048 \
        -validity $VALIDITY_DAYS \
        -storepass "$STORE_PASSWORD" \
        -keypass "$KEY_PASSWORD" \
        -dname "CN=$app_name, OU=Mobile Development, O=GP Stock, L=City, ST=State, C=US"
    
    if [ $? -eq 0 ]; then
        echo "✅ Successfully created keystore: $keystore_path"
    else
        echo "❌ Failed to create keystore for $flavor"
    fi
    echo
}

# Function to list existing keystores
list_keystores() {
    echo "📋 Existing keystores:"
    echo "====================="

    for flavor in $(get_all_flavors); do
        local keystore_path="$JKS_BASE_DIR/$flavor/key.jks"
        if [ -f "$keystore_path" ]; then
            echo "✅ $flavor: $keystore_path"
        else
            echo "❌ $flavor: Missing"
        fi
    done
    echo
}

# Function to verify keystore
verify_keystore() {
    local flavor=$1
    local keystore_path="$JKS_BASE_DIR/$flavor/key.jks"
    
    if [ ! -f "$keystore_path" ]; then
        echo "❌ Keystore not found: $keystore_path"
        return 1
    fi
    
    echo "🔍 Verifying keystore for $flavor..."
    keytool -list -v -keystore "$keystore_path" -storepass "$STORE_PASSWORD"
}

# Main script logic
case "${1:-help}" in
    "create")
        if [ -n "$2" ]; then
            # Create specific flavor
            flavor=$(echo "$2" | tr '[:lower:]' '[:upper:]')
            app_name=$(get_app_name "$flavor")
            if [ -n "$app_name" ]; then
                create_keystore "$flavor" "$app_name"
            else
                echo "❌ Unknown flavor: $2"
                echo "Available flavors: $(get_all_flavors | tr '\n' ' ')"
                exit 1
            fi
        else
            # Create all keystores
            echo "🔑 Creating keystores for all flavors..."
            echo "========================================"
            for flavor in $(get_all_flavors); do
                app_name=$(get_app_name "$flavor")
                create_keystore "$flavor" "$app_name"
            done
        fi
        ;;
    "list")
        list_keystores
        ;;
    "verify")
        if [ -n "$2" ]; then
            flavor=$(echo "$2" | tr '[:lower:]' '[:upper:]')
            verify_keystore "$flavor"
        else
            echo "❌ Please specify a flavor to verify"
            echo "Usage: $0 verify <flavor>"
            exit 1
        fi
        ;;
    "help"|*)
        echo "🔑 Flavor Keystore Management Script"
        echo "===================================="
        echo
        echo "Usage: $0 <command> [options]"
        echo
        echo "Commands:"
        echo "  create [flavor]  - Create keystore(s). If no flavor specified, creates all"
        echo "  list            - List all existing keystores"
        echo "  verify <flavor> - Verify a specific keystore"
        echo "  help            - Show this help message"
        echo
        echo "Available flavors: $(get_all_flavors | tr '\n' ' ')"
        echo
        echo "Examples:"
        echo "  $0 create           # Create all keystores"
        echo "  $0 create gp        # Create only GP keystore"
        echo "  $0 list             # List existing keystores"
        echo "  $0 verify gp        # Verify GP keystore"
        ;;
esac
