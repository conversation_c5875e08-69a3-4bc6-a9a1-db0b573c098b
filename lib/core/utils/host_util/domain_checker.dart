import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/utils/extension/future_list_extensions.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../log.dart';

/// 域名检测结果
class DomainResult {
  final String domain;
  final bool isSuccess;

  DomainResult(this.domain, this.isSuccess);

  @override
  String toString() => "domain: $domain, isSuccess: $isSuccess";
}

/// 域名延迟测试结果
class DomainLatency {
  final String domain;
  final int? latencyMs; // 毫秒，null 表示失败
  final bool isSuccess;

  DomainLatency(this.domain, this.latencyMs, this.isSuccess);
}

/// 域名检测器：负责域名可用性检测、缓存、测速等
class DomainChecker {
  String? _lastSuccessfulHost;

  // IP 地址正则（静态常量，避免重复编译）
  static final _ipRegex = RegExp(
    r'^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)'
    r'(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$',
  );

  // 持久化存储 key
  static final String _lastHostKey =
      'last_successful_host_${AppConfig.instance.siteId}_${AppConfig.instance.flavor}';

  // 并发限制数和超时时间
  static const int _maxConcurrent = 3;
  static const Duration _checkTimeout = Duration(milliseconds: 1500);

  // HTTP 客户端，用于连接复用
  final _client = HttpClient()
    ..connectionTimeout = const Duration(milliseconds: 1500)
    ..maxConnectionsPerHost = 5;

  // ==================== 缓存相关 ====================

  Future<String?> loadLastHost() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cached = prefs.getString(_lastHostKey);
      _lastSuccessfulHost = cached;
      LogD("直接读取缓存的上次主机: $_lastSuccessfulHost");
      return cached;
    } catch (e) {
      LogD("加载上次成功的主机失败: $e");
      _lastSuccessfulHost = null;
      return null;
    }
  }

  Future<void> saveLastHost(String host) async {
    try {
      _lastSuccessfulHost = host;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastHostKey, host);
      LogD("保存上次成功的主机: $host");
    } catch (e) {
      LogD("保存上次成功的主机失败: $e");
    }
  }

  Future<String?> checkLastSuccessfulHost() async {
    if (_lastSuccessfulHost == null || _lastSuccessfulHost!.isEmpty) {
      final lastUrl = await loadLastHost();
      if (lastUrl == null || lastUrl.isEmpty) return null;
    }

    try {
      final result = await _checkDomain(_lastSuccessfulHost!)
          .timeout(const Duration(milliseconds: 1500));

      if (result.isSuccess) {
        LogD("上次成功的主机仍然可用");
        await saveLastHost(_lastSuccessfulHost!);
        return _lastSuccessfulHost;
      } else {
        LogD("上次成功的主机不再可用");
        return null;
      }
    } catch (e) {
      LogD("检查上次成功的主机时出错: $e");
      return null;
    }
  }

  // ==================== 域名检测 ====================

  Future<String?> findFastestDomain(List<String> domains, {Duration? timeout}) async {
    try {
      final result = await _tryNewDomains(domains, timeout: timeout);
      if (result != null) {
        await saveLastHost(result);
      }
      return result;
    } catch (e) {
      LogD("findFastestDomain 错误: $e");
      return null;
    }
  }

  Future<String?> _tryNewDomains(List<String> domains, {Duration? timeout}) async {
    final priorityDomains = domains.take(_maxConcurrent).toList();
    final backupDomains = domains.skip(_maxConcurrent).toList();

    // 先尝试优先域名组（较短超时）
    final priorityTimeout = timeout ?? const Duration(seconds: 3);
    final priorityResult = await _tryDomainsWithTimeout(priorityDomains, timeout: priorityTimeout);
    if (priorityResult != null) {
      return priorityResult;
    }

    // 如果优先组失败，尝试备用组（较长超时）
    if (backupDomains.isNotEmpty) {
      final backupTimeout = timeout != null ? timeout * 2 : const Duration(seconds: 10);
      return await _tryDomainsWithTimeout(backupDomains, timeout: backupTimeout);
    }
    return null;
  }

  Future<String?> _tryDomainsWithTimeout(
    List<String> domains, {
    Duration timeout = const Duration(seconds: 2),
  }) async {
    try {
      final tasks = domains.map((domain) async {
        try {
          final result = await _checkDomain(domain);
          if (result.isSuccess) {
            LogD("✅ 域名可用: $domain");
            return domain;
          } else {
            LogD("❌ 域名不可用: $domain");
            return null;
          }
        } catch (e) {
          LogE("检查域名失败: $domain");
          return null;
        }
      }).toList();

      return await tasks.firstNonNullSuccessful().timeout(timeout, onTimeout: () {
        LogD("所有域名检查超时");
        return null;
      });
    } catch (e) {
      LogE("尝试域名列表失败: $e");
      return null;
    }
  }

  Future<DomainResult> _checkDomain(String domain) async {
    final url = '$domain/api/ping';
    LogD("尝试域名: $url");
    try {
      final uri = Uri.parse(url);
      final request = await _client.openUrl('OPTIONS', uri);
      request.headers.add('user-agent', 'Mozilla/5.0');

      final response = await request.close().timeout(_checkTimeout);
      await response.drain();

      return DomainResult(domain, response.statusCode == 200);
    } catch (e) {
      LogD("检查域名错误: $e");
      return DomainResult(domain, false);
    }
  }

  // ==================== 测速 ====================

  /// 测试单个域名延迟
  Future<DomainLatency> measureDomain(String domain) async {
    final sw = Stopwatch()..start();
    try {
      final res = await _checkDomain(domain).timeout(_checkTimeout);
      sw.stop();
      return DomainLatency(
        domain,
        res.isSuccess ? sw.elapsedMilliseconds : null,
        res.isSuccess,
      );
    } catch (_) {
      sw.stop();
      return DomainLatency(domain, null, false);
    }
  }

  /// 测试域名延迟（顺序执行，避免并发干扰）
  Future<List<DomainLatency>> measureDomains(List<String> domains) async {
    final List<DomainLatency> results = [];
    for (final domain in domains) {
      final sw = Stopwatch()..start();
      try {
        final res = await _checkDomain(domain).timeout(_checkTimeout);
        sw.stop();
        results.add(DomainLatency(
          domain,
          res.isSuccess ? sw.elapsedMilliseconds : null,
          res.isSuccess,
        ));
      } catch (_) {
        sw.stop();
        results.add(DomainLatency(domain, null, false));
      }
    }
    return results;
  }

  // ==================== OSS 获取 ====================

  /// 从 OSS 获取并解析域名列表
  Future<List<String>?> fetchDomainsFromOss(List<String> ossUrls, {Duration? timeout}) async {
    final List<String> allDomains = [];

    try {
      final result = await ossUrls
          .map((url) => _fetchSingleOssUrl(url, timeout: timeout))
          .toList()
          .firstNonNullSuccessful();
      if (result != null) {
        allDomains.addAll(result);
      }
      return allDomains;
    } catch (e) {
      LogD("All OSS requests failed: $e");
      return null;
    }
  }

  Future<List<String>?> _fetchSingleOssUrl(String url, {Duration? timeout}) async {
    try {
      LogD("从 $url 获取域名");

      final response = await http.get(Uri.parse(url)).timeout(timeout ?? const Duration(seconds: 3));

      if (response.statusCode == 200) {
        final List<String> domains = List<String>.from(jsonDecode(response.body)).map((e) {
          e = e.replaceAll(' ', '');
          if (!e.startsWith('http')) {
            // 不以 http 开头，若是 IP 则加 http://，否则添加随机前缀
            return _ipRegex.hasMatch(e) ? "http://$e" : "https://${generateRandomString(8)}.$e";
          } else {
            return e;
          }
        }).toList();

        LogD("成功从 $url 获取域名: $domains");
        return domains;
      }

      LogD("从 $url 获取域名失败: ${response.statusCode}");
      return null;
    } catch (e) {
      LogD("从 $url 获取域名时出错: $e");
      return null;
    }
  }

  // ==================== 网络检测 ====================

  /// 等待网络连接（会一直等待直到有网）
  Future<void> waitUntilConnected({
    Duration interval = const Duration(seconds: 2),
  }) async {
    while (true) {
      final success = await _tryPing();
      if (success) return;
      LogD("网络不通，${interval.inSeconds}秒后重试...");
      await Future.delayed(interval);
    }
  }

  /// 检查网络是否畅通（使用百度，国内最快）
  Future<bool> _tryPing() async {
    try {
      final request = await _client.getUrl(Uri.parse('https://www.baidu.com'));
      final response = await request.close().timeout(const Duration(seconds: 3));
      LogD("baidu.com 网络检测响应: ${response.statusCode}");
      return response.statusCode == 200;
    } catch (e) {
      LogD("网络检测失败: $e");
      return false;
    }
  }

  void dispose() {
    _client.close();
  }


  /// 随机生成字符串
  String generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    final random = Random();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

}
