import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/wallet/funds_record_list.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/contract/domain/filter_constants.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';

class FundRecordCell extends StatelessWidget {
  final FundsRecord? record;
  final bool isContract;
  const FundRecordCell({super.key, required this.record, required this.isContract});

  @override
  Widget build(BuildContext context) {
    /// 标题，这是一个枚举值，根据枚举值显示对应文字
    String transactionType = _getTransactionTypeText(record?.fromType, isContract: isContract);

    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                transactionType,
                style: context.textTheme.regular.w600,
              ),
              Text(
                record?.createTime ?? '',
                style: context.textTheme.regular.fs9,
              ),
            ],
          ),
          12.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('beforeChangeAmount'.tr(), style: context.textTheme.regular),
              FlipText(
                record?.beforeNum ?? 0,
                style: context.textTheme.primary.w600.ffAkz,
                suffix: ' ${record?.currency ?? ''}',
              ),
            ],
          ),
          8.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('changeAmount'.tr(), style: context.textTheme.regular),
              FlipText(
                record?.updateNum ?? 0,
                style: context.textTheme.primary.w600.ffAkz
                    .copyWith(color: (record?.updateNum ?? 0).getValueColor(context)),
                suffix: ' ${record?.currency ?? ''}',
                prefix: (record?.updateNum ?? 0) < 0 ? '' : '+',
              ),
            ],
          ),
          8.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('afterChangeAmount'.tr(), style: context.textTheme.regular),
              FlipText(
                record?.afterNum ?? 0,
                style: context.textTheme.primary.w600.ffAkz,
                suffix: record?.currency ?? "",
              ),
            ],
          ),
          8.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('serialNumber'.tr(), style: context.textTheme.regular),
              Text(
                record?.serialNo ?? '',
                style: context.textTheme.primary.w600.ffAkz.copyWith(
                  color: context.colorTheme.stockRed,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getTransactionTypeText(int? fromType, {required bool isContract}) {
    if (isContract) {
      return _getTransactionTypeTextForContract(fromType);
    } else {
      return _getTransactionTypeTextForAccount(fromType);
    }
  }

  String _getTransactionTypeTextForAccount(int? fromType) =>
      FilterConstants.contractTypes.firstWhereOrNull((element) => element.type == fromType.toString())?.label.tr() ??
      'transaction'.tr();

  String _getTransactionTypeTextForContract(int? fromType) {
    return switch (fromType) {
      0 => 'all'.tr(),
      1 => 'contract_deposit'.tr(),
      2 => 'margin_increase'.tr(),
      3 => 'transfer_to_spot'.tr(),
      4 => 'contract_buy'.tr(),
      5 => 'contract_sell'.tr(),
      6 => 'interest_deduction'.tr(),
      7 => 'dividend_payment'.tr(),
      8 => 'admin_balance_adjustment'.tr(),
      9 => 'trading_fee'.tr(),
      10 => 'order_unfreeze'.tr(),
      12 => 'unfreezeOrderAmount'.tr(),
      13 => 'unfreezeOrderFee'.tr(),
      14 => 'freezeOrderAmount'.tr(),
      15 => 'freezeOrderFee'.tr(),
      _ => 'transaction'.tr()
    };
  }
}
