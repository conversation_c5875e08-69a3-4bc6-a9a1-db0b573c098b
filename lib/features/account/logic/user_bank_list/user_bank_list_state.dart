import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_bank_entity_list.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class UserBankListState extends Equatable {
  final DataStatus bankListFetchStatus;
  final DataStatus bankListUpdateStatus;
  final String? error;
  final List<UserBankEntity>? bankList;
  final UserBankEntity? selectedBank;
  final bool isEditMode;
  final ({DataStatus? status, int? id})? unbindBankStatus;

  const UserBankListState({
    this.bankListFetchStatus = DataStatus.idle,
    this.bankListUpdateStatus = DataStatus.idle,
    this.error,
    this.bankList,
    this.selectedBank,
    this.isEditMode = false,
    this.unbindBankStatus,
  });

  UserBankListState copyWith({
    DataStatus? bankListFetchStatus,
    DataStatus? bankListUpdateStatus,
    String? error,
    List<UserBankEntity>? bankList,
    UserBankEntity? Function()? selectedBank,
    bool? isEditMode,
    ({DataStatus? status, int? id})? unbindBankStatus,
  }) {
    return UserBankListState(
      bankListFetchStatus: bankListFetchStatus ?? this.bankListFetchStatus,
      bankListUpdateStatus: bankListUpdateStatus ?? this.bankListUpdateStatus,
      error: error ?? this.error,
      bankList: bankList ?? this.bankList,
      selectedBank: selectedBank != null ? selectedBank() : this.selectedBank,
      isEditMode: isEditMode ?? this.isEditMode,
      unbindBankStatus: unbindBankStatus ?? this.unbindBankStatus,
    );
  }

  @override
  List<Object?> get props => [
        bankListFetchStatus,
        bankListUpdateStatus,
        error,
        bankList,
        selectedBank,
        isEditMode,
        unbindBankStatus,
      ];
}
