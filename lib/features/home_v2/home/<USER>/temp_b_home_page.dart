import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/features/home_v2/home/<USER>/home_section_style.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

import '../home_v2_cubit.dart';
import '../home_v2_state.dart';
import '../widgets/market/tempb_market_section.dart';
import '../widgets/banner/home_banner_section.dart';
import '../widgets/marquee/home_marquee_section.dart';
import '../widgets/news/home_news_section.dart';
import '../widgets/rankings/home_ranking_section.dart';

/// yhxt 专用首页布局
/// 包含 yhxt 特有的:
/// - 背景图 + 渐变层布局
/// - 专用 AppBar（带 logo、title、subtitle）
/// - 专用排序顺序（marquee → banner → menu → marketTabs → news）
class TempBHomePage extends StatelessWidget {
  final HomeV2State state;

  const TempBHomePage({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HomeV2Cubit>();

    return Stack(
      children: [
        // 渐变覆盖层
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color.fromRGBO(255, 255, 255, 0.0),
                Color(0xFFF4F7FE),
              ],
              stops: [0.0002, 0.638],
            ),
          ),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          body: RefreshIndicator.adaptive(
            onRefresh: cubit.loadAll,
            child: AnimationLimiter(
              child: SingleChildScrollView(
                child: AnimationLimiter(
                  child: Column(
                    spacing: 10.gw,
                    children: AnimationConfiguration.toStaggeredList(
                      children: _buildChildren(cubit),
                      duration: const Duration(milliseconds: 300),
                      childAnimationBuilder: (widget) => SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: widget,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// yhxt 专用排序: marquee → banner → menu → marketTabs → news
  List<Widget> _buildChildren(HomeV2Cubit cubit) {
    return [
      // // 跑马灯
      HomeMarqueeSection(
        notificationList: state.notifications,
        status: state.notificationStatus,
      ),
      // Banner
      HomeBannerSection(
        bannerList: state.bannerList,
        status: state.bannerStatus,
        currentIndex: state.bannerIndex,
        onIndexChanged: cubit.updateBannerIndex,
        style: HomeSectionStyle(margin: EdgeInsets.symmetric(horizontal: 15.gw)),
      ),

      HomeRankingSection(
        margin: EdgeInsets.symmetric(horizontal: 14.gw),
        rankMap: state.rankMap,
        currentRankTabType: state.currentRankTabType,
        onTabChanged: cubit.switchRankTab,
        onMoreTapped: () {
          AuthUtils.verifyAuth(() {
            getIt<NavigatorService>().push(AppRouter.routeRank);
          });
        },
      ),

      // 行情 Tab
      TempBHomeMarketSection(
        tabIndex: state.marketTabIndex,
        tabTypes: state.marketTabTypes,
        onTabChanged: cubit.updateMarketTabIndex,
      ),
      // 新闻
      HomeNewsSection(
        newsData: state.newsData,
        status: state.newsStatus,
      ),
    ];
  }



}
