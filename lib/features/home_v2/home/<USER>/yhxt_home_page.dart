import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/features/home_v2/home/<USER>/home_section_style.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

import '../home_v2_cubit.dart';
import '../home_v2_state.dart';
import '../widgets/common/home_app_bar.dart';
import '../widgets/common/settings_menu.dart';
import '../widgets/menu/home_menu_section.dart';
import '../widgets/banner/home_banner_section.dart';
import '../widgets/marquee/home_marquee_section.dart';
import '../widgets/market/yhxt_market_section.dart';
import '../widgets/news/home_news_section.dart';

/// yhxt 专用首页布局
/// 包含 yhxt 特有的:
/// - 背景图 + 渐变层布局
/// - 专用 AppBar（带 logo、title、subtitle）
/// - 专用排序顺序（marquee → banner → menu → marketTabs → news）
class YhxtHomePage extends StatelessWidget {
  final HomeV2State state;

  const YhxtHomePage({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HomeV2Cubit>();

    return Stack(
      children: [
        // 背景图
        Image.asset(
          "assets/images/bg_home.png",
          fit: BoxFit.fitWidth,
          alignment: Alignment.topCenter,
        ),
        // 渐变覆盖层
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color.fromRGBO(255, 255, 255, 0.0),
                Color(0xFFF4F7FE),
              ],
              stops: [0.0002, 0.638],
            ),
          ),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          body: Column(
            spacing: 10.gw,
            children: [
              // yhxt 专用顶部导航栏
              HomeAppBar(
                title: "appName".tr(),
                subtitle: "上海沅和股权投资基金管理有限公司",
                logo: 'assets/images/logo/app_logo.png',
                menuIcon: 'assets/images/icon_setting.png',
                searchIcon: 'assets/images/icon_search.png',
                onMenuIconTap: () => _onMenuIconTap(context),
                onSearchIconTap: () => _onSearchIconTap(context),
              ),
              Expanded(
                child: RefreshIndicator.adaptive(
                  onRefresh: cubit.loadAll,
                  child: AnimationLimiter(
                    child: SingleChildScrollView(
                      child: AnimationLimiter(
                        child: Column(
                          spacing: 10.gw,
                          children: AnimationConfiguration.toStaggeredList(
                            children: _buildChildren(cubit),
                            duration: const Duration(milliseconds: 300),
                            childAnimationBuilder: (widget) => SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: widget,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// yhxt 专用排序: marquee → banner → menu → marketTabs → news
  List<Widget> _buildChildren(HomeV2Cubit cubit) {
    return [
      // 跑马灯
      HomeMarqueeSection(
        notificationList: state.notifications,
        status: state.notificationStatus,
        style: HomeSectionStyle(
          margin: EdgeInsets.symmetric(horizontal: 15.gw),
          borderRadius: BorderRadius.circular(12.gw),
        ),
      ),

      // Container(
      //   color: Colors.red,
      //   height: 20,
      // ),
      // Banner
      HomeBannerSection(
        bannerList: state.bannerList,
        status: state.bannerStatus,
        currentIndex: state.bannerIndex,
        onIndexChanged: cubit.updateBannerIndex,
        style: HomeSectionStyle(margin: EdgeInsets.symmetric(horizontal: 15.gw)),
      ),
      // 菜单
      const HomeMenuSection(),
      // 行情 Tab
      YhxtHomeMarketSection(
        tabIndex: state.marketTabIndex,
        tabTypes: state.marketTabTypes,
        onTabChanged: cubit.updateMarketTabIndex,
      ),
      // 新闻
      HomeNewsSection(
        newsData: state.newsData,
        status: state.newsStatus,
      ),
    ];
  }

  void _onMenuIconTap(BuildContext context) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final buttonPosition = button.localToGlobal(Offset.zero);
    final RelativeRect position = RelativeRect.fromSize(
      Rect.fromLTRB(
        buttonPosition.dx + button.size.width - 200.gw,
        buttonPosition.dy + 50.gw,
        buttonPosition.dx + button.size.width,
        buttonPosition.dy + 100.gw,
      ),
      const Size(110, 160),
    );
    showMenu(
      context: context,
      position: position,
      elevation: 0,
      color: Colors.transparent,
      constraints: BoxConstraints(maxWidth: 200.gw),
      items: [
        const PopupMenuItem(
          enabled: false,
          padding: EdgeInsets.zero,
          child: SettingsMenu(),
        ),
      ],
    );
  }

  void _onSearchIconTap(BuildContext context) {
    AuthUtils.verifyAuth(
      () => getIt<NavigatorService>().push(AppRouter.routeInstrumentSearch),
    );
  }
}
