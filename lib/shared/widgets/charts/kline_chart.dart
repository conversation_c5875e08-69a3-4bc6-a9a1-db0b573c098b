import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/market/stock_kline_entity.dart';

import 'adapters/kline_adapter.dart';
import 'adapters/chen_kline_adapter.dart';
import 'models/kline_config.dart';

/// K-line Chart Widget
///
/// This widget provides a unified interface for K-line charts across the app.
/// It uses an adapter pattern to support multiple chart packages without
/// changing the business logic.
///
/// Example usage:
/// ```dart
/// KlineChart(
///   rawData: klineItems,
///   config: KlineConfig(
///     period: ChartPeriod.intraday,
///     mode: ChartMode.line,
///     marketType: MarketType.aShares,
///   ),
/// )
/// ```
class KlineChart extends StatefulWidget {
  /// Raw data from API (will be converted by adapter)
  final List<StockKlineItem> rawData;

  /// Chart configuration
  final KlineConfig config;

  /// Chart adapter (defaults to ChenKlineAdapter)
  /// Can be overridden
  final KlineAdapter? adapter;

  /// Controller for programmatic chart control
  final dynamic controller;

  const Kline<PERSON>hart({
    super.key,
    required this.rawData,
    required this.config,
    this.adapter,
    this.controller,
  });

  @override
  State<KlineChart> createState() => _KlineChartState();
}

class _KlineChartState extends State<KlineChart> {
  late KlineAdapter _adapter;
  dynamic _internalController;

  @override
  void initState() {
    super.initState();
    _adapter = widget.adapter ?? ChenKlineAdapter();
    _internalController = widget.controller ?? _adapter.createController();
  }

  @override
  void didUpdateWidget(KlineChart oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If adapter changed, recreate internal controller
    if (widget.adapter != oldWidget.adapter) {
      _adapter = widget.adapter ?? ChenKlineAdapter();
      _internalController = widget.controller ?? _adapter.createController();
    }
  }

  @override
  void dispose() {
    _adapter.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Build chart using adapter
    return _adapter.buildChart(
      context: context,
      rawData: widget.rawData,
      config: widget.config,
      controller: _internalController,
    );
  }
}

/// Builder widget for more control over chart building
/// Useful when you need to access the unified data before rendering
class KlineChartBuilder extends StatelessWidget {
  final List<dynamic> rawData;
  final KlineConfig config;
  final KlineAdapter? adapter;
  final Widget Function(BuildContext context, List<dynamic> data, KlineConfig config) builder;

  const KlineChartBuilder({
    super.key,
    required this.rawData,
    required this.config,
    required this.builder,
    this.adapter,
  });

  @override
  Widget build(BuildContext context) {
    return builder(context, rawData, config);
  }
}
