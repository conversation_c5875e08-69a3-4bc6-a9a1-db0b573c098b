import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/home.dart';
import 'package:gp_stock_app/core/models/apis/rank.dart';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';
import 'package:gp_stock_app/features/activity/activity_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/features/notifications/logic/notifications_cubit.dart';
import 'package:gp_stock_app/features/ranking/models/rank_tab.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';

import 'home_v2_state.dart';

/// 首页 V2 Cubit
/// 统一管理首页所有数据的获取和状态更新
class HomeV2Cubit extends Cubit<HomeV2State> {
  HomeV2Cubit() : super(const HomeV2State()) {
    _initTabTypes();
  }

  /// 初始化行情 Tab 类型（根据 TradingMode）
  void _initTabTypes() {
    TradingMode currentMode = AppConfig.instance.tradingMode;

    // 尝试从 SysSettingsCubit 获取实际的交易模式
    try {
      final sysSettingsState = getIt<SysSettingsCubit>().state;
      sysSettingsState.maybeWhen(
        loaded: (_, config) {
          currentMode = TradingModeExtension.fromIndex(config.tradingMode);
        },
        orElse: () {},
      );
    } catch (_) {
      // 如果获取失败，使用默认配置
    }

    final tabTypes = switch (currentMode) {
      TradingMode.stock => [
          MarketSectionTab.stockIndex,
          MarketSectionTab.stocks,
          MarketSectionTab.watchList,
        ],
      TradingMode.futures => [
          MarketSectionTab.cnFutures,
          MarketSectionTab.globalFutures,
          MarketSectionTab.watchList,
        ],
      TradingMode.stockAndFutures => [
          MarketSectionTab.stockIndex,
          MarketSectionTab.stocks,
          MarketSectionTab.cnFutures,
          MarketSectionTab.globalFutures,
          MarketSectionTab.watchList,
        ],
    };

    emit(state.copyWith(marketTabTypes: tabTypes));
  }

  /// 加载所有首页数据
  Future<void> loadAll() async {
    // 刷新首页自身数据
    await Future.wait([
      fetchBannerList(),
      fetchNotifications(),
      fetchNews(),
      if (AppConfig.instance.flavor == Flavor.tempb) fetchRankAvailable(),
    ]);

    // 同时刷新其他全局 Cubit 的数据（与原 HomeScreen 保持一致）
    _refreshGlobalCubits();
  }

  /// 刷新其他全局 Cubit 的数据
  void _refreshGlobalCubits() {
    try {
      getIt<MarketCubit>().fetchTableData(isHome: true);
      getIt<NotificationsCubit>().getNotificationCount();
      getIt<ActivityCubit>().getTasks();
    } catch (_) {
      // 忽略未注册的 Cubit 错误
    }
  }

  // ==================== Banner ====================

  /// 获取 Banner 列表
  Future<void> fetchBannerList() async {
    if (isClosed) return;
    emit(state.copyWith(bannerStatus: DataStatus.loading));

    try {
      final result = await HomeApi.fetchBannerList();
      if (isClosed) return;

      emit(state.copyWith(
        bannerStatus: DataStatus.success,
        bannerList: () => result,
      ));
    } catch (_) {
      if (!isClosed) {
        emit(state.copyWith(bannerStatus: DataStatus.failed));
      }
    }
  }

  /// 更新当前 Banner 索引
  void updateBannerIndex(int index) {
    if (isClosed) return;
    emit(state.copyWith(bannerIndex: index));
  }

  // ==================== 通知/跑马灯 ====================

  /// 获取通知列表
  Future<void> fetchNotifications({bool suppressPopup = false}) async {
    if (isClosed) return;
    emit(state.copyWith(
      notificationStatus: DataStatus.loading,
      shouldSuppressPopup: suppressPopup,
    ));

    try {
      final result = await HomeApi.fetchNotificationList();
      if (isClosed) return;

      emit(state.copyWith(
        notifications: result ?? [],
        notificationStatus: DataStatus.success,
      ));
    } catch (_) {
      if (!isClosed) {
        emit(state.copyWith(notificationStatus: DataStatus.failed));
      }
    }
  }

  /// 标记弹窗已显示（防止重复弹出）
  void suppressPopup() {
    if (isClosed) return;
    emit(state.copyWith(shouldSuppressPopup: true));
  }

  // ==================== 新闻 ====================

  /// 获取新闻列表
  Future<void> fetchNews({int page = 1, int pageSize = 20}) async {
    if (isClosed) return;
    emit(state.copyWith(newsStatus: DataStatus.loading));

    try {
      final result = await HomeApi.fetchNewsList(page: page, pageSize: pageSize);
      if (isClosed) return;

      emit(state.copyWith(
        newsStatus: DataStatus.success,
        newsData: () => result,
      ));
    } catch (_) {
      if (!isClosed) {
        emit(state.copyWith(newsStatus: DataStatus.failed));
      }
    }
  }

  // ==================== 行情 Tab ====================

  /// 更新当前选中的行情 Tab 索引
  void updateMarketTabIndex(int index) {
    if (isClosed) return;
    emit(state.copyWith(marketTabIndex: index));
  }

  // ==================== 排行榜 ====================

  /// 获取排行榜是否可用
  Future<void> fetchRankAvailable() async {
    if (isClosed) return;
    final isOk = await RankApi.fetchRankAvailable();
    if (isOk) {
      fetchRankList();
    }
  }

  /// 切换排行榜 Tab
  void switchRankTab(RankTabType tabType) {
    if (isClosed) return;
    if (state.currentRankTabType == tabType) return;

    emit(state.copyWith(currentRankTabType: tabType));

    // 如果该 tab 的数据为空，则获取数据
    if (state.rankMap[tabType]?.isEmpty ?? true) {
      fetchRankList();
    }
  }

  /// 获取排行榜列表
  Future<void> fetchRankList() async {
    if (isClosed) return;
    final res = await RankApi.fetchRankList(type: state.currentRankTabType.code);
    if (res != null) {
      final newRankMap = Map<RankTabType, List<RankingEntity>>.from(state.rankMap);
      newRankMap[state.currentRankTabType] = res.list.take(5).toList();
      emit(state.copyWith(rankMap: newRankMap));
    }
  }
}
