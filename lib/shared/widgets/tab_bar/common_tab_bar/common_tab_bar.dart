import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

export 'package:gp_stock_app/shared/widgets/tab_indicator/tc_underline_tab_indicator.dart';
export 'tab_bar_style_config.dart';
export 'tab_clipper.dart';

import 'tab_bar_style_config.dart';

/// TabBar 样式枚举 / TabBar style enum
enum CommonTabBarStyle {
  /// 圆角样式 / Round style
  round,

  /// 矩形样式 / Rectangular style
  rectangular,

  /// 下划线样式 / Line/underline style
  line,

  /// 梯形样式 / Trapezoid style
  trapezoid;

  /// 获取样式配置 / Get style configuration
  TabBarStyleConfig config(BuildContext context, {bool isScrollable = true}) {
    return switch (this) {
      round => TabBarStyleConfig.round(context, isScrollable: isScrollable),
      rectangular => TabBarStyleConfig.rectangular(context, isScrollable: isScrollable),
      line => TabBarStyleConfig.line(context, isScrollable: isScrollable),
      trapezoid => TabBarStyleConfig.trapezoid(context, isScrollable: isScrollable),
    };
  }
}

/// 通用 TabBar 组件 / Common TabBar widget
class CommonTabBar extends StatefulWidget {
  /// Tab 数据列表 / Tab data list
  final List<String> data;

  /// 当前选中索引 / Current selected index
  final int currentIndex;

  /// 外边距 / Padding
  final EdgeInsets? padding;

  /// 标签内边距 / Label padding
  final EdgeInsets? labelPadding;

  /// 点击回调 / Tap callback
  final ValueChanged<int>? onTap;

  /// 背景颜色 / Background color
  final Color backgroundColor;

  /// 高度 / Height
  final double height;

  /// Tab 对齐方式 / Tab alignment
  final TabAlignment? tabAlignment;

  /// 圆角半径 / Border radius
  final double radius;

  /// 样式类型 / Style type
  final CommonTabBarStyle style;

  /// 是否可滚动 / Is scrollable
  final bool isScrollable;

  /// 样式覆盖回调 / Style overrides callback
  final TabBarStyleOverrides? styleOverrides;

  const CommonTabBar({
    super.key,
    required this.data,
    this.currentIndex = 0,
    this.padding,
    this.labelPadding,
    this.onTap,
    this.backgroundColor = Colors.white,
    this.height = 40,
    this.tabAlignment,
    this.radius = 0,
    this.style = CommonTabBarStyle.line,
    this.isScrollable = true,
    this.styleOverrides,
  }) : assert(currentIndex >= 0 && currentIndex < data.length);

  /// 自动生成 Key 的工厂构造函数 / Factory constructor with automatic key generation
  factory CommonTabBar.withAutoKey(
    List<String> data, {
    int currentIndex = 0,
    EdgeInsets? padding,
    EdgeInsets? labelPadding,
    Function(int)? onTap,
    Color? backgroundColor,
    double height = 40,
    TabAlignment? tabAlignment,
    double radius = 0,
    CommonTabBarStyle style = CommonTabBarStyle.line,
    bool isScrollable = true,
    TabBarStyleOverrides? styleOverrides,
  }) {
    return CommonTabBar(
      key: ValueKey('${data.join('_')}_${data.length}_$currentIndex'),
      data: data,
      currentIndex: currentIndex,
      padding: padding,
      labelPadding: labelPadding,
      onTap: onTap,
      backgroundColor: backgroundColor ?? Colors.white,
      height: height,
      tabAlignment: tabAlignment,
      radius: radius,
      style: style,
      isScrollable: isScrollable,
      styleOverrides: styleOverrides,
    );
  }

  @override
  State<CommonTabBar> createState() => _CommonTabBarState();
}

class _CommonTabBarState extends State<CommonTabBar> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.data.length,
      vsync: this,
      initialIndex: widget.currentIndex,
    );
    _tabController.addListener(_handleTabChange);
  }

  @override
  void didUpdateWidget(covariant CommonTabBar old) {
    super.didUpdateWidget(old);

    // If data length changes, recreate controller
    if (old.data.length != widget.data.length) {
      // Safely dispose old controller
      final oldController = _tabController;
      oldController.removeListener(_handleTabChange);

      // Create new controller
      _tabController = TabController(
        length: widget.data.length,
        vsync: this,
        initialIndex: widget.currentIndex.clamp(0, widget.data.length - 1),
      );
      _tabController.addListener(_handleTabChange);

      // Dispose old controller
      oldController.dispose();
    }
    // If external currentIndex changes, sync to TabController
    else if (old.currentIndex != widget.currentIndex &&
        widget.currentIndex != _tabController.index &&
        !_tabController.indexIsChanging) {
      _tabController.animateTo(widget.currentIndex.clamp(0, widget.data.length - 1));
    }
  }

  void _handleTabChange() {
    // Only trigger on user interaction, avoid external animateTo callbacks
    if (mounted && _tabController.indexIsChanging) {
      widget.onTap?.call(_tabController.index);
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_tabController.length != widget.data.length) {
      return const SizedBox.shrink();
    }

    // Get style configuration
    var styleConfig = widget.style.config(
      context,
      isScrollable: widget.isScrollable,
    );

    // Apply style overrides if provided
    if (widget.styleOverrides != null) {
      styleConfig = widget.styleOverrides!(styleConfig);
    }

    return Container(
      padding: widget.padding,
      height: widget.height,
      decoration: BoxDecoration(
        color: styleConfig.showBackground ? widget.backgroundColor : Colors.transparent,
        borderRadius: BorderRadius.circular(styleConfig.radius ?? widget.radius),
        border: styleConfig.borderWidth != null
            ? Border.all(color: styleConfig.borderColor ?? context.theme.primaryColor, width: styleConfig.borderWidth!)
            : null,
      ),
      child: styleConfig.customBuilder?.call(context, widget.data, _tabController.index, widget.onTap) ??
          TabBar(
            controller: _tabController,
            isScrollable: widget.isScrollable,
            tabAlignment: widget.tabAlignment ?? (widget.isScrollable ? TabAlignment.start : null),
            splashFactory: NoSplash.splashFactory,
            overlayColor: WidgetStateProperty.all(context.theme.cardColor),
            dividerColor: Colors.transparent,
            labelPadding: widget.labelPadding ?? styleConfig.labelPadding,
            labelStyle: styleConfig.selectedStyle,
            unselectedLabelStyle: styleConfig.unselectedStyle,
            indicator: styleConfig.indicator,
            indicatorSize: TabBarIndicatorSize.tab,
            indicatorWeight: 0,
            tabs: widget.data.map((str) => Tab(text: str)).toList(),
          ),
    );
  }
}


