import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';
import 'package:gp_stock_app/features/ranking/models/rank_tab.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class RankingState extends Equatable {
  const RankingState({
    this.currentRankTabType = RankTabType.daily,
    this.rankList = const [],
    this.selfRank,
    this.rankStatus = DataStatus.idle,
  });

  final RankTabType currentRankTabType;

  final List<RankingEntity> rankList;

  final RankingEntity? selfRank;

  final DataStatus rankStatus;


  RankingState copyWith({
    RankTabType? currentRankTabType,
    List<RankingEntity>? rankList,
    RankingEntity? selfRank,
    DataStatus? rankStatus,
  }) {
    return RankingState(
      currentRankTabType: currentRankTabType ?? this.currentRankTabType,
      rankList: rankList ?? this.rankList,
      selfRank: selfRank ?? this.selfRank,
      rankStatus: rankStatus ?? this.rankStatus,
    );
  }

  @override
  List<Object?> get props => [
        currentRankTabType,
        rankList,
        selfRank,
        rankStatus,
      ];
}
