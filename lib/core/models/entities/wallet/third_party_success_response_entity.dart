import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/third_party_success_response_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/third_party_success_response_entity.g.dart';

@JsonSerializable()
class ThirdPartySuccessEntity {
  final int amount;
  final String currency;
  final String orderNo;
  final String payUrl;
  final String payWayName;
  final String tradeTime;

  const ThirdPartySuccessEntity({
    this.amount = 0,
    this.currency = "",
    this.orderNo = "",
    this.payUrl = "",
    this.payWayName = "",
    this.tradeTime = "",
  });

  factory ThirdPartySuccessEntity.fromJson(Map<String, dynamic> json) => $ThirdPartySuccessResponseEntityFromJson(json);

  Map<String, dynamic> toJson() => $ThirdPartySuccessResponseEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
