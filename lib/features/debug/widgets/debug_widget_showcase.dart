import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/card/section_container.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/app_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/switch/common_switch.dart';

import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

class DebugWidgetShowcase extends StatefulWidget {
  final AppSkinStyle skinStyle;

  const DebugWidgetShowcase({
    super.key,
    required this.skinStyle,
  });

  @override
  State<DebugWidgetShowcase> createState() => _DebugWidgetShowcaseState();
}

class _DebugWidgetShowcaseState extends State<DebugWidgetShowcase> {
  bool _switchValue = false;
  final TextEditingController _textController = TextEditingController();

  final Map<CommonTabBarStyle, int> _tabIndices = {
    CommonTabBarStyle.line: 0,
    CommonTabBarStyle.round: 0,
    CommonTabBarStyle.rectangular: 0,
    CommonTabBarStyle.trapezoid: 0,
  };
  String? _selectedDropdownValue;

  static const List<String> _dropdownOptions = ['Option 1', 'Option 2', 'Option 3'];

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Colors Section
        _buildSection(
          title: 'Theme Colors',
          icon: Icons.color_lens,
          child: Wrap(
            runSpacing: 8.gw,
            spacing: 8.gw,
            children: [
              _buildColorRow('Primary', context.theme.primaryColor),
              _buildColorRow('Primary Light', context.theme.primaryColorLight),
              _buildColorRow('Card Color', context.theme.cardColor),
              _buildColorRow('Divider', context.theme.dividerColor),
              _buildColorRow('CanvasColor', context.theme.canvasColor),
              _buildColorRow('Scaffold BG', context.theme.scaffoldBackgroundColor),
              _buildColorRow('Disabled', context.theme.disabledColor),
              _buildColorRow('Text Primary', context.colorTheme.textPrimary),
              _buildColorRow('Stock Red', context.colorTheme.stockRed),
              _buildColorRow('Stock Green', context.colorTheme.stockGreen),
              _buildColorRow('Pending', context.colorTheme.pending),
              _buildColorRow('Border', context.colorTheme.border),
              _buildColorRow('Button Primary', context.colorTheme.buttonPrimary),
              _buildColorRow('Button Secondary', context.colorTheme.buttonSecondary),
              _buildColorRow('InviteBG Start', context.colorTheme.inviteBackgroundStart),
              _buildColorRow('InviteBG End', context.colorTheme.inviteBackgroundEnd),
            ],
          ),
        ),

        SizedBox(height: 16.gw),

        // Typography Section
        _buildSection(
          title: 'Typography',
          icon: Icons.text_format,
          child: Container(
            padding: EdgeInsets.all(8.gw),
            color: context.theme.scaffoldBackgroundColor,
            child: Wrap(
              runSpacing: 8.gw,
              spacing: 8.gw,
              children: [
                Text('Primary', style: context.textTheme.primary),
                Text('Secondary', style: context.textTheme.secondary),
                Text('Title', style: context.textTheme.title),
                Text('Regular', style: context.textTheme.regular),
                Text('Tertiary', style: context.textTheme.tertiary),
                Text('Pending', style: context.textTheme.pending),
                Text('Highlight', style: context.textTheme.highlight),
                Text('Active', style: context.textTheme.active),
                Text('Button Primary', style: context.textTheme.buttonPrimary),
                Text('Stock Red', style: context.textTheme.stockRed),
                Text('Stock Green', style: context.textTheme.stockGreen),
              ],
            ),
          ),
        ),
        SizedBox(height: 16.gw),
        // Buttons Section
        _buildSection(
          title: 'Buttons',
          icon: Icons.smart_button,
          child: Column(
            children: [
              _buildButtonRow([
                CommonButton(
                  title: 'Primary',
                  style: CommonButtonStyle.primary,
                  onPressed: () {},
                ),
                CommonButton(
                  title: 'Stock Red',
                  style: CommonButtonStyle.stockRed,
                  onPressed: () {},
                ),
              ]),
              SizedBox(height: 12.gw),
              _buildButtonRow([
                CommonButton(
                  title: 'Stock Green',
                  style: CommonButtonStyle.stockGreen,
                  onPressed: () {},
                ),
                CommonButton(
                  title: 'Outlined',
                  style: CommonButtonStyle.outlined,
                  onPressed: () {},
                ),
              ]),
              SizedBox(height: 12.gw),
              CommonButton(
                title: 'Loading Button',
                style: CommonButtonStyle.primary,
                showLoading: true,
                onPressed: () {},
              ),
            ],
          ),
        ),

        SizedBox(height: 16.gw),

        // Cards Section
        _buildSection(
          title: 'Cards & Containers',
          icon: Icons.crop_portrait,
          child: Column(
            children: [
              ShadowBox(
                child: Text(
                  'This is a ShadowBox widget with default styling',
                  style: context.textTheme.primary,
                ),
              ),
              SizedBox(height: 12.gw),
              SectionContainer(
                title: 'Section Container',
                suffixIcon: Icon(
                  Icons.arrow_forward_ios,
                  size: 16.gw,
                  color: context.colorTheme.textRegular,
                ),
                child: Text(
                  'This is a SectionContainer with title bar and divider',
                  style: context.textTheme.primary,
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: 16.gw),

        // Text Fields Section
        _buildSection(
          title: 'Text Fields',
          icon: Icons.text_fields,
          child: Column(
            children: [
              TextFieldWidget(
                hintText: 'Enter your text here',
                controller: _textController,
                prefixIcon: Icon(
                  Icons.person,
                  color: context.colorTheme.textRegular,
                ),
              ),
              SizedBox(height: 12.gw),
              TextFieldWidget(
                hintText: 'Password field',
                obscureText: true,
                suffixIcon: Icon(
                  Icons.visibility_off,
                  color: context.colorTheme.textRegular,
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: 16.gw),

        // Tab Bar Section
        _buildSection(
          title: 'Tab Bars V2',
          icon: Icons.tab,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...CommonTabBarStyle.values.map((style) => _buildTabBarExample(
                    context,
                    style,
                    _getTabBarTitle(style),
                    _getTabBarData(style),
                  )),
            ],
          ),
        ),

        SizedBox(height: 16.gw),

        // Dropdown Section
        _buildSection(
          title: 'Dropdowns',
          icon: Icons.arrow_drop_down,
          child: AppDropdown<String>(
            hintText: 'Select an option',
            items: _dropdownOptions
                .map((option) => DropdownMenuItem<String>(
                      value: option.toLowerCase().replaceAll(' ', ''),
                      child: Text(option),
                    ))
                .toList(),
            selected: _selectedDropdownValue,
            onChanged: (value) {
              setState(() {
                _selectedDropdownValue = value;
              });
            },
          ),
        ),

        SizedBox(height: 16.gw),

        // Switches Section
        _buildSection(
          title: 'Switches & Controls',
          icon: Icons.toggle_on,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Toggle Switch',
                style: context.textTheme.primary,
              ),
              CommonSwitch(
                value: _switchValue,
                onChanged: (value) {
                  setState(() {
                    _switchValue = value;
                  });
                },
              ),
            ],
          ),
        ),

        SizedBox(height: 32.gw),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: context.theme.primaryColor,
                size: 20.gw,
              ),
              SizedBox(width: 8.gw),
              Text(
                title,
                style: context.textTheme.primary.fs16.w600,
              ),
            ],
          ),
          SizedBox(height: 16.gw),
          child,
        ],
      ),
    );
  }

  Widget _buildButtonRow(List<Widget> buttons) {
    return Row(
      children: buttons
          .map((button) => Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 4.gw),
                  child: button,
                ),
              ))
          .toList(),
    );
  }

  Widget _buildColorRow(String name, Color color) {
    return IntrinsicWidth(
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 1.gw),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              width: 16.gw,
              height: 16.gw,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(4.gr),
                border: Border.all(
                  color: context.theme.dividerColor,
                  width: 1,
                ),
              ),
            ),
            SizedBox(width: 4.gw),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: context.textTheme.primary.fs12,
                ),
                Text(
                  '#${color.toARGB32().toRadixString(16).toUpperCase().padLeft(8, '0').substring(2)}',
                  style: context.textTheme.primary.fs10.copyWith(
                    fontFamily: 'monospace',
                    color: context.colorTheme.textRegular,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // build tab bar example
  Widget _buildTabBarExample(
    BuildContext context,
    CommonTabBarStyle style,
    String title,
    List<String> data,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: context.textTheme.primary.fs12.w600,
        ),
        SizedBox(height: 8.gw),
        SizedBox(
          width: double.infinity,
          child: CommonTabBar(
            data: data,
            currentIndex: _tabIndices[style]!,
            onTap: (index) {
              setState(() {
                _tabIndices[style] = index;
              });
            },
            style: style,
            backgroundColor: context.theme.scaffoldBackgroundColor,
            isScrollable: true,
          ),
        ),
        SizedBox(height: 16.gw),
      ],
    );
  }

  String _getTabBarTitle(CommonTabBarStyle style) {
    switch (style) {
      case CommonTabBarStyle.line:
        return 'Line Style (Default)';
      case CommonTabBarStyle.round:
        return 'Round Style';
      case CommonTabBarStyle.rectangular:
        return 'Rectangular Style';
      case CommonTabBarStyle.trapezoid:
        return 'Trapezoid Style';
    }
  }

  List<String> _getTabBarData(CommonTabBarStyle style) {
    switch (style) {
      case CommonTabBarStyle.line:
        return const ['Line', 'Tab', 'Style'];
      case CommonTabBarStyle.round:
        return const ['Round', 'Tab', 'Style'];
      case CommonTabBarStyle.rectangular:
        return const ['Rect', 'Tab', 'Style', 'Longer', 'Item', 'Even', 'Longer', 'Item'];
      case CommonTabBarStyle.trapezoid:
        return const ['Rect', 'Tab', 'Style', 'Longer', 'Item', 'Even', 'Longer', 'Item'];
    }
  }
}
