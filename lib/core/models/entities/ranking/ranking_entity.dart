import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/ranking_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/ranking_entity.g.dart';

@JsonSerializable()
class RankingEntityList {
  final RankingEntity? self;
  final List<RankingEntity> list;

  const RankingEntityList({
    this.list = const [],
    this.self,
  });

  factory RankingEntityList.fromJson(Map<String, dynamic> json) => $RankingEntityListFromJson(json);

  Map<String, dynamic> toJson() => $RankingEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class RankingEntity {
  final double amount;
  final String avatar;
  final bool isSelf;
  final String nickName;
  final double profitRate;
  final int rankingNo;
  final int userId;

  const RankingEntity({
    this.amount = 0.0,
    this.avatar = '',
    this.isSelf = false,
    this.nickName = '',
    this.profitRate = 0.0,
    this.rankingNo = 0,
    this.userId = 0,
  });

  factory RankingEntity.fromJson(Map<String, dynamic> json) => $RankingEntityFromJson(json);

  Map<String, dynamic> toJson() => $RankingEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
