part of 'fund_records_cubit.dart';

class FundRecordsState extends Equatable {
  final DataStatus assetsRecordsFetchStatus;
  final FundsRecordList? fundsRecords;
  final String? error;
  final DateFilter? dateFilter;
  final TransactionType? transactionType;
  const FundRecordsState({
    this.assetsRecordsFetchStatus = DataStatus.idle,
    this.fundsRecords,
    this.dateFilter,
    this.transactionType,
    this.error,
  });

  @override
  List<Object?> get props => [assetsRecordsFetchStatus, fundsRecords, error, dateFilter, transactionType];

  FundRecordsState copyWith({
    DataStatus? assetsRecordsFetchStatus,
    FundsRecordList? fundsRecords,
    String? error,
    DateFilter? Function()? dateFilter,
    TransactionType? Function()? transactionType,
  }) {
    return FundRecordsState(
      assetsRecordsFetchStatus: assetsRecordsFetchStatus ?? this.assetsRecordsFetchStatus,
      fundsRecords: fundsRecords ?? this.fundsRecords,
      error: error ?? this.error,
      dateFilter: dateFilter != null ? dateFilter() : this.dateFilter,
      transactionType: transactionType != null ? transactionType() : this.transactionType,
    );
  }
}
