import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'contract_apply_state.dart';

/// 合约申请入口 Cubit
class ContractApplyCubit extends Cubit<ContractApplyState> {
  ContractApplyCubit() : super(const ContractApplyState()) {
    fetchOpenContractTypes();
  }

  Future<void> fetchOpenContractTypes() async {
    emit(state.copyWith(fetchStatus: DataStatus.loading));

    final result = await ContractApi.fetchOpenContractTypes();

    if (isClosed) return;

    if (result != null) {
      emit(state.copyWith(
        fetchStatus: DataStatus.success,
        openContractTypes: result,
      ));
    } else {
      emit(state.copyWith(fetchStatus: DataStatus.failed));
    }
  }
}
