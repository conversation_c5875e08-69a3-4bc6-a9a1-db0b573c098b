import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/account/account.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

import '../../../shared/widgets/flip_text.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AssetsSection extends StatelessWidget {
  const AssetsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.all(16.gr),
      padding: EdgeInsets.all(16.gr),
      decoration: BoxDecoration(
        color: context.theme.primaryColor,
        borderRadius: BorderRadius.circular(8.gr),
        boxShadow: [
          BoxShadow(
            color: context.theme.primaryColor.withNewOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Wrap(
        alignment: WrapAlignment.spaceBetween,
        children: [
          Text(
            'totalAssets'.tr(),
            style: context.textTheme.primary.fs20.w700.copyWith(
              color: Colors.white.withNewOpacity(0.8),
            ),
          ),
          BlocSelector<UserCubit, UserState, AccountInfo?>(
            selector: (state) => state.accountInfo,
            builder: (context, accountInfo) {
              return FlipText(
                accountInfo?.accountAmount ?? 0,
                isCurrency: true,
                showCurrencyDropdown: true,
                dropdownIconColor: Colors.white.withNewOpacity(0.8),
                style: context.textTheme.primary.w600.copyWith(
                  color: Colors.white,
                  fontSize: 25,
                  height: 1.2,
                  fontFamily: 'Akzidenz-Grotesk',
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
