import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/user.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/user.g.dart';

@JsonSerializable()
class UserModel extends Equatable {
	final bool auth;
	final int authStatus;
	final String avatar;
	final String countryCode;
	final String email;
	final int fromType;
	final int id;
	final String idCard;
	final String imAccount;
	final String inviteCode;
	final bool isPayment;
	final int level;
	final String mobile;
	final String nickname;
	final int pid;
	final String profiles;
	final String realName;
	final int score;
	final int sex;
	final bool status;
	final int tradeStatus;
	final int type;
	final String uid;
  final String username;
  final String registerType;

	const UserModel({
		this.auth = false,
		this.authStatus = 0,
		this.avatar = '',
		this.countryCode = '',
		this.email = '',
		this.fromType = 0,
		this.id = 0,
		this.idCard = '',
		this.imAccount = '',
		this.inviteCode = '',
		this.isPayment = false,
		this.level = 0,
		this.mobile = '',
		this.nickname = '',
		this.pid = 0,
		this.profiles = '',
		this.realName = '',
		this.score = 0,
		this.sex = 0,
		this.status = false,
		this.tradeStatus = 0,
		this.type = 0,
		this.uid = '',
    this.username = '',
    this.registerType = '',
	});

	factory UserModel.fromJson(Map<String, dynamic> json) => $UserModelFromJson(json);

	Map<String, dynamic> toJson() => $UserModelToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}

  @override
  List<Object?> get props => [
		auth,
		authStatus,
		avatar,
		countryCode,
		email,
		fromType,
		id,
		idCard,
		imAccount,
		inviteCode,
		isPayment,
		level,
		mobile,
		nickname,
		pid,
		profiles,
		realName,
		score,
		sex,
		status,
		tradeStatus,
		type,
		uid,
    username,
    registerType,
	];
}