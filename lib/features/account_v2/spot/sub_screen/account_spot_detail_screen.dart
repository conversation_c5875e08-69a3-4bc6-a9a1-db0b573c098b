import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/account/account.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/assets_card.dart';
import 'package:gp_stock_app/features/account/widgets/build_action_buttons.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/order_list_state.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/account_order_list_view.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/order_table_header/order_table_header.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/features/market/widgets/sliver_app_bar_delegate.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';
import '../../0_home/account_screen_cubit_v2.dart';

class AccountSpotDetailScreen extends StatelessWidget {
  final MarketCategoryState viewModel;

  const AccountSpotDetailScreen({super.key, required this.viewModel});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AccountScreenCubitV2>();

    final keys = viewModel.details.keys.toList();
    OrderListType? selectedType;
    bool needShowTableHeader = false;
    if (viewModel.selectedIndex < keys.length) {
      selectedType = keys[viewModel.selectedIndex];
      /// 成交明细/委托明细 需要显示头部标题
      needShowTableHeader = selectedType != OrderListType.positions;
    }

    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          /// 资产模块
          SliverToBoxAdapter(child: _buildAssetCard(context)),
          if (keys.isNotEmpty)
            SliverOverlapAbsorber(
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
              sliver: SliverPersistentHeader(
                pinned: true,
                delegate: SliverAppBarDelegate(
                  maxHeight: needShowTableHeader ? 86 : 45,
                  minHeight: needShowTableHeader ? 86 : 45,
                  child: ColoredBox(
                    color: context.theme.scaffoldBackgroundColor,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CommonTabBar.withAutoKey(
                          viewModel.details.entries
                              .map((e) => "${tr(e.key.nameKey)}${e.value.countIfNotEmpty}")
                              .toList(),
                          currentIndex: viewModel.selectedIndex,
                          onTap: (index) => cubit.updateSpotMarketSubScreenIndex(index),
                          style: CommonTabBarStyle.line,
                          isScrollable: true,
                          padding: EdgeInsets.only(left: 8.gw),
                        ),
                        SizedBox(height: 5),

                        /// 成交明细/委托明细 头部标题
                        if (needShowTableHeader) ...[
                          OrderTableHeader(
                            type: selectedType!,
                            margin: EdgeInsets.symmetric(horizontal: 18.gw),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ];
      },
      body: DirectSlideView(
        pages: viewModel.details.entries.map((entry) {
          final type = entry.key;
          final orderListState = entry.value;
          return AccountOrderListView(
            key: Key("${viewModel.category.nameKey}_${type.nameKey}"),
            marketCategory: viewModel.category,
            tradingAccountType: TradingAccountType.Spot,
            orderType: type,
            orderListState: orderListState,
            onVisibleRecordsChanged: (visibleIds) {
              cubit.updateVisibleRecords(viewModel.category, type, visibleIds);
            },
            onSellSuccess: () => cubit.fetchSpotScreenCurrentData(forceRefresh: true), // 卖出成功后全量刷新现货数据
            onCancelSuccess: () => cubit.fetchSpotScreenCurrentData(forceRefresh: true), // 撤单成功后全量刷新现货数据
          );
        }).toList(),
        pageIndex: viewModel.selectedIndex,
        onPageChanged: (index) => cubit.updateSpotMarketSubScreenIndex(index),
      ),
    );
  }

  Widget _buildAssetCard(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.gw),
      padding: EdgeInsets.only(bottom: 16.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(10.gr),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Assets Card
          BlocSelector<UserCubit, UserState, AccountInfo?>(
            selector: (state) => state.accountInfo,
            builder: (context, model) {
              return AssetsCard(
                assetTitle: 'heldAssets'.tr(), // 持仓资产
                profitTitle: 'floatingProfitLoss'.tr(), // 浮动盈亏
                totalAssets: viewModel.totalAssets,
                todayEarnings: viewModel.floatingPnl,
                availableBalance: model?.usableCash,
                myInterest: model?.interestCash,
                frozenAmount: model?.freezeCash,
                currency: model?.currency,
              );
            },
          ),
          // Action Buttons
          buildActionButtons(context),
        ],
      ),
    );
  }

  Widget buildActionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BuildActionButton(
          label: 'topUpDeposit'.tr(),
          icon: Assets.myAssetIcon,
          onTap: () {
            getIt<NavigatorService>().push(AppRouter.routeDepositMain);
          },
        ),
        BuildActionButton(
          label: 'cashOut'.tr(),
          icon: Assets.withdrawIcon,
          onTap: () {
            AuthUtils.verifyAuth(
              () => context.verifyRealName(
                () => getIt<NavigatorService>().push(AppRouter.routeWithdrawMain),
              ),
            );
          },
        ),

        /// 交易中心
        BuildActionButton(
          label: 'tradingCenter'.tr(),
          icon: Assets.tradingIcon,
          onTap: () {
            final category = viewModel.category;
            context.read<MainCubit>().selectedNavigationItem(BottomNavType.trade);
            context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.stockIndex);
            if (category.code < 4) {
              /// 股票类型
              context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.stocks);
              context.read<MarketCubit>().updateMarketCategory(MarketCategory.fromCode(category.code));
            } else if (category == MarketCategory.stockIndex) {
              context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.stockIndex);
            } else if (category == MarketCategory.cnFutures) {
              context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.cnFutures);
            } else if (category == MarketCategory.globalFutures) {
              context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.globalFutures);
            }

            getIt<NavigatorService>().popToRoot();
          },
        ),

        /// 资金记录
        BuildActionButton(
          label: 'fundRecords'.tr(),
          icon: Assets.recordsIcon,
          onTap: () => getIt<NavigatorService>().push(AppRouter.routeFundRecords),
        ),

        /// 历史订单
        BuildActionButton(
          label: 'transactionHistory'.tr(),
          icon: Assets.historyIcon,
          onTap: () => getIt<NavigatorService>().push(
            AppRouter.routeOrderHistory,
            arguments: {"category": viewModel.category},
          ),
        ),
      ],
    );
  }
}
