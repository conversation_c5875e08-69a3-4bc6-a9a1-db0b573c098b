import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/wallet/alipay_account_entity.dart';
import 'package:gp_stock_app/core/models/entities/wallet/withdraw_config_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

class AlipayApi {
  static Future<WithdrawConfigEntity?> fetchAlipayWithdrawConfig() async {
    final res = await Http().request<WithdrawConfigEntity>(
      ApiEndpoints.alipayWithdrawConfig,
      method: HttpMethod.get,
    );
    return res.data;
  }

  static Future<List<AlipayAccountEntity>> listAlipayAccount() async {
    final res = await Http().request<AlipayAccountEntityList>(
      ApiEndpoints.listALiPay,
      method: HttpMethod.get,
    );
    return res.data?.list ?? [];
  }

  static Future<bool> unbindUserWallet(int id) async {
    final res = await Http().request<bool>(
      '${ApiEndpoints.unbindUserWallet}/$id',
      method: HttpMethod.delete,
    );
    return res.data ?? false;
  }
}
