// import 'package:extra_hittest_area/extra_hittest_area.dart';
// import 'package:flutter/material.dart';
//
// import 'package:gp_stock_app/core/utils/keyboard_config.dart';
// import 'package:gp_stock_app/core/utils/screen_util.dart';
// import 'package:keyboard_actions/keyboard_actions.dart';
// import 'package:gp_stock_app/core/theme/app_themes.dart';
//
// class FTradeBuySellTextField extends StatelessWidget {
//   static const double _buttonSize = 20.0;
//   static const double _buttonRadius = 4.0;
//   static const double _iconSize = 16.0;
//   static const double _horizontalSpacing = 8.0;
//
//   final VoidCallback? onIncrementPressed;
//   final VoidCallback? onDecrementPressed;
//   final VoidCallback onTap;
//   final TextEditingController controller;
//   final FocusNode focusNode;
//   const FTradeBuySellTextField({
//     super.key,
//     required this.onIncrementPressed,
//     required this.onDecrementPressed,
//     required this.controller,
//     required this.focusNode,
//     required this.onTap,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: 35.gw,
//       decoration: BoxDecoration(
//         color: context.theme.inputDecorationTheme.fillColor,
//         borderRadius: BorderRadius.circular(5.gr),
//       ),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         spacing: 1,
//         children: [
//           _buildControlButton(
//             isLeft: true,
//             context: context,
//             icon: Icons.remove,
//             onPressed: onDecrementPressed,
//           ),
//           Expanded(child: _buildTextField(context)),
//           _buildControlButton(
//             isLeft: false,
//             context: context,
//             icon: Icons.add,
//             onPressed: onIncrementPressed,
//           ),
//         ],
//       ),
//     );
//   }
//
//   /// Builds the text input field
//   Widget _buildTextField(BuildContext context) {
//     return KeyboardActions(
//       config: KeyboardConfig.buildTradeConfig(context, focusNode),
//       autoScroll: false,
//       disableScroll: true,
//       child: TextField(
//         focusNode: focusNode,
//         controller: controller,
//         textInputAction: TextInputAction.done,
//         keyboardType: const TextInputType.numberWithOptions(decimal: true),
//         // inputFormatters: [
//         //   FilteringTextInputFormatter.allow(RegExp(r'[\d\.,]')),
//         //   CommaReplacementFormatter(),
//         // ],
//         onTap: onTap,
//         decoration: InputDecoration(
//           border: InputBorder.none,
//           focusedBorder: InputBorder.none,
//           hintStyle: context.textTheme.primary,
//           isDense: true,
//           contentPadding: EdgeInsets.symmetric(
//             horizontal: _horizontalSpacing.gw,
//             vertical: _horizontalSpacing.gw,
//           ),
//         ),
//         textAlign: TextAlign.center,
//         style: context.textTheme.primary,
//       ),
//     );
//   }
//
//   /// Builds the increment/decrement button
//   Widget _buildControlButton({
//     required bool isLeft,
//     required BuildContext context,
//     required IconData icon,
//     required VoidCallback? onPressed,
//   }) {
//     return GestureDetector(
//       behavior: HitTestBehavior.opaque,
//       onTap: onPressed,
//       child: Padding(
//         padding: isLeft ? EdgeInsets.only(left: 12.gw) : EdgeInsets.only(right: 12.gw),
//         child: Container(
//           height: _buttonSize.gw,
//           width: _buttonSize.gw,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(_buttonRadius.gr),
//             color: context.theme.cardColor,
//           ),
//           child: Center(
//             child: Icon(
//               icon,
//               size: _iconSize.gr,
//               color: onPressed == null ? context.theme.dividerColor : context.colorTheme.textRegular,
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
