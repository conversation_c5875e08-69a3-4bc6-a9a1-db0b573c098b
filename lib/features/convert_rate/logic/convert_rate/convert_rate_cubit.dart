import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/models/apis/common.dart';
import 'package:gp_stock_app/core/models/entities/common/exchange_rate_entity.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import 'convert_rate_state.dart';

@injectable
class ConvertRateCubit extends Cubit<ConvertRateState> {
  ConvertRateCubit()
      : super(const ConvertRateState(
          customAmount: "1",
          convertedCNY: 1,
        ));

  // Fetch exchange rates from API
  Future<void> fetchConvertRate() async {
    print("CUBIT: fetchConvertRate() called");
    emit(state.copyWith(status: DataStatus.loading));

    final result = await CommonApi.getExchangeRate();

    if (result.isNotEmpty) {
      print("CUBIT: fetchConvertRate success, got ${result.length} rates");
      emit(state.copyWith(
        status: DataStatus.success,
        rates: result,
        error: null,
      ));

      // Calculate initial conversions without changing base CNY value
      _calculateConversions(state.customAmount);
    }
  }

  // Update amount but only recalculate conversions for non-math inputs or when requested
  void updateAmount(String amount, {bool isEditing = true, bool recalculate = false}) {
    print("CUBIT: updateAmount($amount, isEditing=$isEditing, recalculate=$recalculate)");

    // Clean and validate the amount
    String cleanAmount = amount;
    if (cleanAmount.isEmpty || cleanAmount == '.') {
      cleanAmount = '0';
    }

    // Check if the input contains math operators
    bool containsMathOperations = cleanAmount.contains('+') ||
        cleanAmount.contains('-') ||
        cleanAmount.contains('×') ||
        cleanAmount.contains('÷') ||
        cleanAmount.contains('*') ||
        cleanAmount.contains('/');

    // Update state with new amount
    emit(state.copyWith(
      customAmount: cleanAmount,
      isEditingAmount: isEditing,
      isCalculating: recalculate && !containsMathOperations,
    ));

    // Only recalculate conversions if:
    // 1. recalculate is true and no math operations, or
    // 2. not editing and no math operations
    if ((recalculate || !isEditing) && !containsMathOperations) {
      print("CUBIT: Triggering recalculation for amount: $cleanAmount");
      _calculateConversions(cleanAmount);
    } else if (containsMathOperations) {
      print("CUBIT: Math operation detected, deferring conversion until equals");
    }
  }

  // Specifically for when calculation is complete
  void calculationComplete(String calculatedAmount) {
    print("CUBIT: calculationComplete($calculatedAmount)");
    updateAmount(calculatedAmount, isEditing: false, recalculate: true);
  }

  // Update selected currency
  void updateSelectedCurrency(String currency) {
    print("CUBIT: updateSelectedCurrency($currency), current: ${state.selectedCurrency}");
    if (currency == state.selectedCurrency) {
      print("CUBIT: Same currency, no update needed");
      return;
    }

    // Get the existing converted value for the new currency
    double? targetValue;
    switch (currency) {
      case 'USD':
        targetValue = state.convertedUSD;
        break;
      case 'CNY':
        targetValue = state.convertedCNY;
        break;
      case 'HKD':
        targetValue = state.convertedHKD;
        break;
      default:
        targetValue = 1.0;
    }

    // Format the value for display (integer if whole number, 4 decimals otherwise)
    String newAmount;
    if (targetValue == null || targetValue == 0) {
      newAmount = "1";
    } else if (targetValue == targetValue.toInt()) {
      newAmount = targetValue.toInt().toString();
    } else {
      newAmount = targetValue.toStringAsFixed(4);
    }

    print("CUBIT: Switching to $currency with amount: $newAmount");

    // Update state with new currency and amount, without recalculating conversions
    emit(state.copyWith(
      selectedCurrency: currency,
      customAmount: newAmount,
      isEditingAmount: false,
      isCalculating: false,
    ));
  }

  // Calculate conversions based on amount and selected currency
  void _calculateConversions(String amount) {
    try {
      print("CUBIT: _calculateConversions($amount) for ${state.selectedCurrency}");

      // Parse the amount
      double parsedAmount = _parseAmount(amount);
      print("CUBIT: Parsed amount: $parsedAmount");

      if (state.rates == null || state.rates!.isEmpty) {
        print("CUBIT: No rates available, using direct values");
        emit(state.copyWith(
          convertedUSD: parsedAmount,
          convertedCNY: parsedAmount,
          convertedHKD: parsedAmount,
          isCalculating: false,
        ));
        return;
      }

      // Default rates
      const defaultRates = {
        'CNY_USD': 0.14,
        'CNY_HKD': 1.09,
      };

      // Get rates from API or use defaults
      final rates = <String, double>{};

      // CNY to USD
      rates['CNY_USD'] = state.rates!
              .firstWhere(
                (rate) => rate.currencyBase == 'CNY' && rate.currencyTarget == 'USD',
                orElse: () => const ExchangeRate(currencyBase: 'CNY', currencyTarget: 'USD', rate: 0.14),
              )
              .rate ??
          defaultRates['CNY_USD']!;

      // CNY to HKD
      rates['CNY_HKD'] = state.rates!
              .firstWhere(
                (rate) => rate.currencyBase == 'CNY' && rate.currencyTarget == 'HKD',
                orElse: () => const ExchangeRate(currencyBase: 'CNY', currencyTarget: 'HKD', rate: 1.09),
              )
              .rate ??
          defaultRates['CNY_HKD']!;

      // Calculate derived rates
      rates['USD_CNY'] = 1 / rates['CNY_USD']!;
      rates['USD_HKD'] = rates['CNY_HKD']! / rates['CNY_USD']!;
      rates['HKD_CNY'] = 1 / rates['CNY_HKD']!;
      rates['HKD_USD'] = 1 / rates['USD_HKD']!;

      print("CUBIT: Calculated rates: $rates");

      // Calculate based on selected currency
      double usdAmount;
      double cnyAmount;
      double hkdAmount;

      switch (state.selectedCurrency) {
        case 'CNY':
          cnyAmount = parsedAmount;
          usdAmount = parsedAmount * rates['CNY_USD']!;
          hkdAmount = parsedAmount * rates['CNY_HKD']!;
          print("CUBIT: Base CNY: $cnyAmount → USD: $usdAmount, HKD: $hkdAmount");
          break;
        case 'USD':
          usdAmount = parsedAmount;
          cnyAmount = parsedAmount * rates['USD_CNY']!;
          hkdAmount = parsedAmount * rates['USD_HKD']!;
          print("CUBIT: Base USD: $usdAmount → CNY: $cnyAmount, HKD: $hkdAmount");
          break;
        case 'HKD':
          hkdAmount = parsedAmount;
          cnyAmount = parsedAmount * rates['HKD_CNY']!;
          usdAmount = parsedAmount * rates['HKD_USD']!;
          print("CUBIT: Base HKD: $hkdAmount → CNY: $cnyAmount, USD: $usdAmount");
          break;
        default:
          // Default to CNY
          cnyAmount = parsedAmount;
          usdAmount = parsedAmount * rates['CNY_USD']!;
          hkdAmount = parsedAmount * rates['CNY_HKD']!;
          print("CUBIT: Default to CNY: $cnyAmount → USD: $usdAmount, HKD: $hkdAmount");
      }

      emit(state.copyWith(
        convertedUSD: usdAmount,
        convertedCNY: cnyAmount,
        convertedHKD: hkdAmount,
        isCalculating: false,
      ));

      print("CUBIT: State updated with USD: $usdAmount, CNY: $cnyAmount, HKD: $hkdAmount");
    } catch (e) {
      print("CUBIT: Error in calculation: $e");
      emit(state.copyWith(
        isCalculating: false,
      ));
    }
  }

  // Parse amount string to double
  double _parseAmount(String amount) {
    try {
      // Check if the amount contains math operators
      bool containsMathOperations = amount.contains('+') ||
          amount.contains('-') ||
          amount.contains('×') ||
          amount.contains('÷') ||
          amount.contains('*') ||
          amount.contains('/');

      print("CUBIT: _parseAmount($amount), contains math operations: $containsMathOperations");

      // Let the keyboard handle math expressions
      if (containsMathOperations) {
        print("CUBIT: Math expression detected, returning 0");
        return 0.0;
      }

      // Special case for decimal point at end or empty
      if (amount.endsWith('.')) {
        amount = amount.substring(0, amount.length - 1);
      }
      if (amount.isEmpty) {
        return 0.0;
      }

      // For regular numbers, parse directly
      final cleanAmount = amount.replaceAll(RegExp(r'[^0-9.]'), '');
      double result = cleanAmount.isEmpty ? 0.0 : double.parse(cleanAmount);
      print("CUBIT: Parsed amount $amount to $result");
      return result;
    } catch (e) {
      print("CUBIT: Error parsing amount $amount: $e");
      return 0.0;
    }
  }
}
