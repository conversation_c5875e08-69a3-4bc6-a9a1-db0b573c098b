import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/account/account.dart';
import 'package:gp_stock_app/core/models/entities/wallet/alipay_account_entity.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/gift_type.dart';
import 'package:gp_stock_app/features/profile/widgets/account_selection_bottom_sheet.dart';
import 'package:gp_stock_app/shared/app/extension/string_extension.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_input_formatter/no_leading_zero_int_formatter.dart';

import 'alipay_deposit_cubit.dart';
import 'alipay_deposit_state.dart';

class AlipayDepositView extends StatefulWidget {
  const AlipayDepositView({super.key});

  @override
  State<AlipayDepositView> createState() => _AlipayDepositViewState();
}

class _AlipayDepositViewState extends State<AlipayDepositView> {
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _orderNumberController = TextEditingController();

  @override
  void dispose() {
    _amountController.dispose();
    _orderNumberController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _setupControllerListeners();
  }

  void _setupControllerListeners() {
    _amountController.addListener(() {
      context.read<AlipayDepositCubit>().updateDepositAmount(_amountController.text);
    });
    _orderNumberController.addListener(() {
      context.read<AlipayDepositCubit>().updateOrderNumber(_orderNumberController.text);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AlipayDepositCubit, AlipayDepositState>(
      listenWhen: (previous, current) => previous.submitStatus != current.submitStatus,
      listener: (context, state) {
        if (state.submitStatus == DataStatus.success) {
          GPEasyLoading.showToast('depositedSuccessfully'.tr());
          context.read<AlipayDepositCubit>().resetForm();
          _clearControllers();
        } else if (state.submitStatus == DataStatus.failed) {
          GPEasyLoading.showToast('depositFailed'.tr());
        }
      },
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 16,
              children: [
                _buildAmountSection(context),
                _buildAlipayAccountSection(context),
                _buildTransferNumberInput(),
                SizedBox(height: 100.gw), // Space for bottom navigation bar
              ],
            ),
          ),
        ),
        bottomNavigationBar: Container(
          padding: EdgeInsets.all(16.gw),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            spacing: 16.gw,
            children: [
              _buildSubmitButton(context),
              SupportWidget(),
              SizedBox(height: 16.gw),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAlipayAccountSection(BuildContext context) {
    return BlocSelector<AlipayDepositCubit, AlipayDepositState, AlipayAccountEntity?>(
      selector: (state) => state.selectedAlipayAccount,
      builder: (context, state) {
        final cashbackAmount =
            ((state?.constantGiveRate ?? 0) / 100) * (double.tryParse(_amountController.text) ?? 0.00);
        return GestureDetector(
          onTap: () {
            _showAccountSelectionBottomSheet(context);
          },
          child: ShadowBox(
            borderRadius: BorderRadius.circular(8.gr),
            padding: EdgeInsets.all(16.gw),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'receiver'.tr(),
                  style: context.textTheme.title.w600,
                ),
                SizedBox(height: 8.gw),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.gr),
                    color: context.theme.inputDecorationTheme.fillColor,
                  ),
                  padding: EdgeInsets.symmetric(vertical: 10.gw, horizontal: 12.gw),
                  child: Row(
                    spacing: 20.gw,
                    children: [
                      Expanded(
                          child:
                              Text(state?.channelName ?? 'selectPaymentMethod'.tr(), style: context.textTheme.title)),
                      Icon(
                        Icons.keyboard_arrow_down_sharp,
                        size: 24.gw,
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 8.gw),
                Wrap(
                  children: [
                    Text.rich(
                      TextSpan(
                        text: GiveGiftType.fromType(state?.giveGiftType ?? 1).label.tr(),
                        style: context.textTheme.regular.fs12.copyWith(
                          color: context.colorTheme.textRegular,
                        ),
                        children: [
                          TextSpan(
                            text: ' ${cashbackAmount.toStringAsFixed(2)} 元',
                            style: context.textTheme.primary.fs12.w600.copyWith(
                              color: Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.gw),
                _infoRow(
                    'receiver'.tr(),
                    (state?.ownerName.isNotNullNorEmpty ?? false)
                        ? state?.ownerName ?? ''
                        : 'pleaseContactCustomerService'.tr()),
                Divider(color: context.theme.hintColor, thickness: 0.5),
                _infoRow(
                    'alipayAccount'.tr(),
                    (state?.bankAccount.isNotNullNorEmpty ?? false)
                        ? state?.bankAccount ?? ''
                        : 'pleaseContactCustomerService'.tr()),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTransferNumberInput() {
    return ShadowBox(
      borderRadius: BorderRadius.circular(8.gr),
      padding: EdgeInsets.all(16.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('transferor'.tr(), style: context.textTheme.regular.w600),
          SizedBox(height: 8),
          TextFieldWidget(
            hintText: 'pleaseEnterYourNameForTransfer'.tr(),
            borderRadius: 8.gr,
            contentPadding: EdgeInsets.symmetric(vertical: 10.gw, horizontal: 12.gw),
            controller: _orderNumberController,
          ),
        ],
      ),
    );
  }

  Widget _infoRow(String title, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 0.35.gsw,
            child: Text(
              title,
              style: context.textTheme.regular.copyWith(
                color: context.colorTheme.textRegular,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: context.textTheme.regular.copyWith(
                color: context.theme.hintColor,
              ),
              softWrap: true,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountSection(BuildContext context) {
    return BlocBuilder<AlipayDepositCubit, AlipayDepositState>(
      builder: (context, state) {
        return ShadowBox(
          borderRadius: BorderRadius.circular(8.gr),
          padding: EdgeInsets.all(16.gw),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 12.gw,
            children: [
              Text('enterRechargeAmount'.tr(), style: context.textTheme.title.w600),
              _buildAmountTextField(context, state),
              BlocSelector<UserCubit, UserState, AccountInfo?>(
                selector: (state) => state.accountInfo,
                builder: (context, accountInfo) {
                  return Column(
                    children: [
                      Row(
                        spacing: 8,
                        children: [
                          Text(
                            'balance'.tr(),
                            style: context.textTheme.regular.fs13,
                          ),
                          FlipText(
                            accountInfo?.usableCash ?? 0,
                          )
                        ],
                      ),
                    ],
                  );
                },
              ),
              if (state.depositConfigEntity != null)
                Text(
                  '* ${'minimumAmount'.tr()} ${state.selectedAlipayAccount?.minAmount} ${'maximumAmount'.tr()} ${state.selectedAlipayAccount?.maxAmount}',
                  style: context.textTheme.title.copyWith(
                    color: Color(0xFFE06822),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAmountTextField(BuildContext context, AlipayDepositState state) {
    return TextFieldWidget(
      controller: _amountController,
      hintText: 'pleaseEnterAmount'.tr(),
      textInputType: TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [NoLeadingZeroIntFormatter()],
      borderType: TextFieldBorderType.outline,
      contentPadding: EdgeInsets.symmetric(vertical: 10.gw, horizontal: 12.gw),
      borderRadius: 8.gr,
      errorText: !state.isAmountValid ? 'amount_exceeds_limit_range'.tr() : null,
    );
  }

  Widget _buildSubmitButton(BuildContext context) {
    return BlocBuilder<AlipayDepositCubit, AlipayDepositState>(
      builder: (context, state) {
        return CommonButton(
          title: 'submit'.tr(),
          enable: state.canSubmit,
          showLoading: state.submitStatus == DataStatus.loading,
          onPressed: state.canSubmit ? () => _handleSubmit(context) : null,
        );
      },
    );
  }

  void _handleSubmit(BuildContext context) {
    context.read<AlipayDepositCubit>().submitDeposit();
  }

  void _clearControllers() {
    _amountController.clear();
    _orderNumberController.clear();
  }

  void _showAccountSelectionBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => BlocProvider.value(
        value: context.read<AlipayDepositCubit>(),
        child: AccountSelectionBottomSheet(
          list: _buildAccountList(context),
        ),
      ),
    );
  }

  Widget _buildAccountList(BuildContext context) {
    return BlocBuilder<AlipayDepositCubit, AlipayDepositState>(
      builder: (context, state) {
        if (state.alipayAccountStatus == DataStatus.loading) {
          return Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                context.theme.primaryColor,
              ),
            ),
          );
        }

        if (state.alipayAccounts.isEmpty) {
          return Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 24.gw),
              child: Text('no_data_available'.tr()),
            ),
          );
        }

        return ListView.separated(
          shrinkWrap: true,
          padding: EdgeInsets.symmetric(horizontal: 10.gw),
          itemCount: state.alipayAccounts.length,
          separatorBuilder: (context, index) => Divider(
            height: 1,
            color: context.colorTheme.border,
            thickness: 0.5,
          ),
          itemBuilder: (context, index) {
            final account = state.alipayAccounts[index];
            final isSelected = state.selectedAlipayAccount?.id == account.id;

            return _buildAccountItem(
              context,
              account: account,
              isSelected: isSelected,
              onTap: () {
                Navigator.of(context).pop();
                context.read<AlipayDepositCubit>().updateSelectedAlipayAccount(account);
              },
            );
          },
        );
      },
    );
  }

  Widget _buildAccountItem(
    BuildContext context, {
    required AlipayAccountEntity account,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final width = (1.gsw / 3) - (10.gw * 2);
    final style = context.textTheme.title;
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.gw),
        child: Row(
          children: [
            SizedBox(
              width: width,
              child: Text('alipayAccount'.tr(), style: style, textAlign: TextAlign.left),
            ),
            Expanded(
              child: Text(account.channelName, style: style, textAlign: TextAlign.center),
            ),
            Container(
              width: width,
              alignment: Alignment.centerRight,
              child: Container(
                width: 18.gw,
                height: 18.gw,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected ? context.theme.primaryColor : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? context.theme.primaryColor : context.colorTheme.border,
                    width: 1,
                  ),
                ),
                child: isSelected ? Icon(Icons.check, size: 10.gw, color: context.textTheme.secondary.color) : null,
              ),
            ),
            SizedBox(width: 15.gw),
          ],
        ),
      ),
    );
  }
}
