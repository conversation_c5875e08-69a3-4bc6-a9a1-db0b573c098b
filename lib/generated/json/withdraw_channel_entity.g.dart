import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/withdraw_channel_entity.dart';

WithdrawChannelListEntity $WithdrawChannelListEntityFromJson(Map<String, dynamic> json) {
  final WithdrawChannelListEntity withdrawChannelListEntity = WithdrawChannelListEntity();
  final List<WithdrawChannel>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<WithdrawChannel>(e) as WithdrawChannel).toList();
  if (list != null) {
    withdrawChannelListEntity.list = list;
  }
  return withdrawChannelListEntity;
}

Map<String, dynamic> $WithdrawChannelListEntityToJson(WithdrawChannelListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension WithdrawChannelListEntityExtension on WithdrawChannelListEntity {
  WithdrawChannelListEntity copyWith({
    List<WithdrawChannel>? list,
  }) {
    return WithdrawChannelListEntity()
      ..list = list ?? this.list;
  }
}

WithdrawChannel $WithdrawChannelFromJson(Map<String, dynamic> json) {
  final WithdrawChannel withdrawChannel = WithdrawChannel();
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    withdrawChannel.currency = currency;
  }
  final double? exchangeRate = jsonConvert.convert<double>(json['exchangeRate']);
  if (exchangeRate != null) {
    withdrawChannel.exchangeRate = exchangeRate;
  }
  final int? handlingFeeType = jsonConvert.convert<int>(json['handlingFeeType']);
  if (handlingFeeType != null) {
    withdrawChannel.handlingFeeType = handlingFeeType;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    withdrawChannel.id = id;
  }
  final int? maxWithdrawalAmount = jsonConvert.convert<int>(json['maxWithdrawalAmount']);
  if (maxWithdrawalAmount != null) {
    withdrawChannel.maxWithdrawalAmount = maxWithdrawalAmount;
  }
  final int? minWithdrawalAmount = jsonConvert.convert<int>(json['minWithdrawalAmount']);
  if (minWithdrawalAmount != null) {
    withdrawChannel.minWithdrawalAmount = minWithdrawalAmount;
  }
  final String? netWorkName = jsonConvert.convert<String>(json['netWorkName']);
  if (netWorkName != null) {
    withdrawChannel.netWorkName = netWorkName;
  }
  final int? network = jsonConvert.convert<int>(json['network']);
  if (network != null) {
    withdrawChannel.network = network;
  }
  final String? withdrawalAmountOptions = jsonConvert.convert<String>(json['withdrawalAmountOptions']);
  if (withdrawalAmountOptions != null) {
    withdrawChannel.withdrawalAmountOptions = withdrawalAmountOptions;
  }
  final List<String>? withdrawalAmountOptionsList = (json['withdrawalAmountOptionsList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (withdrawalAmountOptionsList != null) {
    withdrawChannel.withdrawalAmountOptionsList = withdrawalAmountOptionsList;
  }
  final int? withdrawalFee = jsonConvert.convert<int>(json['withdrawalFee']);
  if (withdrawalFee != null) {
    withdrawChannel.withdrawalFee = withdrawalFee;
  }
  return withdrawChannel;
}

Map<String, dynamic> $WithdrawChannelToJson(WithdrawChannel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currency'] = entity.currency;
  data['exchangeRate'] = entity.exchangeRate;
  data['handlingFeeType'] = entity.handlingFeeType;
  data['id'] = entity.id;
  data['maxWithdrawalAmount'] = entity.maxWithdrawalAmount;
  data['minWithdrawalAmount'] = entity.minWithdrawalAmount;
  data['netWorkName'] = entity.netWorkName;
  data['network'] = entity.network;
  data['withdrawalAmountOptions'] = entity.withdrawalAmountOptions;
  data['withdrawalAmountOptionsList'] = entity.withdrawalAmountOptionsList;
  data['withdrawalFee'] = entity.withdrawalFee;
  return data;
}

extension WithdrawChannelExtension on WithdrawChannel {
  WithdrawChannel copyWith({
    String? currency,
    double? exchangeRate,
    int? handlingFeeType,
    int? id,
    int? maxWithdrawalAmount,
    int? minWithdrawalAmount,
    String? netWorkName,
    int? network,
    String? withdrawalAmountOptions,
    List<String>? withdrawalAmountOptionsList,
    int? withdrawalFee,
  }) {
    return WithdrawChannel()
      ..currency = currency ?? this.currency
      ..exchangeRate = exchangeRate ?? this.exchangeRate
      ..handlingFeeType = handlingFeeType ?? this.handlingFeeType
      ..id = id ?? this.id
      ..maxWithdrawalAmount = maxWithdrawalAmount ?? this.maxWithdrawalAmount
      ..minWithdrawalAmount = minWithdrawalAmount ?? this.minWithdrawalAmount
      ..netWorkName = netWorkName ?? this.netWorkName
      ..network = network ?? this.network
      ..withdrawalAmountOptions = withdrawalAmountOptions ?? this.withdrawalAmountOptions
      ..withdrawalAmountOptionsList = withdrawalAmountOptionsList ?? this.withdrawalAmountOptionsList
      ..withdrawalFee = withdrawalFee ?? this.withdrawalFee;
  }
}