import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/bank_entity_list.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/bank_entity_list.g.dart';

@JsonSerializable()
class BankEntityList {
  final List<BankEntity> list;

  const BankEntityList({
    this.list = const [],
  });

  factory BankEntityList.fromJson(Map<String, dynamic> json) => $BankEntityListFromJson(json);

  Map<String, dynamic> toJson() => $BankEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class BankEntity {
  final String bankCode;
  final String bankFullName;
  final String bankName;
  final int bankRegion;
  final String icon;
  final int id;

  const BankEntity({
    this.bankCode = '',
    this.bankFullName = '',
    this.bankName = '',
    this.bankRegion = 0,
    this.icon = '',
    this.id = 0,
  });

  factory BankEntity.fromJson(Map<String, dynamic> json) => $BankEntityFromJson(json);

  Map<String, dynamic> toJson() => $BankEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}