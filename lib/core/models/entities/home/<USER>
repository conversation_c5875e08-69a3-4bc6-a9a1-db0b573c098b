import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/company_news_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/company_news_entity.g.dart';

@JsonSerializable()
class CompanyNewsEntity {
  @JSONField(name: 'article_title')
  final String articleTitle;
  @JSONField(name: 'article_market')
  final String articleMarket;
  @JSONField(name: 'article_symbol')
  final String articleSymbol;
  @JSONField(name: 'article_auth')
  final String articleAuth;
  @JSONField(name: 'article_content')
  final String articleContent;
  final String id;
  @JSONField(name: 'article_date')
  final String articleDate;

  const CompanyNewsEntity({
    this.articleTitle = '',
    this.articleMarket = '',
    this.articleSymbol = '',
    this.articleAuth = '',
    this.articleContent = '',
    this.id = '',
    this.articleDate = '',
  });

  factory CompanyNewsEntity.fromJson(Map<String, dynamic> json) => $CompanyNewsEntityFromJson(json);

  Map<String, dynamic> toJson() => $CompanyNewsEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
