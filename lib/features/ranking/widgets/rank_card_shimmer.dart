import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class RankCardShimmer extends StatelessWidget {
  const RankCardShimmer({
    super.key,
    this.topMargin,
  });

  final double? topMargin;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10.gw, 38.gw, 10.gw, 10.gw),
      margin: EdgeInsets.only(left: 5.gw, right: 5.gw, top: topMargin ?? 0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: context.theme.cardColor,
        border: Border.all(color: context.theme.dividerColor),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.center,
        children: [
          Column(
            spacing: 6.gw,
            children: [
              // Rank Icon placeholder
              ShimmerWidget(
                height: 35.gw,
                width: 35.gw,
                radius: 35.gw / 2,
              ),
              // Nickname placeholder
              ShimmerWidget(
                height: 16.gw,
                width: 80.gw,
                radius: 4,
              ),
              // Short text placeholder
              ShimmerWidget(
                height: 12.gw,
                width: 60.gw,
                radius: 4,
              ),
              // Percentage container placeholder
              ShimmerWidget(
                height: 24.gw,
                width: 70.gw,
                radius: 7.gr,
              ),
              // Amount placeholder
              ShimmerWidget(
                height: 12.gw,
                width: 90.gw,
                radius: 4,
              ),
            ],
          ),
          // Avatar placeholder
          Positioned(
            top: -70,
            child: ShimmerWidget(
              width: 50.gw,
              height: 50.gw,
              radius: 50.gw / 2,
            ),
          ),
        ],
      ),
    );
  }
}
