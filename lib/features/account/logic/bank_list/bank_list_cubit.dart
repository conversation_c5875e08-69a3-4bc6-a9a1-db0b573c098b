import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/models/apis/wallet.dart';
import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class BankListCubit extends Cubit<BankListState> {
  BankListCubit() : super(BankListState()) {
    fetchBankList();
  }

  void fetchBankList() async {
    emit(state.copyWith(bankListFetchStatus: DataStatus.loading));
    final result = await WalletApi.getBankList();
    emit(state.copyWith(bankList: result, bankListFetchStatus: DataStatus.success));
  }
}
