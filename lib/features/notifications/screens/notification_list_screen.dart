import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import '../../../shared/constants/enums.dart';
import '../logic/notifications_cubit.dart';
import '../widgets/notification_cell.dart';

class NotificationListScreen extends StatefulWidget {
  const NotificationListScreen({super.key});

  @override
  State<NotificationListScreen> createState() => _NotificationListScreenState();
}

class _NotificationListScreenState extends State<NotificationListScreen> {
  int _selectedTabIndex = 0;
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _fetchNotifications();
    _setupScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _setupScrollController() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8 && !_isLoadingMore) {
        _loadMoreData();
      }
    });
  }

  Future<void> _loadMoreData() async {
    final state = context.read<NotificationsCubit>().state;
    final hasMorePages = (state.notificationData?.current ?? 0) < (state.notificationData?.pages ?? 0);

    if (hasMorePages && !_isLoadingMore) {
      setState(() => _isLoadingMore = true);

      await context.read<NotificationsCubit>().getNotificationList(
            messageType: _getMessageType(),
            isLoadMore: true,
          );

      setState(() => _isLoadingMore = false);
    }
  }

  void _fetchNotifications() {
    final type = _getMessageType();
    context.read<NotificationsCubit>()
      ..getNotificationList(messageType: type)
      ..getNotificationCount();
  }

  String _getMessageType() {
    switch (_selectedTabIndex) {
      case 0:
        return NotificationType.system.name;
      case 1:
        return NotificationType.warning.name;
      default:
        return NotificationType.system.name;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('myNotifications'.tr()),
        surfaceTintColor: Colors.transparent,
        actions: [
          TextButton(
            onPressed: () => context.read<NotificationsCubit>().readAllNotifications(),
            child: Text('readAll'.tr()),
          )
        ],
      ),
      body: Column(
        children: [
          _buildTabs(),
          Expanded(
            child: RefreshIndicator.adaptive(
              backgroundColor: context.theme.cardColor,
              onRefresh: () async {
                _fetchNotifications();
              },
              child: _buildRegularNotificationsTab(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRegularNotificationsTab() {
    return BlocBuilder<NotificationsCubit, NotificationsState>(
      builder: (context, state) {
        if (state.notificationFetchStatus == DataStatus.loading && !_isLoadingMore) {
          return _loadingState();
        }
        return _successState(state);
      },
    );
  }

  Widget _buildTabs() {
    final tabData = [
      'systemMessage'.tr(),
      'warningMessage'.tr(),
    ];

    return Container(
      padding: EdgeInsets.only(bottom: 4.gw, top: 4.gw),
      child: CommonTabBar.withAutoKey(
        tabData,
        currentIndex: _selectedTabIndex,
        onTap: (index) {
          setState(() => _selectedTabIndex = index);
          _fetchNotifications();
        },
        style: CommonTabBarStyle.line,
        isScrollable: false,
      ),
    );
  }

  Widget _successState(NotificationsState state) {
    final records = state.notificationData?.records ?? [];
    if (records.isEmpty) {
      return Center(
        child: TableEmptyWidget(
          width: 60.gw,
          height: 60.gw,
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: records.length + (_isLoadingMore ? 1 : 0),
      padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 8.gw),
      itemBuilder: (context, index) {
        if (index == records.length) {
          return Padding(
            padding: EdgeInsets.all(8.gr),
            child: Center(
              child: CircularProgressIndicator(
                color: context.colorTheme.buttonPrimary,
              ),
            ),
          );
        }

        final record = records[index];

        return NotificationCell(
          isRead: record.status == 1,
          icon: _getIcon(record.type),
          title: record.title,
          subtitle: record.content,
          createTime: record.createTime,
          color: _getColor(record.type, context),
          onTap: () async {
            await context.read<NotificationsCubit>().readNotification(messageId: record.id);
            if (!context.mounted) return;
            context.read<NotificationsCubit>().getNotificationCount();
          },
        );
      },
    );
  }
}

IconData _getIcon(String type) {
  switch (type) {
    case 'system':
      return Icons.notifications;
    case 'announcement':
      return Icons.campaign;
    case 'forecast':
      return Icons.timeline;
    default:
      return Icons.notifications;
  }
}

Color _getColor(String type, BuildContext context) {
  switch (type) {
    case 'system':
      return context.colorTheme.buttonPrimary.withValues(alpha: 0.3);
    case 'announcement':
      return context.colorTheme.pending.withValues(alpha: 0.3);
    case 'forecast':
      return context.colorTheme.stockGreen.withValues(alpha: 0.3);
    default:
      return context.colorTheme.textRegular.withValues(alpha: 0.3);
  }
}

Widget _loadingState() {
  return ListView.separated(
    itemCount: 6,
    shrinkWrap: true,
    padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 8.gw),
    separatorBuilder: (_, __) => 10.verticalSpace,
    itemBuilder: (_, __) => ShimmerWidget(
      height: 60.gw,
      radius: 12.gr,
    ),
  );
}
