import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/features/account_v2/0_home/account_screen_state_v2.dart';
import 'package:gp_stock_app/features/account_v2/contract/account_contract_screen.dart' show AccountContractScreen;
import 'package:gp_stock_app/features/account_v2/spot/account_spot_screen.dart';
import 'package:gp_stock_app/my_app.dart' show routeObserver;
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import 'account_screen_cubit_v2.dart';

class AccountScreenV2 extends StatefulWidget {
  const AccountScreenV2({super.key});

  @override
  State<StatefulWidget> createState() => _AccountScreenV2State();
}

class _AccountScreenV2State extends State<AccountScreenV2> with RouteAware {
  final pageController = PageController();
  int currentIndex = 0;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AccountScreenCubitV2>().fetchSpotScreenCurrentData();
    });
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route != null) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void didPopNext() {
    // 从其他页面返回时触发刷新（如从交易页面返回）
    // 只在现货 tab 选中时刷新
    final cubit = context.read<AccountScreenCubitV2>();
    final state = cubit.state;
    if (state.tradingTabBarCurrentIndex == state.accountTabBarList.indexOf(TradingAccountType.Spot)) {
      cubit.fetchSpotScreenCurrentData(forceRefresh: true);
    }
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AccountScreenCubitV2>();
    return BlocBuilder<AccountScreenCubitV2, AccountScreenStateV2>(
      // buildWhen: (previous, current) => previous.tradingTabBarCurrentIndex != current.tradingTabBarCurrentIndex,
      builder: (context, state) {
        return Column(
          children: [
            4.verticalSpace,
            getTabBar(
              context,
              tabs: state.accountTabBarList.map((e) => e.nameKey.tr()).toList(),
              selectedIndex: state.tradingTabBarCurrentIndex,
              onTabSelected: (index) => cubit.updateAccountTabBarIndex(index),
            ),
            Expanded(
              child: DirectSlideView(
                pageIndex: state.tradingTabBarCurrentIndex,
                physics: const NeverScrollableScrollPhysics(),
                pages: state.accountTabBarList.map((type) => _getAccountTypeScreenBy(type)).toList(),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _getAccountTypeScreenBy(TradingAccountType type) {
    return switch (type) {
      TradingAccountType.Spot => AccountSpotScreen(), // 现货
      TradingAccountType.Contract => AccountContractScreen(), // 合约
    };
  }

  Widget getTabBar(
    BuildContext context, {
    required List<String> tabs,
    required int selectedIndex,
    required Function(int) onTabSelected,
  }) {
    // Get style configuration based on skin
    final (style, backgroundColor) = switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateD => (CommonTabBarStyle.trapezoid, context.theme.cardColor),
      AppSkinStyle.kTemplateB || AppSkinStyle.kTemplateC => (CommonTabBarStyle.rectangular, context.theme.cardColor),
      AppSkinStyle.kTemplateA || AppSkinStyle.kGP || AppSkinStyle.kZangGolden => (CommonTabBarStyle.round, null),
    };

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 4.gw),
      child: CommonTabBar.withAutoKey(
        tabs,
        currentIndex: selectedIndex,
        onTap: onTabSelected,
        style: style,
        isScrollable: false,
        backgroundColor: backgroundColor,
      ),
    );
  }
}
