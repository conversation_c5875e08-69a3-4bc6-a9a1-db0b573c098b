import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

/// 新闻单元格组件（通用样式）
class HomeNewsCell extends StatelessWidget {
  final NewsRecords news;

  const HomeNewsCell({
    super.key,
    required this.news,
  });

  @override
  Widget build(BuildContext context) {
    final isYhxt = AppConfig.instance.flavor == Flavor.yhxt;
    final textBeforeImage =
        isYhxt || AppConfig.instance.skinStyle == AppSkinStyle.kTemplateC;

    // kTemplateC: text-left, image-right
    if (textBeforeImage) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(child: _buildContent(context)),
          12.horizontalSpace,
          Hero(
            tag: 'news_${news.id}',
            child: _buildImage(),
          ),
        ],
      );
    }

    // 其他样式: image-left, text-right
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Hero(
          tag: 'news_${news.id}',
          child: _buildImage(),
        ),
        12.horizontalSpace,
        Expanded(child: _buildContent(context)),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    var titleStyle = context.textTheme.title;
    var sourceStyle = context.textTheme.secondary.fs12;
    var timeStyle = context.textTheme.secondary.fs12;

    switch (AppConfig.instance.flavor) {
      case Flavor.yhxt:
        titleStyle = const TextStyle(color: Colors.black, fontSize: 14);
        sourceStyle = const TextStyle(color: Color(0xff0052FF), fontSize: 12);
        timeStyle = const TextStyle(color: Color(0xff525A79), fontSize: 12);
        break;
      default:
        break;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Text(
          news.title,
          style: titleStyle,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: 8.gw),
        // 来源和时间
        Row(
          spacing: 10.gw,
          children: [
            if (news.comeFrom.isNotEmpty) ...[
              Text(
                news.comeFrom,
                style: sourceStyle,
              ),
            ],
            Text(
              _formatTime(news.publishTime),
              style: timeStyle,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildImage() {
    final imageUrl = news.url.isNotEmpty ? news.url : news.bigUrl;
    if (imageUrl.isEmpty) {
      return SizedBox(
        width: 120.gw,
        height: 80.gw,
      );
    }

    return IconHelper.loadAsset(
      imageUrl,
      width: 120.gw,
      height: 80.gw,
      fit: BoxFit.cover,
      radius: 8.gw,
      placeholder: _buildPlaceholderBox(),
      errorWidget: _buildPlaceholderBox(showIcon: true),
    );
  }

  Widget _buildPlaceholderBox({bool showIcon = false}) {
    return Container(
      width: 120.gw,
      height: 80.gw,
      color: Colors.grey[200],
      child: showIcon
          ? const Icon(
              Icons.image_not_supported,
              color: Colors.grey,
            )
          : null,
    );
  }

  String _formatTime(String publishTime) {
    if (publishTime.isEmpty) return '';
    if (publishTime.contains(' ')) {
      return publishTime.split(' ').first;
    }
    return publishTime;
  }
}
