import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'contract_settle_history_cubit.dart';

class ContractSettleHistoryScreen extends StatefulWidget {
  const ContractSettleHistoryScreen({super.key});

  @override
  State<ContractSettleHistoryScreen> createState() => _ContractSettleHistoryScreenState();
}

class _ContractSettleHistoryScreenState extends State<ContractSettleHistoryScreen> {
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  Future<void> _onRefresh() async {
    await context.read<ContractSettleHistoryCubit>().fetchSettleHistory();
    _refreshController.refreshCompleted();
  }

  Future<void> _onLoading() async {
    await context.read<ContractSettleHistoryCubit>().fetchSettleHistory(isLoadMore: true);

    if (!mounted) return;

    final state = context.read<ContractSettleHistoryCubit>().state;
    if (state.hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('historicalMessages'.tr()),
        surfaceTintColor: Colors.transparent,
      ),
      body: BlocBuilder<ContractSettleHistoryCubit, ContractSettleHistoryState>(
        builder: (context, state) {
          if (state.fetchStatus == DataStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          }

          final records = state.records;
          if (records.isEmpty || state.fetchStatus == DataStatus.failed) {
            return CommonRefresher(
              controller: _refreshController,
              enablePullDown: true,
              onRefresh: _onRefresh,
              child: _buildEmptyState(),
            );
          }

          return CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: state.hasMore,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            child: AnimationLimiter(
              child: ListView.builder(
                padding: EdgeInsets.symmetric(horizontal: 10.gr, vertical: 10.gr),
                itemCount: records.length,
                itemBuilder: (context, index) {
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 600),
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: Padding(
                          padding: EdgeInsets.only(bottom: 10.gr),
                          child: _ContractRecordItem(record: records[index]),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        SizedBox(
          height: 0.7.gsh,
          child: Center(
            child: TableEmptyWidget(
              height: 60.gw,
              width: 60.gw,
            ),
          ),
        ),
      ],
    );
  }
}

class _ContractRecordItem extends StatelessWidget {
  final Contract record;

  const _ContractRecordItem({required this.record});

  @override
  Widget build(BuildContext context) {
    return AnimationConfiguration.synchronized(
      duration: const Duration(milliseconds: 300),
      child: ScaleAnimation(
        scale: 0.95,
        child: ShadowBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(record.label, style: context.textTheme.primary.fs12.w700),
                  GestureDetector(
                    onTap: () => getIt<NavigatorService>().push(
                      AppRouter.routeOrderHistory,
                      arguments: {'contractId': record.id, 'isHistory': true},
                    ),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 4.gw),
                      decoration: BoxDecoration(
                        color: context.theme.primaryColor,
                        borderRadius: BorderRadius.circular(6.gr),
                      ),
                      child: Text(
                        'transactionDetails'.tr(),
                        style: context.textTheme.secondary.fs12.w600,
                      ),
                    ),
                  ),
                ],
              ),
              8.verticalSpace,
              _buildDataFields(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDataFields(BuildContext context) {
    return Column(
      children: [
        _DataField(
          label: 'date'.tr(),
          value: _formatDateRange(record.openTime, record.expireTime),
        ),
        _DataField(
          label: 'initialMargin'.tr(),
          value: record.initCash.toString(),
        ),
        _DataField(
          label: 'totalOperatingFunds'.tr(),
          value: record.totalPower.toString(),
        ),
        _DataField(
          label: 'expandedMargin'.tr(),
          value: record.expendAmount.toString(),
        ),
        _DataField(
          label: 'supplementLoss'.tr(),
          value: record.coverLossAmount.toString(),
        ),
        _DataField(
          label: 'totalProfitAndLoss'.tr(),
          value: record.winAmount.toString(),
        ),
      ],
    );
  }

  String _formatDateRange(String? startDate, String? endDate) {
    final start = (startDate ?? '0').split(' ')[0];
    final end = (endDate ?? '0').split(' ')[0];
    return '$start - $end';
  }
}

class _DataField extends StatelessWidget {
  final String label;
  final String value;

  const _DataField({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 3.gr),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: context.textTheme.primary.fs13,
          ),
          Text(
            value,
            style: context.textTheme.regular.w600.ffAkz,
          ),
        ],
      ),
    );
  }
}
