import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/models/entities/market/index_stock_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market/stock_screen.dart';
import 'package:gp_stock_app/features/market/watch_list/screens/watch_list_screen.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/index_trading/index_trading_page.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown_v2.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import '../../shared/mixin/animation.dart';
import 'logic/market/market_cubit.dart';

class MarketSectionScreen extends StatefulWidget {
  final bool showBackButton;
  final ScrollController? scrollController;

  const MarketSectionScreen({
    super.key,
    this.showBackButton = false,
    this.scrollController,
  });

  @override
  State<MarketSectionScreen> createState() => _MarketSectionScreenState();
}

class _MarketSectionScreenState extends State<MarketSectionScreen>
    with StaggeredAnimation, AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  List<String> tabs = [];
  TradingCenterV2Cubit? _indexTradingCubit;

  @override
  void initState() {
    super.initState();
    TradingMode currentMode = AppConfig.instance.tradingMode;
    final sysSettingsState = context
        .read<SysSettingsCubit>()
        .state;

    sysSettingsState.maybeWhen(
      loaded: (_, config) {
        currentMode = TradingModeExtension.fromIndex(config.tradingMode);
      },
      orElse: () {},
    );

    switch (currentMode) {
      case TradingMode.stock:
        tabs = ['stocks', 'stockIndex', 'watchList'];
        break;
      case TradingMode.futures:
        tabs = ['cnFutures', "globalFutures", 'watchList'];
        break;
      case TradingMode.stockAndFutures:
        tabs = ['stocks', 'stockIndex', 'cnFutures', "globalFutures", 'watchList'];
        break;
    }
  }

  @override
  void dispose() {
    _indexTradingCubit?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: Column(
        children: [
          BlocSelector<MarketCubit, MarketState, MarketSectionTab>(
            selector: (state) => state.selectedMarketSectionTab,
            builder: (context, state) {
              return Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gw),
                child: CommonTabBar.withAutoKey(
                  tabs.map((tab) => tab.tr()).toList(),
                  style: switch (AppConfig.instance.skinStyle) {
                    AppSkinStyle.kGP || AppSkinStyle.kTemplateA => CommonTabBarStyle.round,
                    AppSkinStyle.kTemplateD => CommonTabBarStyle.trapezoid,
                    _ => CommonTabBarStyle.rectangular,
                  },
                  currentIndex: state.index,
                  onTap: (i) => context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.values[i]),
                  padding: EdgeInsets.all(2.gw),
                  backgroundColor: context.theme.cardColor,
                  radius: 8.gr,
                  isScrollable: false,
                  labelPadding: EdgeInsets.symmetric(horizontal: 5.gw),
                ),
              );
            },
          ),
          Expanded(
            child: BlocSelector<MarketCubit, MarketState, MarketSectionTab>(
                selector: (state) => state.selectedMarketSectionTab,
                builder: (context, state) =>
                switch (state) {
                  MarketSectionTab.stocks =>
                      StockScreen(
                        showBackButton: true,
                        scrollController: widget.scrollController,
                      ),
                  MarketSectionTab.stockIndex => _buildIndexTradingTab(),
                  MarketSectionTab.cnFutures =>
                      BlocProvider<CNFuturesListCubit>(
                        key: const ValueKey('market_cn_futures'),
                        create: (_) => CNFuturesListCubit(FTradeListRepository(), showInHomePage: false),
                        child: const FTradeListScreen(scope: FuturesScope.cn, showInHomePage: false),
                      ),
                  MarketSectionTab.globalFutures =>
                      BlocProvider<GlobalFuturesListCubit>(
                        key: const ValueKey('market_global_futures'),
                        create: (_) => GlobalFuturesListCubit(FTradeListRepository(), showInHomePage: false),
                        child: const FTradeListScreen(scope: FuturesScope.global, showInHomePage: false),
                      ),
                  MarketSectionTab.watchList => WatchListScreen(),
                }),
          ),
        ],
      ),
    );
  }


  Widget _buildIndexTradingTab() {
    return BlocSelector<IndexTradeCubit,
        IndexTradeState,
        ({int selectedIndex, List<IndexStockConfig> indexes, List<IndexStockInfo> indexStocks})>(
      selector: (state) =>
      (
      selectedIndex: state.selectedIndex,
      indexes: state.indexStockConfigList,
      indexStocks: state.indexStocks,
      ),
      builder: (context, indexState) {
        if (indexState.indexes.isEmpty || indexState.indexStocks.isEmpty) {
          return const Center(child: CircularProgressIndicator.adaptive());
        }

        final safeIndex = indexState.selectedIndex.clamp(0, indexState.indexes.length - 1);
        final selected = indexState.indexes[safeIndex];
        final security = Security(instrument: selected.instrument);

        _indexTradingCubit ??= TradingCenterV2Cubit(
          security: security,
          initialTabType: TradeTabType.Trading,
          initialTradeAccountId: kDropDownValueSpotId,
        );

        return BlocProvider.value(
          value: _indexTradingCubit!,
          child: const IndexTradingPage(),
        );
      },
    );
  }
}
