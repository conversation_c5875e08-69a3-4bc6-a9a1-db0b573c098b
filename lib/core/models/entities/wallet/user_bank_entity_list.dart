import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/user_bank_entity_list.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/user_bank_entity_list.g.dart';

@JsonSerializable()
class UserBankEntityList {
  final List<UserBankEntity> list;

  const UserBankEntityList({
    this.list = const [],
  });

  factory UserBankEntityList.fromJson(Map<String, dynamic> json) => $UserBankEntityListFromJson(json);

  Map<String, dynamic> toJson() => $UserBankEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class UserBankEntity {
  final String bankAccount;
  final String bankCode;
  final String bankFullName;
  final String bankIcon;
  final String icon;
  final int id;
  final String realName;
  final String status;
  final String tailNumber;
  final int userId;

  const UserBankEntity({
    this.bankAccount = '',
    this.bankCode = '',
    this.bankFullName = '',
    this.bankIcon = '',
    this.icon = '',
    this.id = 0,
    this.realName = '',
    this.status = '',
    this.tailNumber = '',
    this.userId = 0,
  });

  factory UserBankEntity.fromJson(Map<String, dynamic> json) => $UserBankEntityFromJson(json);

  Map<String, dynamic> toJson() => $UserBankEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}