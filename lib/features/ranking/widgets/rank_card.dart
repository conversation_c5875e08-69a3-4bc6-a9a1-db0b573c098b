import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/utils/trading_utils.dart';
import 'package:gp_stock_app/features/ranking/models/rank_model.dart';
import 'package:gp_stock_app/shared/widgets/avatar/app_avatar.dart';

class RankCard extends StatelessWidget {
  const RankCard({
    super.key,
    this.topMargin,
    required this.rankModel,
    required this.rankingEntity,
  });

  final double? topMargin;
  final RankViewModel rankModel;
  final RankingEntity rankingEntity;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10.gw, 38.gw, 10.gw, 10.gw),
      margin: EdgeInsets.only(left: 5.gw, right: 5.gw, top: topMargin ?? 0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: rankModel.borderColor),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            rankModel.gradientColor,
            Colors.white,
          ],
        ),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.center,
        children: [
          Column(
            spacing: 6.gw,
            children: [
              Image.asset(
                'assets/icons/rank_${rankModel.rank}.png',
                height: 35.gw,
              ),
              Text(
                rankingEntity.nickName,
                style: context.textTheme.title.fs16.w600,
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                rankModel.label.tr(namedArgs: {'rank': '${rankingEntity.rankingNo}'}),
                style: context.textTheme.regular.fs12.w500,
                textAlign: TextAlign.center,
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 4.gw),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7.gr),
                  color: context.theme.primaryColor,
                ),
                child: Text('${rankingEntity.profitRate}%', style: context.textTheme.secondary.fs13.w600),
              ),
              Text('¥ ${TradingUtils.formatPrice(rankingEntity.amount)}', style: context.textTheme.tertiary.fs12.w500),
            ],
          ),
          Positioned(
            top: -70,
            child: AppAvatar(avatar: rankingEntity.avatar, size: 50.gw),
          ),
        ],
      ),
    );
  }
}
