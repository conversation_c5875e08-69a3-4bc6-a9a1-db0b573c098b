import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/account.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/models/entities/wallet/funds_record_list.dart';
import 'package:gp_stock_app/features/contract/domain/models/date_filter.dart';
import 'package:gp_stock_app/features/contract/domain/models/transaction_type.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';

part 'fund_records_state.dart';

@injectable
class FundRecordsCubit extends Cubit<FundRecordsState> {
  FundRecordsCubit() : super(FundRecordsState());

  Future<void> getAssetRecord({
    required int? contractId,
    bool isLoadMore = false,
    String? commentAssetId,
  }) async {
    if (!isLoadMore) {
      emit(state.copyWith(assetsRecordsFetchStatus: DataStatus.loading));
    }
    int page = isLoadMore ? (state.fundsRecords?.current ?? 0) + 1 : 1;
    final result = await AccountApi.fetchFundsRecordList(
      contractId: contractId,
      pageNum: page,
      commentAssetId: commentAssetId,
      fromType: state.transactionType?.type,
      startTime: state.dateFilter?.startTime,
      endTime: state.dateFilter?.endTime,
    );

    if (result != null) {
      final records = isLoadMore
          ? [...?state.fundsRecords?.records, ...result.records]
          : result.records;

      emit(state.copyWith(
        assetsRecordsFetchStatus: DataStatus.success,
        fundsRecords: FundsRecordList(
          records: records,
          current: result.current,
          hasNext: result.hasNext,
          total: result.total,
        ),
      ));
    } else {
      emit(state.copyWith(assetsRecordsFetchStatus: DataStatus.failed));
    }
  }

  Future<void> getContractAccountRecord({
    required int? contractId,
    bool isLoadMore = false,
  }) async {
    if (!isLoadMore) {
      emit(state.copyWith(assetsRecordsFetchStatus: DataStatus.loading));
    }
    int page = isLoadMore ? (state.fundsRecords?.current ?? 0) + 1 : 1;
    final result = await ContractApi.fetchFundsRecordList(
      contractId: contractId,
      pageNum: page,
    );

    if (result != null) {
      final records = isLoadMore
          ? [...?state.fundsRecords?.records, ...result.records]
          : result.records;

      emit(state.copyWith(
        assetsRecordsFetchStatus: DataStatus.success,
        fundsRecords: FundsRecordList(
          records: records,
          current: result.current,
          hasNext: result.hasNext,
          total: result.total,
        ),
      ));
    } else {
      emit(state.copyWith(assetsRecordsFetchStatus: DataStatus.failed));
    }
  }

  void setTransactionType(TransactionType? type) => emit(state.copyWith(transactionType: () => type));
  void setDateFilter(DateFilter? filter) => emit(state.copyWith(dateFilter: () => filter));
}
