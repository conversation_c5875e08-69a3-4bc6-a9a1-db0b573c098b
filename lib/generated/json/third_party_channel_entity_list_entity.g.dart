import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/third_party_channel_entity_list_entity.dart';

ThirdPartyChannelListEntity $ThirdPartyChannelListEntityFromJson(Map<String, dynamic> json) {
  final ThirdPartyChannelListEntity thirdPartyChannelListEntity = ThirdPartyChannelListEntity();
  final List<ThirdPartyChannelEntity>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ThirdPartyChannelEntity>(e) as ThirdPartyChannelEntity).toList();
  if (list != null) {
    thirdPartyChannelListEntity.list = list;
  }
  return thirdPartyChannelListEntity;
}

Map<String, dynamic> $ThirdPartyChannelListEntityToJson(ThirdPartyChannelListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension ThirdPartyChannelListEntityExtension on ThirdPartyChannelListEntity {
  ThirdPartyChannelListEntity copyWith({
    List<ThirdPartyChannelEntity>? list,
  }) {
    return ThirdPartyChannelListEntity()
      ..list = list ?? this.list;
  }
}

ThirdPartyChannelEntity $ThirdPartyChannelEntityFromJson(Map<String, dynamic> json) {
  final ThirdPartyChannelEntity thirdPartyChannelEntity = ThirdPartyChannelEntity();
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    thirdPartyChannelEntity.currency = currency;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    thirdPartyChannelEntity.icon = icon;
  }
  final List<ThirdPartyChannelPayType>? payTypeList = (json['payTypeList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ThirdPartyChannelPayType>(e) as ThirdPartyChannelPayType).toList();
  if (payTypeList != null) {
    thirdPartyChannelEntity.payTypeList = payTypeList;
  }
  final String? payWayCode = jsonConvert.convert<String>(json['payWayCode']);
  if (payWayCode != null) {
    thirdPartyChannelEntity.payWayCode = payWayCode;
  }
  final String? payWayName = jsonConvert.convert<String>(json['payWayName']);
  if (payWayName != null) {
    thirdPartyChannelEntity.payWayName = payWayName;
  }
  final bool? recommended = jsonConvert.convert<bool>(json['recommended']);
  if (recommended != null) {
    thirdPartyChannelEntity.recommended = recommended;
  }
  final bool? isNeedBind = jsonConvert.convert<bool>(json['isNeedBind']);
  if (isNeedBind != null) {
    thirdPartyChannelEntity.isNeedBind = isNeedBind;
  }
  return thirdPartyChannelEntity;
}

Map<String, dynamic> $ThirdPartyChannelEntityToJson(ThirdPartyChannelEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currency'] = entity.currency;
  data['icon'] = entity.icon;
  data['payTypeList'] = entity.payTypeList.map((v) => v.toJson()).toList();
  data['payWayCode'] = entity.payWayCode;
  data['payWayName'] = entity.payWayName;
  data['recommended'] = entity.recommended;
  data['isNeedBind'] = entity.isNeedBind;
  return data;
}

extension ThirdPartyChannelEntityExtension on ThirdPartyChannelEntity {
  ThirdPartyChannelEntity copyWith({
    String? currency,
    String? icon,
    List<ThirdPartyChannelPayType>? payTypeList,
    String? payWayCode,
    String? payWayName,
    bool? recommended,
    bool? isNeedBind,
  }) {
    return ThirdPartyChannelEntity()
      ..currency = currency ?? this.currency
      ..icon = icon ?? this.icon
      ..payTypeList = payTypeList ?? this.payTypeList
      ..payWayCode = payWayCode ?? this.payWayCode
      ..payWayName = payWayName ?? this.payWayName
      ..recommended = recommended ?? this.recommended
      ..isNeedBind = isNeedBind ?? this.isNeedBind;
  }
}

ThirdPartyChannelPayType $ThirdPartyChannelPayTypeFromJson(Map<String, dynamic> json) {
  final ThirdPartyChannelPayType thirdPartyChannelPayType = ThirdPartyChannelPayType();
  final String? amountList = jsonConvert.convert<String>(json['amountList']);
  if (amountList != null) {
    thirdPartyChannelPayType.amountList = amountList;
  }
  final int? amountMaxLimit = jsonConvert.convert<int>(json['amountMaxLimit']);
  if (amountMaxLimit != null) {
    thirdPartyChannelPayType.amountMaxLimit = amountMaxLimit;
  }
  final int? amountMinLimit = jsonConvert.convert<int>(json['amountMinLimit']);
  if (amountMinLimit != null) {
    thirdPartyChannelPayType.amountMinLimit = amountMinLimit;
  }
  final String? controllerTips = jsonConvert.convert<String>(json['controllerTips']);
  if (controllerTips != null) {
    thirdPartyChannelPayType.controllerTips = controllerTips;
  }
  final int? exchangeRate = jsonConvert.convert<int>(json['exchangeRate']);
  if (exchangeRate != null) {
    thirdPartyChannelPayType.exchangeRate = exchangeRate;
  }
  final bool? fixedAmount = jsonConvert.convert<bool>(json['fixedAmount']);
  if (fixedAmount != null) {
    thirdPartyChannelPayType.fixedAmount = fixedAmount;
  }
  final int? payTypeId = jsonConvert.convert<int>(json['payTypeId']);
  if (payTypeId != null) {
    thirdPartyChannelPayType.payTypeId = payTypeId;
  }
  final String? payTypeName = jsonConvert.convert<String>(json['payTypeName']);
  if (payTypeName != null) {
    thirdPartyChannelPayType.payTypeName = payTypeName;
  }
  final int? sort = jsonConvert.convert<int>(json['sort']);
  if (sort != null) {
    thirdPartyChannelPayType.sort = sort;
  }
  return thirdPartyChannelPayType;
}

Map<String, dynamic> $ThirdPartyChannelPayTypeToJson(ThirdPartyChannelPayType entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['amountList'] = entity.amountList;
  data['amountMaxLimit'] = entity.amountMaxLimit;
  data['amountMinLimit'] = entity.amountMinLimit;
  data['controllerTips'] = entity.controllerTips;
  data['exchangeRate'] = entity.exchangeRate;
  data['fixedAmount'] = entity.fixedAmount;
  data['payTypeId'] = entity.payTypeId;
  data['payTypeName'] = entity.payTypeName;
  data['sort'] = entity.sort;
  return data;
}

extension ThirdPartyChannelPayTypeExtension on ThirdPartyChannelPayType {
  ThirdPartyChannelPayType copyWith({
    String? amountList,
    int? amountMaxLimit,
    int? amountMinLimit,
    String? controllerTips,
    int? exchangeRate,
    bool? fixedAmount,
    int? payTypeId,
    String? payTypeName,
    int? sort,
  }) {
    return ThirdPartyChannelPayType()
      ..amountList = amountList ?? this.amountList
      ..amountMaxLimit = amountMaxLimit ?? this.amountMaxLimit
      ..amountMinLimit = amountMinLimit ?? this.amountMinLimit
      ..controllerTips = controllerTips ?? this.controllerTips
      ..exchangeRate = exchangeRate ?? this.exchangeRate
      ..fixedAmount = fixedAmount ?? this.fixedAmount
      ..payTypeId = payTypeId ?? this.payTypeId
      ..payTypeName = payTypeName ?? this.payTypeName
      ..sort = sort ?? this.sort;
  }
}