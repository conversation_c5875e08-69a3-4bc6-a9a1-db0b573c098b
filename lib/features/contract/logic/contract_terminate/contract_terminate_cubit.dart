import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';


part 'contract_terminate_state.dart';

@injectable
class ContractTerminateCubit extends Cubit<ContractTerminateState> {
  ContractTerminateCubit() : super(const ContractTerminateState());

  Future<void> terminateContract({required String contractId}) async {
    emit(state.copyWith(terminateStatus: DataStatus.loading));
    final flag = await ContractApi.terminateContract(contractId: contractId);
    if (flag) {
      emit(state.copyWith(terminateStatus: DataStatus.success));
    } else {
      emit(state.copyWith(terminateStatus: DataStatus.failed));
    }
  }
}
