import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/auth.dart';
import 'package:gp_stock_app/core/models/entities/user/user.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/secure_storage_helper.dart';
import 'package:gp_stock_app/core/utils/wangyi_captcha_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/keys.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_socket_interface.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/utils/auth/auth_utils.dart';
import '../../../../shared/constants/enums.dart';

part 'sign_in_state.dart';

@lazySingleton
class SignInCubit extends Cubit<SignInState> {
  final WebSocketService _webSocketService;

  SignInCubit(this._webSocketService) : super(const SignInState());

  void setLoginMode(LoginMainMode loginMode) =>
      emit(state.copyWith(loginMode: (loginMainMode: loginMode, loginSubMode: state.loginMode.loginSubMode)));

  void setLoginSubMode({LoginSubMode? loginSubMode}) => emit(
        state.copyWith(
          loginMode: loginSubMode != null
              ? (loginMainMode: state.loginMode.loginMainMode, loginSubMode: loginSubMode)
              : switch (state.loginMode.loginSubMode) {
                  LoginSubMode.email => (
                      loginMainMode: state.loginMode.loginMainMode,
                      loginSubMode: LoginSubMode.mobile
                    ),
                  LoginSubMode.mobile => (
                      loginMainMode: state.loginMode.loginMainMode,
                      loginSubMode: LoginSubMode.email
                    ),
                },
        ),
      );

  Future<void> login({
    required String username,
    required String password,
    required String mobile,
    required String smsCode,
  }) async {
    emit(state.copyWith(loginFetchStatus: DataStatus.loading));
    final loginMode = state.loginMode.loginMainMode;
    final accountName = _getAccountName(loginMode, username, mobile);

    final (bool isSuccess, bool isNeedCaptcha) = await AuthApi.fetchWangYiCaptchaRequired(
      type: WangYiCaptchaType.kLogin,
    );
    if (!isSuccess) {
      emit(state.copyWith(loginFetchStatus: DataStatus.failed));
      return GPEasyLoading.showToast("get_verification_method_failed".tr());
    }
    if (isNeedCaptcha) {
      _handleCaptcha(accountName, password, smsCode);
    } else {
      _commitLogin(accountName, password, smsCode);
    }
  }

  String _getAccountName(LoginMainMode loginMode, String username, String mobile) {
    return loginMode == LoginMainMode.password ? username : mobile;
  }

  void _handleCaptcha(String accountName, String password, String smsCode) {
    WangYiCaptchaUtil().show(
      captchaId: AppConfig.instance.wangYiCaptchaKey,
      account: accountName,
      onSuccess: (code) => _commitLogin(accountName, password, smsCode, validate: code),
      onValidateFailClose: _onCaptchaValidationFailed,
      onError: _onCaptchaError,
    );
  }

  void _onCaptchaValidationFailed() {
    if (!isClosed) {
      emit(state.copyWith(loginFetchStatus: DataStatus.idle));
    }
  }

  void _onCaptchaError() {
    if (!isClosed) {
      GPEasyLoading.showToast('验证失败'.tr());
      emit(state.copyWith(loginFetchStatus: DataStatus.failed));
    }
  }

  Future<void> _commitLogin(String userName, String password, String code, {String? validate}) async {
    final model = await AuthApi.login(
      userName: userName,
      password: password,
      verifyType: _verifyType,
      smsCode: state.isLoginWithMobileOTP ? code : null,
      emailCode: state.isLoginWithEmailOTP ? code : null,
      validate: validate,
    );
    if (model != null) {
      _onLoginSuccess(model, userName, password);
    } else {
      emit(state.copyWith(loginFetchStatus: DataStatus.failed));
    }
  }

  Future<void> _onLoginSuccess(UserModel model, String userName, String password) async {
    final userCubit = getIt<UserCubit>();
    userCubit.setUserInfo(model);
    userCubit.fetchUserInfo();
    if (_shouldRememberPassword()) {
      await AuthUtils.instance
          .rememberPassword(true, username: userName, password: password, loginSubMode: state.loginMode.loginSubMode);
    }
    _webSocketService.send({'type': "auth", 'data': userCubit.state.token});
    emit(state.copyWith(loginFetchStatus: DataStatus.success));
  }

  bool _shouldRememberPassword() {
    return state.loginMode.loginMainMode == LoginMainMode.password && state.isRememberPassword;
  }

  void init() {
    SecureStorageHelper().readSecureData(LocalStorageKeys.userV2).then((value) async {
      if (value != null) {
        final token = await SecureStorageHelper().readSecureData(LocalStorageKeys.token);
        _webSocketService.send({
          'type': "auth",
          'data': token,
        });
        emit(
          state.copyWith(loginFetchStatus: DataStatus.success),
        );
      }
    });
  }

  Future<void> logout() async {
    await SecureStorageHelper().deleteAllExcept();
    NetworkProvider.clearCache();
    emit(state.copyWith(clearData: true));
  }

  Future<void> rememberPassword(bool value, {String? username, String? password}) async {
    emit(state.copyWith(isRememberPassword: value));
    if (value) {
      await AuthUtils.instance
          .rememberPassword(value, username: username, password: password, loginSubMode: state.loginMode.loginSubMode);
    } else {
      await AuthUtils.instance.clearRememberedCredentials();
    }
  }

  Future<({bool isRememberPassword, String? username, String? password, LoginSubMode? loginSubMode})>
      getRememberPassword() async {
    final value = await AuthUtils.instance.isRememberPassword;
    emit(state.copyWith(isRememberPassword: value.isRememberPassword));
    return value;
  }

  void acceptTerms(bool value) {
    emit(state.copyWith(isAcceptedTerms: value));
  }

  String get _verifyType => switch (state.loginMode.loginMainMode) {
        LoginMainMode.password => switch (state.loginMode.loginSubMode) {
            LoginSubMode.mobile => "account",
            LoginSubMode.email => "email_password",
          },
        LoginMainMode.otp => switch (state.loginMode.loginSubMode) {
            LoginSubMode.mobile => "mobile",
            LoginSubMode.email => "email_code",
          }
      };
}
