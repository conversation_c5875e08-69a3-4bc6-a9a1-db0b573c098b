import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/trade_navigation_helper.dart';
import 'package:gp_stock_app/shared/widgets/market_table/index_trade_row.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

/// 首页指数股票列表组件（垂直列表，无图表）
class IndexStockListView extends StatefulWidget {
  const IndexStockListView({super.key});

  @override
  State<IndexStockListView> createState() => _IndexStockListViewState();
}

class _IndexStockListViewState extends State<IndexStockListView> with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IndexTradeCubit, IndexTradeState>(
      builder: (context, state) {
        int itemCount = state.indexStocks.length;

        if (state.status == DataStatus.loading) {
          return _buildLoadingList();
        }

        if (state.status.isFailed) {
          return const Center(child: TableEmptyWidget());
        }

        if (state.status == DataStatus.success && itemCount <= 0) {
          return const Center(child: TableEmptyWidget());
        }

        if (itemCount > 5) {
          itemCount = 5;
        }

        return ListView.builder(
          physics: NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          itemCount: itemCount,
          itemBuilder: (context, index) {
            final item = state.indexStocks[index].stockInfo;
            return IndexTradeRow(
              data: item,
              onTap: () {
                AuthUtils.verifyAuth(() {
                  TradeNavigationHelper.goToTradingCenterV2(
                    context,
                    security: item.instrumentInfo,
                    tabType: TradeTabType.Quotes,
                    isIndexTradingOverride: true,
                  );
                });
              },
            );
          },
        );
      },
    );
  }

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        6,
        (_) => Padding(
          padding: EdgeInsets.only(bottom: 8.gw),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.gr),
            child: ShimmerWidget(
              height: 45.gw,
              width: double.infinity,
            ),
          ),
        ),
      ),
    );
  }
}
