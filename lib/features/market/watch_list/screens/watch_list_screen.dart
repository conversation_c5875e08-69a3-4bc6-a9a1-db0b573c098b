import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import '../widgets/wishlist_data_table.dart';

class WatchListScreen extends StatefulWidget {
  const WatchListScreen({super.key});

  @override
  State<WatchListScreen> createState() => _WatchListScreenState();
}

class _WatchListScreenState extends State<WatchListScreen> {
  final ScrollController _scrollController = ScrollController();
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      context.read<WatchListCubit>().getWatchList(loadMore: true);
    }
  }

  final tabContents = [
    WishListDataTable(),
    WishListDataTable(filter: MarketSymbol.CN),
    WishListDataTable(filter: MarketSymbol.HK),
    WishListDataTable(filter: MarketSymbol.US),
  ];

  @override
  Widget build(BuildContext context) {
    final tabData = [
      'all',
      MarketSymbol.CN.displayName,
      MarketSymbol.HK.displayName,
      MarketSymbol.US.displayName,
    ];

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(50.gw),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 4.gw),
          color: context.theme.scaffoldBackgroundColor,
          child: CommonTabBar.withAutoKey(
            tabData.map((e) => e.tr()).toList(),
            currentIndex: _selectedTabIndex,
            onTap: (index) {
              setState(() => _selectedTabIndex = index);
              context
                  .read<WatchListCubit>()
                  .setMarket(MarketSymbol.getMarketType(tabContents[index].filter?.marketSymbol));
            },
            style: CommonTabBarStyle.line,
            isScrollable: true,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.only(top: 10.gw),
        child: tabContents[_selectedTabIndex],
      ),
    );
  }
}
