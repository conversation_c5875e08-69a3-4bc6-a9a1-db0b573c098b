import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_wallet_entity_list.dart';

UserWalletEntityList $UserWalletEntityListFromJson(Map<String, dynamic> json) {
  final List<UserWalletEntity>? list = (json['list'] as List<dynamic>?)?.map(
                      (e) => jsonConvert.convert<UserWalletEntity>(e) as UserWalletEntity).toList();
  return UserWalletEntityList(
    list: list ?? const [],
  );
}

Map<String, dynamic> $UserWalletEntityListToJson(UserWalletEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension UserWalletEntityListExtension on UserWalletEntityList {
  UserWalletEntityList copyWith({
    List<UserWalletEntity>? list,
  }) {
    return UserWalletEntityList(
      list: list ?? this.list,
    );
  }
}

UserWalletEntity $UserWalletEntityFromJson(Map<String, dynamic> json) {
  final String? bankCode = jsonConvert.convert<String>(json['bankCode']);
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  final String? creator = jsonConvert.convert<String>(json['creator']);
  final String? icon = jsonConvert.convert<String>(json['icon']);
  final int? id = jsonConvert.convert<int>(json['id']);
  final String? payAddress = jsonConvert.convert<String>(json['payAddress']);
  final String? payTypeCode = jsonConvert.convert<String>(json['payTypeCode']);
  final String? payWayCode = jsonConvert.convert<String>(json['payWayCode']);
  final int? siteId = jsonConvert.convert<int>(json['siteId']);
  final String? type = jsonConvert.convert<String>(json['type']);
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  final String? updater = jsonConvert.convert<String>(json['updater']);
  final int? userId = jsonConvert.convert<int>(json['userId']);
  return UserWalletEntity(
    bankCode: bankCode ?? '',
    createTime: createTime ?? '',
    creator: creator ?? '',
    icon: icon ?? '',
    id: id ?? 0,
    payAddress: payAddress ?? '',
    payTypeCode: payTypeCode ?? '',
    payWayCode: payWayCode ?? '',
    siteId: siteId ?? 0,
    type: type ?? '',
    updateTime: updateTime ?? '',
    updater: updater ?? '',
    userId: userId ?? 0,
  );
}

Map<String, dynamic> $UserWalletEntityToJson(UserWalletEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['bankCode'] = entity.bankCode;
  data['createTime'] = entity.createTime;
  data['creator'] = entity.creator;
  data['icon'] = entity.icon;
  data['id'] = entity.id;
  data['payAddress'] = entity.payAddress;
  data['payTypeCode'] = entity.payTypeCode;
  data['payWayCode'] = entity.payWayCode;
  data['siteId'] = entity.siteId;
  data['type'] = entity.type;
  data['updateTime'] = entity.updateTime;
  data['updater'] = entity.updater;
  data['userId'] = entity.userId;
  return data;
}

extension UserWalletEntityExtension on UserWalletEntity {
  UserWalletEntity copyWith({
    String? bankCode,
    String? createTime,
    String? creator,
    String? icon,
    int? id,
    String? payAddress,
    String? payTypeCode,
    String? payWayCode,
    int? siteId,
    String? type,
    String? updateTime,
    String? updater,
    int? userId,
  }) {
    return UserWalletEntity(
      bankCode: bankCode ?? this.bankCode,
      createTime: createTime ?? this.createTime,
      creator: creator ?? this.creator,
      icon: icon ?? this.icon,
      id: id ?? this.id,
      payAddress: payAddress ?? this.payAddress,
      payTypeCode: payTypeCode ?? this.payTypeCode,
      payWayCode: payWayCode ?? this.payWayCode,
      siteId: siteId ?? this.siteId,
      type: type ?? this.type,
      updateTime: updateTime ?? this.updateTime,
      updater: updater ?? this.updater,
      userId: userId ?? this.userId,
    );
  }
}