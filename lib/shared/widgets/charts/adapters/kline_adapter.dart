import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/market/stock_kline_entity.dart';
import '../models/kline_config.dart';

/// Abstract interface for K-line chart adapters
abstract class KlineAdapter {
  /// Build the chart widget
  Widget buildChart({
    required BuildContext context,
    required List<StockKlineItem> rawData,
    required KlineConfig config,
    dynamic controller,
  });


  /// Returns a controller that can be used to control the chart
  /// (zoom, scroll, etc.) programmatically.
  dynamic createController() {
    return null; // Default: no controller
  }

  void dispose() {
    // Default: No-op, can override if needed
  }
}

/// Controller interface for chart control
abstract class KlineController {
  /// Zoom to a specific scale
  Future<void> scaleTo(double scale);

  /// Zoom in
  Future<void> zoomIn();

  /// Zoom out
  Future<void> zoomOut();

  /// Reset zoom to default
  Future<void> resetZoom();

  /// Get current scale
  double get currentScale;

  /// Save current state
  void saveState();

  /// Restore saved state
  Future<void> restoreState();

  /// Dispose controller
  void dispose();
}
