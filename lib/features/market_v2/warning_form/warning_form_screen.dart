import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/trade/stock_info_v2.dart';
import 'package:gp_stock_app/core/models/entities/market/warn_response_entity.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_state.dart';
import 'package:gp_stock_app/features/market_v2/warning_form/warning_form_cubit.dart';
import 'package:gp_stock_app/features/market_v2/warning_form/warning_form_state.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';

class WarningFormScreen extends StatefulWidget {
  final Security security;
  final bool isUpdate;
  const WarningFormScreen({
    super.key,
    required this.security,
    this.isUpdate = false,
  });

  @override
  State<WarningFormScreen> createState() => _WarningFormScreenState();
}

class _WarningFormScreenState extends State<WarningFormScreen> {
  final _priceRoseController = TextEditingController(text: '0');
  final _priceFellController = TextEditingController(text: '0');
  final _dailyIncreaseController = TextEditingController(text: '0');
  final _dailyDeclineController = TextEditingController(text: '0');

  @override
  void initState() {
    super.initState();
    context.read<WarningFormCubit>().getWarnByInstrument(widget.security.instrument);
  }

  @override
  void dispose() {
    _priceRoseController.dispose();
    _priceFellController.dispose();
    _dailyIncreaseController.dispose();
    _dailyDeclineController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: context.colorTheme.textPrimary,
            size: 20.gw,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.isUpdate ? 'editWarning'.tr() : 'addWarning'.tr(),
          style: context.textTheme.primary.fs18.w600,
        ),
        actions: [
          if (!widget.isUpdate)
            TextButton(
              onPressed: () => getIt<NavigatorService>().push(AppRouter.warningList),
              child: Text(
                'mineWarning'.tr(),
                style: context.textTheme.primary.copyWith(
                  color: context.theme.primaryColor,
                ),
              ),
            ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<WarningFormCubit, WarningFormState>(
            listenWhen: (previous, current) => previous.fetchStatus != current.fetchStatus,
            listener: (context, state) {
              if (state.fetchStatus == DataStatus.success) {
                GPEasyLoading.dismiss();
                _priceRoseController.text = state.warnData?.targetUpPrice.toString() ?? '';
                _priceFellController.text = state.warnData?.targetDownPrice.toString() ?? '';
                _dailyIncreaseController.text = state.warnData?.targetUpGain.toString() ?? '';
                _dailyDeclineController.text = state.warnData?.targetDownGain.toString() ?? '';
              }
            },
          ),
        ],
        child: BlocBuilder<WarningFormCubit, WarningFormState>(
          builder: (context, state) {
            return SingleChildScrollView(
              padding: EdgeInsets.all(16.gr),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStockInfo(),
                  20.verticalSpace,
                  _buildWarningInputs(),
                  40.verticalSpace,
                  CommonButton(
                    title: 'submit'.tr(),
                    onPressed: state.fetchStatus == DataStatus.success ? _submitWarning : null,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStockInfo() {
    return BlocSelector<TradingCenterV2Cubit, TradingCenterV2State, StockInfoV2?>(
      selector: (state) => state.stockInfo,
      builder: (context, stockInfo) {
        return ShadowBox(
          child: Container(
            padding: EdgeInsets.all(4.gr),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Wrap(
                  children: [
                    AutoSizeText(
                      stockInfo?.name ?? '',
                      style: context.textTheme.primary.w600,
                    ),
                    8.horizontalSpace,
                    SymbolChip(name: stockInfo?.market ?? '', chipColor: context.theme.primaryColor),
                    8.horizontalSpace,
                    Text(
                      stockInfo?.symbol ?? '',
                      style: context.textTheme.regular,
                    ),
                  ],
                ),
                12.verticalSpace,
                Wrap(
                  spacing: 4,
                  children: [
                    AmountRow(
                      title: 'currentPrice'.tr(),
                      amount: stockInfo?.latestPrice ?? 0,
                    ),
                    AmountRow(
                      title: 'change'.tr(),
                      amount: stockInfo?.chg ?? 0,
                      color: (stockInfo?.chg ?? 0) > 0 ? context.colorTheme.stockGreen : context.colorTheme.stockRed,
                    ),
                    AmountRow(
                      title: 'changePercent'.tr(),
                      suffix: '%',
                      amount: stockInfo?.gain ?? 0,
                      color: (stockInfo?.gain ?? 0) > 0 ? context.colorTheme.stockGreen : context.colorTheme.stockRed,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWarningInputs() {
    return ShadowBox(
      child: Theme(
        data: context.theme.copyWith(
          inputDecorationTheme: context.theme.inputDecorationTheme.copyWith(
            border: InputBorder.none,
          ),
        ),
        child: Column(
          children: [
            _buildInputRow('priceRoseTo'.tr(), _priceRoseController),
            Divider(color: context.colorTheme.textRegular.withValues(alpha: 0.3), thickness: 0.5),
            _buildInputRow('priceFellTo'.tr(), _priceFellController),
            Divider(color: context.colorTheme.textRegular.withValues(alpha: 0.3), thickness: 0.5),
            _buildInputRow(
              'dailyIncreaseExceeds'.tr(),
              _dailyIncreaseController,
              suffix: Icon(Icons.percent, color: context.colorTheme.stockGreen),
            ),
            Divider(color: context.colorTheme.textRegular.withValues(alpha: 0.3), thickness: 0.5),
            _buildInputRow(
              'dailyDeclineExceeds'.tr(),
              _dailyDeclineController,
              suffix: Icon(Icons.percent, color: context.colorTheme.stockRed),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputRow(String label, TextEditingController controller, {Widget? suffix}) {
    return SizedBox(
      height: 50.gw,
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              label,
              style: context.textTheme.primary.fs15,
            ),
          ),
          Expanded(
            flex: 2,
            child: TextField(
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              controller: controller,
              style: context.textTheme.primary.fs15,
              decoration: InputDecoration(
                suffixIcon: suffix,
                suffixIconConstraints: const BoxConstraints(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _submitWarning() {
    final id = context.read<WarningFormCubit>().state.warnData?.id;
    final warn = WarnResponse(
      market: widget.security.market,
      symbol: widget.security.symbol,
      name: widget.security.name,
      targetUpPrice: double.tryParse(_priceRoseController.text) ?? 0,
      targetDownPrice: double.tryParse(_priceFellController.text) ?? 0,
      targetUpGain: double.tryParse(_dailyIncreaseController.text) ?? 0,
      targetDownGain: double.tryParse(_dailyDeclineController.text) ?? 0,
      id: id ?? 0,
      createTime: context.read<WarningFormCubit>().state.warnData?.createTime ?? '',
      updateTime: context.read<WarningFormCubit>().state.warnData?.updateTime ?? '',
      securityType: widget.security.securityType,
      userId: context.read<WarningFormCubit>().state.warnData?.userId ?? 0,
    );
    if (id != null && id > 0) {
      context.read<WarningFormCubit>().addOrUpdateWarn(warn, isUpdate: true);
    } else {
      context.read<WarningFormCubit>().addOrUpdateWarn(warn);
    }
  }
}
