import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/user/user.dart';
import 'package:equatable/equatable.dart';


UserModel $UserModelFromJson(Map<String, dynamic> json) {
  return UserModel(auth: jsonConvert.convert<bool>(json['auth']) ?? false,
      authStatus: jsonConvert.convert<int>(json['authStatus']) ?? 0,
      avatar: jsonConvert.convert<String>(json['avatar']) ?? '',
      countryCode: jsonConvert.convert<String>(json['countryCode']) ?? '',
      email: jsonConvert.convert<String>(json['email']) ?? '',
      fromType: jsonConvert.convert<int>(json['fromType']) ?? 0,
      id: jsonConvert.convert<int>(json['id']) ?? 0,
      idCard: jsonConvert.convert<String>(json['idCard']) ?? '',
      imAccount: jsonConvert.convert<String>(json['imAccount']) ?? '',
      inviteCode: jsonConvert.convert<String>(json['inviteCode']) ?? '',
      isPayment: jsonConvert.convert<bool>(json['isPayment']) ?? false,
      level: jsonConvert.convert<int>(json['level']) ?? 0,
      mobile: jsonConvert.convert<String>(json['mobile']) ?? '',
      nickname: jsonConvert.convert<String>(json['nickname']) ?? '',
      pid: jsonConvert.convert<int>(json['pid']) ?? 0,
      profiles: jsonConvert.convert<String>(json['profiles']) ?? '',
      realName: jsonConvert.convert<String>(json['realName']) ?? '',
      score: jsonConvert.convert<int>(json['score']) ?? 0,
      sex: jsonConvert.convert<int>(json['sex']) ?? 0,
      status: jsonConvert.convert<bool>(json['status']) ?? false,
      tradeStatus: jsonConvert.convert<int>(json['tradeStatus']) ?? 0,
      type: jsonConvert.convert<int>(json['type']) ?? 0,
      uid: jsonConvert.convert<String>(json['uid']) ?? '',
      username: jsonConvert.convert<String>(json['username']) ?? '',
      registerType: jsonConvert.convert<String>(json['registerType']) ?? '');
}

Map<String, dynamic> $UserModelToJson(UserModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['auth'] = entity.auth;
  data['authStatus'] = entity.authStatus;
  data['avatar'] = entity.avatar;
  data['countryCode'] = entity.countryCode;
  data['email'] = entity.email;
  data['fromType'] = entity.fromType;
  data['id'] = entity.id;
  data['idCard'] = entity.idCard;
  data['imAccount'] = entity.imAccount;
  data['inviteCode'] = entity.inviteCode;
  data['isPayment'] = entity.isPayment;
  data['level'] = entity.level;
  data['mobile'] = entity.mobile;
  data['nickname'] = entity.nickname;
  data['pid'] = entity.pid;
  data['profiles'] = entity.profiles;
  data['realName'] = entity.realName;
  data['score'] = entity.score;
  data['sex'] = entity.sex;
  data['status'] = entity.status;
  data['tradeStatus'] = entity.tradeStatus;
  data['type'] = entity.type;
  data['uid'] = entity.uid;
  data['username'] = entity.username;
  data['registerType'] = entity.registerType;
  return data;
}

extension UserModelExtension on UserModel {
  UserModel copyWith({
    bool? auth,
    int? authStatus,
    String? avatar,
    String? countryCode,
    String? email,
    int? fromType,
    int? id,
    String? idCard,
    String? imAccount,
    String? inviteCode,
    bool? isPayment,
    int? level,
    String? mobile,
    String? nickname,
    int? pid,
    String? profiles,
    String? realName,
    int? score,
    int? sex,
    bool? status,
    int? tradeStatus,
    int? type,
    String? uid,
    String? username,
    String? registerType,
  }) {
    return UserModel(auth: auth ?? this.auth,
        authStatus: authStatus ?? this.authStatus,
        avatar: avatar ?? this.avatar,
        countryCode: countryCode ?? this.countryCode,
        email: email ?? this.email,
        fromType: fromType ?? this.fromType,
        id: id ?? this.id,
        idCard: idCard ?? this.idCard,
        imAccount: imAccount ?? this.imAccount,
        inviteCode: inviteCode ?? this.inviteCode,
        isPayment: isPayment ?? this.isPayment,
        level: level ?? this.level,
        mobile: mobile ?? this.mobile,
        nickname: nickname ?? this.nickname,
        pid: pid ?? this.pid,
        profiles: profiles ?? this.profiles,
        realName: realName ?? this.realName,
        score: score ?? this.score,
        sex: sex ?? this.sex,
        status: status ?? this.status,
        tradeStatus: tradeStatus ?? this.tradeStatus,
        type: type ?? this.type,
        uid: uid ?? this.uid,
        username: username ?? this.username,
        registerType: registerType ?? this.registerType);
  }
}