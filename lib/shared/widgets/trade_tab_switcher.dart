import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/tab_bar_widget.dart';

class TradeTabSwitcher extends StatelessWidget {
  final TradeTabType currentTab;
  final ValueChanged<TradeTabType> onTabSelected;
  final TabComponentBoxStyle style;

  const TradeTabSwitcher({
    super.key,
    required this.currentTab,
    required this.onTabSelected,
    this.style = TabComponentBoxStyle.kPrimary,
  });

  @override
  Widget build(BuildContext context) {
    return TabComponentBox(
      label1: 'trade'.tr(),
      label2: 'quotation'.tr(),
      selectedIndex: currentTab.index,
      style: style,
      onTap: (index) => onTabSelected(TradeTabType.values[index]),
    );
  }
}
