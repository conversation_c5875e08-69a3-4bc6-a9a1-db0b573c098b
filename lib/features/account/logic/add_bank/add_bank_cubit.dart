import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/bank_entity_list.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/features/account/logic/add_bank/add_bank_state.dart';

class AddBankCubit extends Cubit<AddBankState> {
  AddBankCubit() : super(const AddBankState());

  void updateCardNumber(String? cardNumber) {
    emit(state.copyWith(cardNumber: cardNumber));
    _validateForm();
  }

  void updateOtp(String? otp) {
    emit(state.copyWith(otp: otp));
    _validateForm();
  }

  void updateSelectedBank(BankEntity? bank) {
    emit(state.copyWith(selectedBank: bank));
    _validateForm();
  }

  void _validateForm() {
    final isUserRegisteredViaEmail = getIt<UserCubit>().isUserRegisteredViaEmail;
    final isValid = state.cardNumber != null &&
        state.cardNumber!.isNotEmpty &&
        state.selectedBank != null &&
        (isUserRegisteredViaEmail || state.otp != null && state.otp!.isNotEmpty);

    emit(state.copyWith(isFormValid: isValid));
  }
}
