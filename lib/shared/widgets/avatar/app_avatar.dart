import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AppAvatar extends StatelessWidget {
  final String? avatar;
  final double size;
  final Color? backgroundColor;

  const AppAvatar({
    super.key,
    this.avatar,
    this.size = 60,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final bgColor = backgroundColor ?? context.theme.primaryColor;
    final radius = size / 2;

    // 无头像 - 显示默认图标
    if (avatar == null || avatar!.isEmpty) {
      return CircleAvatar(
        radius: radius,
        backgroundColor: bgColor,
        child: Icon(Icons.person_outline, color: Colors.white, size: size * 0.5),
      );
    }

    // 网络图片
    if (avatar!.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: avatar!,
        imageBuilder: (context, imageProvider) => CircleAvatar(
          radius: radius,
          backgroundImage: imageProvider,
        ),
        placeholder: (context, url) => CircleAvatar(
          radius: radius,
          backgroundColor: bgColor,
          child: SizedBox(
            width: size * 0.4,
            height: size * 0.4,
            child: const CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
          ),
        ),
        errorWidget: (context, url, error) => CircleAvatar(
          radius: radius,
          backgroundColor: bgColor,
          child: Icon(Icons.person_outline, color: Colors.white, size: size * 0.5),
        ),
      );
    }

    // 本地图片
    return CircleAvatar(
      radius: radius,
      backgroundColor: bgColor,
      backgroundImage: AssetImage('assets/images/avatars/$avatar.png'),
    );
  }
}
