import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/trade/depth_quote.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_state_v2.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/order_list_state.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/account_order_list_view.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/order_table_header/order_table_header.dart';
import 'package:gp_stock_app/features/market/widgets/sliver_app_bar_delegate.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_state.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trade/stock_order_book/stock_order_book_section.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trade/stock_price_section/stock_price_section.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trade/trading_form_section/trading_form_section.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown_v2.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

class TradePage extends StatefulWidget {
  const TradePage({
    super.key,
    required this.security,
    this.headerSlivers = const [],
  });

  final Security security;
  final List<Widget> headerSlivers;

  @override
  State<TradePage> createState() => _TradePageState();
}

class _TradePageState extends State<TradePage> {
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<TradingCenterV2Cubit>();
    return MultiBlocListener(
      listeners: [
        // 监听现货账户余额变化 / Listen to spot account balance changes
        BlocListener<UserCubit, UserState>(
          listenWhen: (previous, current) {
            // 监听 accountInfo 对象变化（包括 null -> 非null）或 usableCash 变化
            final prevCash = previous.accountInfo?.usableCash;
            final currCash = current.accountInfo?.usableCash;
            return prevCash != currCash;
          },
          listener: (context, state) {
            context.read<TradingCenterV2Cubit>().onSpotBalanceChanged(state.accountInfo?.usableCash ?? 0);
          },
        ),
        // 监听合约账户余额变化 / Listen to contract account balance changes
        BlocListener<AccountScreenCubitV2, AccountScreenStateV2>(
          listenWhen: (previous, current) {
            // 获取当前选择的交易账户ID
            final accountId = context.read<TradingCenterV2Cubit>().state.tradeForm.currentTradeAccount?.id;
            // 如果不是合约账户，不监听
            if (accountId == null || accountId == kDropDownValueSpotId) return false;

            // 查找当前账户对应的合约
            final prevContract = previous.contractSummaryList.firstWhereOrNull((c) => c.id.toString() == accountId);
            final currContract = current.contractSummaryList.firstWhereOrNull((c) => c.id.toString() == accountId);

            // 只有当余额发生变化时才触发
            return prevContract?.useAmount != currContract?.useAmount;
          },
          listener: (context, state) {
            final tradingCubit = context.read<TradingCenterV2Cubit>();
            final accountId = tradingCubit.state.tradeForm.currentTradeAccount?.id;

            // 验证是合约账户
            if (accountId != null && accountId != kDropDownValueSpotId) {
              final contract = state.contractSummaryList.firstWhereOrNull((c) => c.id.toString() == accountId);

              if (contract != null) {
                tradingCubit.onContractBalanceChanged(contract);
              }
            }
          },
        ),
      ],
      child: BlocSelector<TradingCenterV2Cubit, TradingCenterV2State,
              ({OrderListType orderListType, Map<OrderListType, OrderListState> orderListStateMap})>(
          selector: (state) => (
                orderListType: state.currentOrderListType,
                orderListStateMap: state.orderListStateMap,
              ),
          builder: (context, state) {
            final entries = state.orderListStateMap.entries.toList();
            final keys = state.orderListStateMap.keys.toList();
            final pageIndex = keys.indexOf(state.orderListType);
            bool needShowTableHeader = state.orderListType != OrderListType.positions;
            return BlocListener<TradingCenterV2Cubit, TradingCenterV2State>(
                listenWhen: (previous, current) {
                  final instrumentChanged = previous.security.instrument != current.security.instrument;
                  final tabBecameTrade = previous.currentTabType != current.currentTabType &&
                      current.currentTabType == TradeTabType.Trading;
                  return instrumentChanged || tabBecameTrade;
                },
                listener: (context, state) {
                  if (!_scrollController.hasClients) return;
                  _scrollController.animateTo(
                    0,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                },
                child: NestedScrollView(
                  controller: _scrollController,
                  headerSliverBuilder: (context, innerBoxIsScrolled) {
                    final slivers = <Widget>[
                      ...widget.headerSlivers,
                      SliverToBoxAdapter(
                        child: _buildTradingSection(context),
                      ),
                    ];

                    if (keys.isNotEmpty) {
                      slivers.add(
                        SliverOverlapAbsorber(
                          handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                          sliver: SliverPersistentHeader(
                            pinned: true,
                            delegate: SliverAppBarDelegate(
                              maxHeight: needShowTableHeader ? 86 : 45,
                              minHeight: needShowTableHeader ? 86 : 45,
                              child: ColoredBox(
                                color: context.theme.scaffoldBackgroundColor,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CommonTabBar.withAutoKey(
                                      entries
                                          .map((e) => "${tr(e.key.tradingCenterKey)}${e.value.countIfNotEmpty}")
                                          .toList(),
                                      currentIndex: pageIndex,
                                      onTap: (index) => cubit.onChangeCurrentOrderListType(keys[index]),
                                      style: CommonTabBarStyle.line,
                                      isScrollable: true,
                                      padding: EdgeInsets.only(left: 8.gw),
                                    ),

                                    SizedBox(height: 5),

                                    /// 成交明细/委托明细 头部标题
                                    if (needShowTableHeader) ...[
                                      OrderTableHeader(
                                        type: state.orderListType,
                                        margin: EdgeInsets.symmetric(horizontal: 11.gw),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    }

                    return slivers;
                  },
                  body: DirectSlideView(
                    pages: entries.map((entry) {
                      final type = entry.key;
                      final orderListState = entry.value;
                      return AccountOrderListView(
                        margin: 11.gw,
                        key: Key("trading_center_${type.tradingCenterKey}"),
                        tradingAccountType: cubit.state.tradeForm.currentContract != null
                            ? TradingAccountType.Contract
                            : TradingAccountType.Spot,
                        marketCategory: widget.security.marketCategory,
                        contractModel: cubit.state.tradeForm.currentContract,
                        orderType: type,
                        orderListState: orderListState,
                        onVisibleRecordsChanged: (visibleIds) {
                          cubit.updateVisibleRecords(type, visibleIds);
                        },
                        onSellSuccess: () {
                          cubit.refreshAllOrderLists();
                        },
                      );
                    }).toList(),
                    pageIndex: pageIndex,
                    onPageChanged: (index) => cubit.onChangeCurrentOrderListType(keys[index]),
                  ),
                ));
          }),
    );
  }

  Widget _buildTradingSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(11.gw, 12.gw, 11.gw, 12.gw),
      padding: EdgeInsets.symmetric(horizontal: 11.gw, vertical: 12.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(10.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10.gw,
            spreadRadius: 2.gw,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        spacing: 8.gw,
        children: [
          if (!widget.security.isIndexTrading) ...[
            /// 价格模块
            StockPriceSection(marketCategory: widget.security.marketCategory),

            /// 五档交易模块
            _buildOrderBookSection(context),
          ],

          /// 交易表单模块
          BlocSelector<TradingCenterV2Cubit, TradingCenterV2State, bool>(
              selector: (state) => state.tradeForm.currentContract != null,
              builder: (context, isContractTrading) {
                return TradingFormSection(
                  security: widget.security,
                  isContractTrading: isContractTrading,
                );
              }),
        ],
      ),
    );
  }

  Widget _buildOrderBookSection(BuildContext context) {
    return BlocSelector<TradingCenterV2Cubit, TradingCenterV2State,
        ({DepthQuote? depthQuote, DataStatus status, StockOrderLevelType levelType, String? currency})>(
      selector: (state) => (
        depthQuote: state.depthQuote,
        status: state.depthQuoteStatus,
        levelType: state.stockOrderLevelType,
        currency: state.stockInfo?.currency,
      ),
      builder: (context, state) {
        final cubit = context.read<TradingCenterV2Cubit>();
        final marketCategory = widget.security.marketCategory;
        final enableLevelSelector = marketCategory.code < 3;

        return StockOrderBookSection(
          marketCategory: marketCategory,
          currency: state.currency ?? '',
          depthQuote: state.depthQuote,
          status: state.status,
          levelType: state.levelType,
          enableLevelSelector: enableLevelSelector,
          onLevelTypeChanged: enableLevelSelector ? cubit.onChangeStockOrderLevelType : null,
          onTapAsk: (item) {
            // 点击档位设置买入价
            if (item.price > 0) {
              // 切换为限价单
              cubit.onChangeOrderType(OrderPriceType.limit);

              // 设置价格为点击的档位价格
              cubit.onInputPriceChanged(item.price);

              // 设置为半仓（最后设置，避免被价格变化清空）
              cubit.onFractionChanged(OrderFraction.oneHalf);
            }
          },
        );
      },
    );
  }
}
