import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class RankTileShimmer extends StatelessWidget {
  const RankTileShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.gw, horizontal: 8.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: context.theme.cardColor,
      ),
      child: Row(
        spacing: 12.gw,
        children: [
          // Avatar placeholder
          ShimmerWidget(
            width: 50.gw,
            height: 50.gw,
            radius: 50.gw / 2,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4.gw,
            children: [
              // Nickname placeholder
              ShimmerWidget(
                width: 100.gw,
                height: 16.gw,
                radius: 4,
              ),
              // Subtext placeholder
              ShimmerWidget(
                width: 60.gw,
                height: 12.gw,
                radius: 4,
              ),
            ],
          ),
          Spacer(),
          Column(
            spacing: 4.gw,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Percentage container placeholder
              ShimmerWidget(
                width: 60.gw,
                height: 24.gw,
                radius: 7.gr,
              ),
              // Amount placeholder
              ShimmerWidget(
                width: 80.gw,
                height: 12.gw,
                radius: 4,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
