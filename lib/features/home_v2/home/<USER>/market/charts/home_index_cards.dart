import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/market/index_stock_card_model.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/trade_navigation_helper.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import 'index_kline_chart.dart';

/// 首页指数卡片组件（带K线图，GP样式）
class HomeIndexCards extends StatefulWidget {
  final bool isFromHome;

  const HomeIndexCards({super.key, this.isFromHome = false});

  @override
  State<HomeIndexCards> createState() => _HomeIndexCardsState();
}

class _HomeIndexCardsState extends State<HomeIndexCards> {
  int _firstVisibleItemIndex = 0;
  static const double _cardHeight = 170.0;
  static const double _graphHeight = 70.0;
  static const double _graphWidth = 120.0;
  final double _itemWidth = 0.32.gsw - 4.gw; // Exactly 1/3 of screen width
  final double _itemSpacing = 5.gw;

  final PageController _pageController = PageController(
    viewportFraction: 0.33,
    initialPage: 0,
  );

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        context.read<IndexTradeCubit>().updateAnimate();
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<IndexTradeCubit>();

    if (cubit.state.indexStockConfigList.isEmpty) return SizedBox.shrink();
    return BlocListener<IndexTradeCubit, IndexTradeState>(
      listenWhen: (previous, current) => previous.animate != current.animate,
      listener: (context, state) {
        if (widget.isFromHome) return;
        if (state.animate) {
          _pageController.animateToPage(
            state.selectedIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          context.read<IndexTradeCubit>().updateAnimate(animate: false);
          setState(() {
            _firstVisibleItemIndex = state.selectedIndex;
          });
        }
      },
      child: BlocBuilder<IndexTradeCubit, IndexTradeState>(
        bloc: getIt<IndexTradeCubit>(),
        builder: (context, state) {
          int itemCount = state.indexStocks.length;
          if (state.status == DataStatus.loading) {
            return _buildLoading();
          }

          if (state.status.isFailed || itemCount == 0) {
            return const Center(child: TableEmptyWidget());
          }

          return Column(
            children: [
              Container(
                height: _cardHeight.gw,
                padding: EdgeInsets.symmetric(horizontal: 0, vertical: 5.gw),
                child: PageView.builder(
                  key: PageStorageKey('visual_graph//${widget.isFromHome}'),
                  controller: _pageController,
                  itemCount: itemCount,
                  onPageChanged: (index) {
                    setState(() {
                      _firstVisibleItemIndex = index;
                    });
                  },
                  pageSnapping: true,
                  padEnds: false,
                  // physics: const CustomPageViewScrollPhysics(),
                  itemBuilder: (context, i) => Padding(
                    padding: EdgeInsets.symmetric(horizontal: _itemSpacing / 2),
                    child: _buildStockCard(context, state, i),
                  ),
                ),
              ),
              if (itemCount > 0) _buildIndicators(itemCount - 2, context),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStockCard(BuildContext context, IndexTradeState state, int i) {
    final data = state.indexStocks;
    final isSelected = i == state.selectedIndex;
    final stockInfoData = data[i].stockInfo;

    final marketCategory = MarketCategory.fromSecurity(stockInfoData.market, '1');
    final market = marketCategory.marketType ?? '';
    final stockData = IndexStockCardModel.fromStockInfoWithSpots(context, data[i], market);

    return GestureDetector(
      onTap: () => _handleCardTap(data, i),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        width: _itemWidth,
        padding: EdgeInsets.symmetric(vertical: 5.gw),
        margin: _calculateCardMargin(isSelected, i),
        decoration: _buildCardDecoration(context, isSelected),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          spacing: 4,
          children: [
            SizedBox(height: 3.gw),
            // Stock name
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.gw),
              child: Text(
                stockData.name,
                style: context.textTheme.primary.w700.copyWith(color: stockData.textColor),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Graph
            IndexKlineChart(
              spots: stockData.spots ?? [],
              textColor: stockData.textColor,
              lineColor: stockData.lineColor ?? stockData.textColor,
              width: _graphWidth,
              height: _graphHeight,
              market: market,
            ),
            SizedBox(height: 3.gw),
            // Price
            FlipText(
              stockData.price,
              style: context.textTheme.primary.w600.copyWith(
                color: stockData.textColor,
              ),
              fractionDigits: 3,
            ),
            // Change and percentage
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              spacing: 4,
              children: [
                // Price change
                FlipText(
                  stockData.change,
                  prefix: stockData.change >= 0 ? '+' : '',
                  style: context.textTheme.primary.fs11.copyWith(
                    color: stockData.textColor,
                  ),
                ),
                // Percentage change
                FlipText(
                  stockData.gainPercentage,
                  prefix: stockData.gainPercentage >= 0 ? '+' : '',
                  suffix: '%',
                  style: context.textTheme.primary.fs11.w500.copyWith(
                    color: stockData.textColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Calculate card margin based on selection state and position
  EdgeInsets _calculateCardMargin(bool isSelected, int index) {
    return EdgeInsets.only(
      right: isSelected && !widget.isFromHome ? 0.gw : 1.gw,
      left: index == 0 ? 4.gw : 1.gw,
      bottom: isSelected && !widget.isFromHome ? 0 : 2.gw,
      top: isSelected && !widget.isFromHome ? 0 : 2.gw,
    );
  }

  /// Build card decoration with proper styling
  BoxDecoration _buildCardDecoration(BuildContext context, bool isSelected) {
    return BoxDecoration(
      color: context.theme.cardColor,
      borderRadius: BorderRadius.circular(12),
      border: isSelected && !widget.isFromHome ? Border.all(color: context.theme.primaryColor, width: 2.gw) : null,
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.15),
          blurRadius: 2,
          offset: const Offset(0, 1),
        ),
      ],
    );
  }

  void _handleCardTap(List<IndexStockInfo> data, int i) {
    if (widget.isFromHome) {
      AuthUtils.verifyAuth(() {
        final stockInfo = data[i].stockInfo;
        TradeNavigationHelper.goToTradingCenterV2(
          context,
          security: stockInfo.instrumentInfo,
          tabType: TradeTabType.Quotes,
          isIndexTradingOverride: true,
        );
      });
    }
    context.read<IndexTradeCubit>().updateSelectedIndex(i);
  }

  Widget _buildIndicators(int itemCount, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          itemCount,
          (index) => AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: index == _firstVisibleItemIndex ? 12 : 6,
            height: 6,
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(3),
              color: index == _firstVisibleItemIndex ? Theme.of(context).primaryColor : Colors.grey.withNewOpacity(0.3),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoading() {
    final double itemWidth = 0.32.gsw - 4.gw;
    return SizedBox(
      height: 180.gw,
      child: ListView.separated(
        itemCount: 3,
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 6),
        scrollDirection: Axis.horizontal,
        separatorBuilder: (_, __) => const SizedBox(width: 8),
        itemBuilder: (_, __) => ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: ShimmerWidget(
            height: 190.gw,
            width: itemWidth,
          ),
        ),
      ),
    );
  }
}
