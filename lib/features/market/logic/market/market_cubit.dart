import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/market.dart';
import 'package:gp_stock_app/core/models/entities/market/market_plate_list.dart';
import 'package:gp_stock_app/core/utils/extension/future_list_extensions.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';
import 'package:gp_stock_app/core/models/entities/market/plate_info_list.dart';

import 'package:gp_stock_app/features/account_v2/0_home/domain/models/stock_plate.dart';

part 'market_state.dart';

@injectable
class MarketCubit extends Cubit<MarketState> {
  MarketCubit() : super(const MarketState());
  Timer? _pollTimer;

  void init({bool isRefresh = false}) {
    _startPolling();
    if (isRefresh) {
      emit(state.copyWith(
        plateFetchStatusA: DataStatus.idle,
        gainDistributionFetchStatus: DataStatus.idle,
        gainDistributionList: null,
        plateList: null,
        tableData: null,
        tableFetchStatus: DataStatus.idle,
        pageNumber: 1,
      ));
    }

    fetchPlateList();
    fetchTableData();
    fetchGainDistributionList();
  }

  final plateListSortField = 'gain';

  void _startPolling() {
    _pollTimer?.cancel();
    _pollTimer = Timer.periodic(const Duration(milliseconds: 3500), (_) => pollMarket());
  }

  @override
  Future<void> close() {
    _pollTimer?.cancel();
    return super.close();
  }

  void pollMarket() {
    final currentMarket = state.selectedMarketCategory.market;
    final isCurrentMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(currentMarket);
    if (isCurrentMarketOpen) {
      fetchPlateList(isPolling: true);
      fetchGainDistributionList();
      updateTableTab(state.selectedTableTab, isLive: true);
    }
  }

  void fetchPlateList({
    bool isLoadMore = false,
    String? field,
    String? order,
    bool isPolling = false,
  }) async {
    if (state.hasReachedMax ||
        state.plateFetchStatusA == DataStatus.loading ||
        state.paginationStatus == DataStatus.loading) {
      return;
    }
    if (!isLoadMore && !isPolling) {
      emit(state.copyWith(plateFetchStatusA: DataStatus.loading, pageNumber: 1, hasReachedMax: false));
    } else {
      emit(state.copyWith(paginationStatus: DataStatus.loading));
    }
    int page = isLoadMore ? state.pageNumber + 1 : state.pageNumber;
    try {
      final result = await MarketApi.fetchSectorList(
        category: state.selectedMarketCategory,
        pageNum: page,
        field: state.sortByChangePlateListAsc == null ? null : plateListSortField,
        order: state.sortByChangePlateListAsc == null
            ? null
            : (state.sortByChangePlateListAsc == SortType.ASC ? 'ASC' : 'DESC'),
      );

      if (result == null) {
        emit(state.copyWith(plateFetchStatusA: DataStatus.failed));
        return;
      }

      if (isLoadMore) {
        final mergedList = PlateInfoList(
          current: result.current,
          total: result.total,
          hasNext: result.hasNext,
          records: [...state.plateList?.records ?? [], ...result.records],
        );
        emit(state.copyWith(
          plateFetchStatusA: DataStatus.success,
          plateList: mergedList,
          pageNumber: page,
          paginationStatus: DataStatus.success,
          hasReachedMax: result.records.isEmpty,
        ));
      } else {
        emit(state.copyWith(
          plateFetchStatusA: DataStatus.success,
          plateList: result,
          pageNumber: page,
          paginationStatus: DataStatus.success,
          hasReachedMax: result.records.isEmpty,
        ));
      }
    } catch (e) {
      emit(state.copyWith(plateFetchStatusA: DataStatus.failed));
    }
  }

  Future<List<int>> fetchGainDistributionList() async {
    emit(state.copyWith(gainDistributionFetchStatus: DataStatus.loading));
    try {
      final result = await MarketApi.fetchGainDistributionList(category: state.selectedMarketCategory);
      emit(
        state.copyWith(
          gainDistributionFetchStatus: DataStatus.success,
          gainDistributionList: result,
        ),
      );
      return result ?? [];
    } catch (e) {
      emit(state.copyWith(gainDistributionFetchStatus: DataStatus.failed));
      rethrow;
    }
  }


  Future fetchTableData({
    bool isLoadMore = false,
    bool isHome = false,
    bool isLive = false,
    int pageSize = 20,
    bool skipLoading = false,
  }) async {
    if (!isLive && !skipLoading) emit(state.copyWith(tableFetchStatus: DataStatus.loading));
    if (isLoadMore) emit(state.copyWith(isPaginating: true));
    try {
      // Following H5 app approach: increase pageSize instead of page number for load more
      // Always use page 1 and adjust pageSize based on current data length (API not providing page number details)
      final int adjustedPageSize;

      if (isLive) {
        // For polling/live updates, use the current pageSize from state
        adjustedPageSize = state.currentPageSize;
      } else if (isLoadMore) {
        // For load more, increase pageSize by adding more items
        adjustedPageSize = state.currentPageSize + pageSize;
      } else {
        // For initial load, use the provided pageSize
        adjustedPageSize = pageSize;
      }

      final result = await MarketApi().fetchMarketPlateList(
        marketCategory: state.selectedMarketCategory,
        stockPlate: state.selectedStockPlate,
        sortType: state.sortType,
        order: state.order,
        pageNum: 1, // Always use page 1
        isHome: isHome,
        pageSize: adjustedPageSize,
      );

      if (result == null) {
        emit(state.copyWith(tableFetchStatus: DataStatus.failed, isPaginating: false));
        return;
      }

      if (isLoadMore) {
        final mergedList = state.tableData?.list.mergeDedupKeepFirst(result.list, (item) => item.name) ?? result.list;
        emit(
          state.copyWith(
            tableData: MarketPlateList(
              list: mergedList,
              totalNum: result.totalNum,
            ),
            tableFetchStatus: DataStatus.success,
            isPaginating: false,
            currentPageSize: adjustedPageSize,
          ),
        );
      } else {
        emit(
          state.copyWith(
            tableFetchStatus: DataStatus.success,
            tableData: result,
            isPaginating: false,
            currentPageSize: adjustedPageSize,
          ),
        );
      }
    } on Exception {
      emit(state.copyWith(tableFetchStatus: DataStatus.failed, isPaginating: false));
    }
  }

  void updateTableTab(
    int tab, {
    bool isLive = false,
  }) {
    // Only reset pageSize when it's not a live/polling call
    if (isLive) {
      emit(state.copyWith(selectedTableTab: tab)); // Don't reset pageSize for polling
    } else {
      emit(state.copyWith(selectedTableTab: tab, currentPageSize: 20)); // Reset pageSize when switching tabs
    }
    fetchTableData(isLive: isLive);
  }

  void updateMarketCategory(MarketCategory category, {bool isHome = false, bool skipLoading = false}) {
    if (category == state.selectedMarketCategory && !isHome) return;
    // 使用 StockPlate 的静态方法获取默认板块 / Use static method to get default plate
    final defaultPlate = StockPlate.defaultByCategory(category);
    emit(
      state.copyWith(
        selectedMarketCategory: category,
        selectedStockPlate: defaultPlate,
        currentPageSize: 20,
      ),
    );
    fetchGainDistributionList();
    fetchPlateList();
    fetchTableData(isHome: isHome, skipLoading: skipLoading);
  }

  void updateStockPlate(StockPlate plate) {
    if (plate == state.selectedStockPlate) return;
    emit(state.copyWith(
      selectedStockPlate: plate,
      selectedMarketCategory: plate.category,
      currentPageSize: 20,
    ));
    fetchTableData();
  }

  void updateMainHeaderTab(MarketSectionTab tab) => emit(state.copyWith(selectedMarketSectionTab: tab));

  void handleSortByChange() {
    final nextSortType = state.sortByChangeAsc == null
        ? SortType.ASC
        : state.sortByChangeAsc == SortType.ASC
            ? SortType.DESC
            : null;

    emit(state.copyWith(
      resetSortByPrice: true,
      sortByChangeAsc: nextSortType,
      resetSortByChange: nextSortType == null,
      sortType: nextSortType == null ? 0 : 2,
      order: nextSortType == null ? 'DESC' : (nextSortType == SortType.ASC ? 'ASC' : 'DESC'),
    ));

    fetchTableData();
  }

  void handleSortByChangePlateList() {
    final nextSortType = state.sortByChangePlateListAsc == null
        ? SortType.ASC
        : state.sortByChangePlateListAsc == SortType.ASC
            ? SortType.DESC
            : null;

    emit(state.copyWith(
      sortByChangePlateListAsc: nextSortType,
      resetSortByChangePlateList: nextSortType == null,
    ));

    fetchPlateList(
      field: plateListSortField,
      order: nextSortType == null ? null : (nextSortType == SortType.ASC ? 'ASC' : 'DESC'),
    );
  }

  void handleSortByPrice() {
    final nextSortType = state.sortByPriceAsc == null
        ? SortType.ASC
        : state.sortByPriceAsc == SortType.ASC
            ? SortType.DESC
            : null;

    emit(state.copyWith(
      resetSortByChange: true,
      sortByPriceAsc: nextSortType,
      resetSortByPrice: nextSortType == null,
      sortType: nextSortType == null ? 0 : 1,
      order: nextSortType == null ? 'DESC' : (nextSortType == SortType.ASC ? 'ASC' : 'DESC'),
    ));

    fetchTableData();
  }

}
