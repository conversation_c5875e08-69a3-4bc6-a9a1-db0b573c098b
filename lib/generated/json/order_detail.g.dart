import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/trade/order_detail.dart';
import 'package:gp_stock_app/core/utils/contract_utils.dart';


OrderDetail $OrderDetailFromJson(Map<String, dynamic> json) {
  return OrderDetail(cancelTime: jsonConvert.convert<String>(json['cancelTime']) ?? '',
      chargeDetailList: jsonConvert.convertListNotNull<OrderDetailChargeDetailList>(json['chargeDetailList']) ??
          <OrderDetailChargeDetailList>[],
      contractId: jsonConvert.convert<int>(json['contractId']) ?? 0,
      contractType: jsonConvert.convert<int>(json['contractType']) ?? 0,
      costPrice: jsonConvert.convert<int>(json['costPrice']) ?? 0,
      currency: jsonConvert.convert<String>(json['currency']) ?? '',
      dealNum: jsonConvert.convert<int>(json['dealNum']) ?? 0,
      dealPrice: jsonConvert.convert<double>(json['dealPrice']) ?? 0.0,
      dealTime: jsonConvert.convert<String>(json['dealTime']) ?? '',
      direction: jsonConvert.convert<int>(json['direction']) ?? 0,
      id: jsonConvert.convert<int>(json['id']) ?? 0,
      market: jsonConvert.convert<String>(json['market']) ?? '',
      multiple: jsonConvert.convert<int>(json['multiple']) ?? 0,
      periodType: jsonConvert.convert<int>(json['periodType']) ?? 0,
      priceType: jsonConvert.convert<int>(json['priceType']) ?? 0,
      securityType: jsonConvert.convert<String>(json['securityType']) ?? '',
      settleType: jsonConvert.convert<int>(json['settleType']) ?? 0,
      status: jsonConvert.convert<int>(json['status']) ?? 0,
      stockPrice: jsonConvert.convert<int>(json['stockPrice']) ?? 0,
      symbol: jsonConvert.convert<String>(json['symbol']) ?? '',
      symbolName: jsonConvert.convert<String>(json['symbolName']) ?? '',
      tradeFee: jsonConvert.convert<double>(json['tradeFee']) ?? 0.0,
      tradeNum: jsonConvert.convert<int>(json['tradeNum']) ?? 0,
      tradePrice: jsonConvert.convert<double>(json['tradePrice']) ?? 0.0,
      tradeRate: jsonConvert.convert<double>(json['tradeRate']) ?? 0.0,
      tradeTime: jsonConvert.convert<String>(json['tradeTime']) ?? '',
      tradeType: jsonConvert.convert<int>(json['tradeType']) ?? 0,
      transactionAmount: jsonConvert.convert<int>(json['transactionAmount']) ?? 0,
      type: jsonConvert.convert<int>(json['type']) ?? 0,
      winAmount: jsonConvert.convert<int>(json['winAmount']) ?? 0);
}

Map<String, dynamic> $OrderDetailToJson(OrderDetail entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['cancelTime'] = entity.cancelTime;
  data['chargeDetailList'] = entity.chargeDetailList.map((v) => v.toJson()).toList();
  data['contractId'] = entity.contractId;
  data['contractType'] = entity.contractType;
  data['costPrice'] = entity.costPrice;
  data['currency'] = entity.currency;
  data['dealNum'] = entity.dealNum;
  data['dealPrice'] = entity.dealPrice;
  data['dealTime'] = entity.dealTime;
  data['direction'] = entity.direction;
  data['id'] = entity.id;
  data['market'] = entity.market;
  data['multiple'] = entity.multiple;
  data['periodType'] = entity.periodType;
  data['priceType'] = entity.priceType;
  data['securityType'] = entity.securityType;
  data['settleType'] = entity.settleType;
  data['status'] = entity.status;
  data['stockPrice'] = entity.stockPrice;
  data['symbol'] = entity.symbol;
  data['symbolName'] = entity.symbolName;
  data['tradeFee'] = entity.tradeFee;
  data['tradeNum'] = entity.tradeNum;
  data['tradePrice'] = entity.tradePrice;
  data['tradeRate'] = entity.tradeRate;
  data['tradeTime'] = entity.tradeTime;
  data['tradeType'] = entity.tradeType;
  data['transactionAmount'] = entity.transactionAmount;
  data['type'] = entity.type;
  data['winAmount'] = entity.winAmount;
  return data;
}

extension OrderDetailExtension on OrderDetail {
  OrderDetail copyWith({
    String? cancelTime,
    List<OrderDetailChargeDetailList>? chargeDetailList,
    int? contractId,
    int? contractType,
    int? costPrice,
    String? currency,
    int? dealNum,
    double? dealPrice,
    String? dealTime,
    int? direction,
    int? id,
    String? market,
    int? multiple,
    int? periodType,
    int? priceType,
    String? securityType,
    int? settleType,
    int? status,
    int? stockPrice,
    String? symbol,
    String? symbolName,
    double? tradeFee,
    int? tradeNum,
    double? tradePrice,
    double? tradeRate,
    String? tradeTime,
    int? tradeType,
    int? transactionAmount,
    int? type,
    int? winAmount,
  }) {
    return OrderDetail(cancelTime: cancelTime ?? this.cancelTime,
        chargeDetailList: chargeDetailList ?? this.chargeDetailList,
        contractId: contractId ?? this.contractId,
        contractType: contractType ?? this.contractType,
        costPrice: costPrice ?? this.costPrice,
        currency: currency ?? this.currency,
        dealNum: dealNum ?? this.dealNum,
        dealPrice: dealPrice ?? this.dealPrice,
        dealTime: dealTime ?? this.dealTime,
        direction: direction ?? this.direction,
        id: id ?? this.id,
        market: market ?? this.market,
        multiple: multiple ?? this.multiple,
        periodType: periodType ?? this.periodType,
        priceType: priceType ?? this.priceType,
        securityType: securityType ?? this.securityType,
        settleType: settleType ?? this.settleType,
        status: status ?? this.status,
        stockPrice: stockPrice ?? this.stockPrice,
        symbol: symbol ?? this.symbol,
        symbolName: symbolName ?? this.symbolName,
        tradeFee: tradeFee ?? this.tradeFee,
        tradeNum: tradeNum ?? this.tradeNum,
        tradePrice: tradePrice ?? this.tradePrice,
        tradeRate: tradeRate ?? this.tradeRate,
        tradeTime: tradeTime ?? this.tradeTime,
        tradeType: tradeType ?? this.tradeType,
        transactionAmount: transactionAmount ?? this.transactionAmount,
        type: type ?? this.type,
        winAmount: winAmount ?? this.winAmount);
  }
}

OrderDetailChargeDetailList $OrderDetailChargeDetailListFromJson(Map<String, dynamic> json) {
  return OrderDetailChargeDetailList(fee: jsonConvert.convert<double>(json['fee']) ?? 0.0,
      feeName: jsonConvert.convert<String>(json['feeName']) ?? '');
}

Map<String, dynamic> $OrderDetailChargeDetailListToJson(OrderDetailChargeDetailList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['fee'] = entity.fee;
  data['feeName'] = entity.feeName;
  return data;
}

extension OrderDetailChargeDetailListExtension on OrderDetailChargeDetailList {
  OrderDetailChargeDetailList copyWith({
    double? fee,
    String? feeName,
  }) {
    return OrderDetailChargeDetailList(fee: fee ?? this.fee, feeName: feeName ?? this.feeName);
  }
}