import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AIChatState extends Equatable {
  final DataStatus aiChatStatus;
  final List<AIAnswer> aiChatData;
  final String? aiChatError;

  const AIChatState({
    this.aiChatStatus = DataStatus.idle,
    this.aiChatData = const [],
    this.aiChatError,
  });

  @override
  List<Object?> get props => [
        aiChatStatus,
        aiChatData,
        aiChatError,
      ];

  AIChatState copyWith({
    DataStatus? aiChatStatus,
    List<AIAnswer>? aiChatData,
    String? aiChatError,
  }) {
    return AIChatState(
      aiChatStatus: aiChatStatus ?? this.aiChatStatus,
      aiChatData: aiChatData ?? this.aiChatData,
      aiChatError: aiChatError ?? this.aiChatError,
    );
  }
}
