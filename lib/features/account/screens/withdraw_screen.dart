import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_bank_entity_list.dart';
import 'package:gp_stock_app/core/models/entities/wallet/withdraw_config_entity.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/add_bank/add_bank_cubit.dart';
import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/otp/otp_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_state.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_cubit.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_state.dart';
import 'package:gp_stock_app/features/account/screens/add_bank_screen.dart';
import 'package:gp_stock_app/features/account/widgets/withdraw_password_dialog.dart';
import 'package:gp_stock_app/features/profile/widgets/account_selection_bottom_sheet.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_input_formatter/no_leading_zero_int_formatter.dart';

import '../../../shared/routes/app_router.dart';
import '../../../shared/widgets/buttons/custom_material_button.dart';

class WithdrawScreen extends StatefulWidget {
  const WithdrawScreen({super.key, this.showAppBar = true});
  final bool showAppBar;

  @override
  State<WithdrawScreen> createState() => _WithdrawScreenState();
}

class _WithdrawScreenState extends State<WithdrawScreen> {
  final amountController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.showAppBar
          ? AppBar(
              title: Text('bankCardWithdraw'.tr()),
              actions: [
                IconButton(
                  icon: Icon(LucideIcons.notepad_text),
                  onPressed: () {
                    getIt<NavigatorService>().push(AppRouter.routeWithdrawRecords);
                  },
                ),
              ],
            )
          : null,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 10),
              BlocBuilder<UserBankListCubit, UserBankListState>(
                builder: (context, state) {
                  return GestureDetector(
                    onTap: () {
                      _showAccountSelectionBottomSheet(context);
                    },
                    child: ShadowBox(
                      padding: EdgeInsets.all(16.gw),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 12.gw,
                        children: [
                          Text(
                            'selectWithdrawBank'.tr(),
                            style: context.textTheme.title.w600,
                          ),
                          TextFieldWidget(
                            suffixIcon: Icon(
                              Icons.keyboard_arrow_down_sharp,
                              size: 24.gw,
                            ),
                            readOnly: true,
                            onTap: () => _showAccountSelectionBottomSheet(context),
                            controller: TextEditingController(
                                text: state.selectedBank != null
                                    ? '${state.selectedBank?.bankFullName ?? ''}(${state.selectedBank?.bankAccount ?? ''})'
                                    : null),
                          )
                        ],
                      ),
                    ),
                  );
                },
              ),
              SizedBox(height: 10),
              BlocBuilder<UserBankListCubit, UserBankListState>(
                builder: (context, state) {
                  return ShadowBox(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 10,
                      children: [
                        SizedBox(height: 4),
                        Row(
                          children: [
                            SizedBox(
                              width: 0.4.gsw,
                              child: Text(
                                'cardholder'.tr(),
                                style: context.textTheme.regular.copyWith(
                                  color: context.colorTheme.textRegular,
                                ),
                              ),
                            ),
                            Text(
                              state.selectedBank?.realName ?? '',
                              style: context.textTheme.regular.copyWith(
                                color: context.colorTheme.textRegular,
                              ),
                            ),
                          ],
                        ),
                        Divider(
                          color: context.theme.dividerColor,
                        ),
                        Row(
                          children: [
                            SizedBox(
                              width: 0.4.gsw,
                              child: Text(
                                'cardNumber'.tr(),
                                style: context.textTheme.regular.copyWith(
                                  color: context.colorTheme.textRegular,
                                ),
                              ),
                            ),
                            Text(
                              state.selectedBank?.bankAccount ?? '',
                              style: context.textTheme.regular.copyWith(
                                color: context.colorTheme.textRegular,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 4),
                      ],
                    ),
                  );
                },
              ),
              SizedBox(height: 16),
              ShadowBox(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "withdrawalAmount".tr(),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: context.colorTheme.textPrimary,
                      ),
                    ),
                    SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                            child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            color: context.theme.scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: TextField(
                            controller: amountController,
                            onChanged: (value) => context.read<WithdrawalCubit>().updateWithdrawAmount(value),
                            decoration: InputDecoration(
                              hintText: 'enterWithdrawAmount'.tr(),
                              hintStyle: TextStyle(color: context.colorTheme.textRegular),
                              border: InputBorder.none,
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              /// 正整数且第一位不为0
                              NoLeadingZeroIntFormatter(),
                            ],
                          ),
                        )),
                        SizedBox(width: 8),
                        Text('¥', style: TextStyle(fontSize: 18, color: context.colorTheme.textRegular)),
                      ],
                    ),
                    SizedBox(height: 10),
                    Row(
                      children: [
                        Text("availableBalance".tr(), style: context.textTheme.title),
                        SizedBox(width: 8),
                        BlocSelector<UserCubit, UserState, String>(
                          selector: (state) => state.accountInfo?.usableCash.toString() ?? '0.00',
                          builder: (context, balance) {
                            return Text(balance, style: context.textTheme.regular.w600.ffAkz);
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: 12),
                    BlocSelector<WithdrawalCubit, WithdrawalState, WithdrawConfigEntity?>(
                      selector: (state) => state.withdrawalConfig,
                      builder: (context, state) {
                        if (state == null) return const SizedBox.shrink();
                        final minWithdrawalAmount = state.minWithdrawalAmount;
                        final maxWithdrawalAmount = state.maxWithdrawalAmount;
                        return Text(
                          '*${'thatChannelMinimumDeposit'.tr()} $minWithdrawalAmount, ${'thatChannelMaximumDeposit'.tr()} $maxWithdrawalAmount',
                          style: TextStyle(fontSize: 12, color: context.colorTheme.textRegular),
                        );
                      },
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: BlocListener<WithdrawalCubit, WithdrawalState>(
                      listenWhen: (previous, current) => previous.withdrawStatus != current.withdrawStatus,
                      listener: (context, state) {
                        if (state.withdrawStatus.isSuccess) {
                          amountController.clear();
                        }
                      },
                      child: BlocSelector<WithdrawalCubit, WithdrawalState,
                          ({WithdrawConfigEntity? config, bool isFormValid, bool isLoading, double? usableCash})>(
                        selector: (state) => (
                          config: state.withdrawalConfig,
                          isFormValid: state.isFormValid ?? false,
                          isLoading: state.withdrawStatus.isLoading,
                          usableCash: getIt<UserCubit>().state.accountInfo?.usableCash,
                        ),
                        builder: (context, state) {
                          return CustomMaterialButton(
                            onPressed: state.isFormValid
                                ? () {
                                    final amount = double.tryParse(amountController.text) ?? 0;
                                    final config = state.config;
                                    final usableCash = state.usableCash ?? 0;

                                    if (config == null) return;

                                    if (amount < config.minWithdrawalAmount) {
                                      Helper.showFlutterToast(
                                          '${'thatChannelMinimumDeposit'.tr()} ${config.minWithdrawalAmount}',
                                          gravity: ToastGravity.CENTER);
                                      return;
                                    }

                                    if (amount > config.maxWithdrawalAmount) {
                                      Helper.showFlutterToast(
                                          '${'thatChannelMaximumDeposit'.tr()} ${config.maxWithdrawalAmount}',
                                          gravity: ToastGravity.CENTER);
                                      return;
                                    }

                                    if (amount > usableCash) {
                                      Helper.showFlutterToast('insufficientBalance'.tr(), gravity: ToastGravity.CENTER);
                                      return;
                                    }

                                    showDialog(
                                      context: context,
                                      builder: (_) => BlocProvider.value(
                                        value: context.read<WithdrawalCubit>(),
                                        child: WithdrawPasswordDialog(
                                          withdrawAmount: amountController.text,
                                          userBankId: context.read<UserBankListCubit>().state.selectedBank!.id,
                                        ),
                                      ),
                                    );
                                  }
                                : null,
                            isLoading: state.isLoading,
                            buttonText: 'submit'.tr(),
                            isEnabled: state.isFormValid,
                            color: context.theme.primaryColor,
                            borderColor: context.theme.primaryColor,
                            borderRadius: 5.gr,
                            textColor: Colors.white,
                            fontSize: 13.gr,
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),
              SupportWidget(),
            ],
          ),
        ),
      ),
    );
  }

  void _showAccountSelectionBottomSheet(BuildContext ctx) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: false,
      backgroundColor: Colors.transparent,
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: context.read<UserBankListCubit>(),
          ),
          BlocProvider.value(
            value: context.read<WithdrawalCubit>(),
          ),
        ],
        child: BlocBuilder<UserBankListCubit, UserBankListState>(
          builder: (context, state) {
            return AccountSelectionBottomSheet(
              headers: [('cardNo', TextAlign.left), ('selection', TextAlign.right)],
              onAddAccount: () {
                Navigator.of(context).pop();
                Navigator.push(
                  ctx,
                  MaterialPageRoute(
                    builder: (_) => MultiBlocProvider(
                      providers: [
                        BlocProvider.value(
                          value: ctx.read<BankListCubit>(),
                        ),
                        BlocProvider.value(
                          value: ctx.read<UserBankListCubit>(),
                        ),
                        BlocProvider(create: (context) => AddBankCubit()),
                        BlocProvider(create: (context) => OtpCubit()),
                      ],
                      child: AddBankScreen(),
                    ),
                  ),
                );
              },
              editMode: state.isEditMode,
              list: _buildAccountList(context),
              onManageClick: () {
                context.read<UserBankListCubit>().updateEditMode();
              },
              onInitState: () {
                context.read<UserBankListCubit>().updateEditMode(value: false);
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildAccountList(BuildContext context) {
    return BlocSelector<
        UserBankListCubit,
        UserBankListState,
        ({
          List<UserBankEntity> accounts,
          UserBankEntity? selectedAccount,
          bool isEditMode,
          ({int? id, DataStatus? status}) unbindWalletStatus
        })>(
      selector: (state) => (
        accounts: state.bankList ?? [],
        selectedAccount: state.selectedBank,
        isEditMode: state.isEditMode,
        unbindWalletStatus: (id: state.unbindBankStatus?.id, status: state.unbindBankStatus?.status)
      ),
      builder: (context, state) => Expanded(
          child: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: 10.gw),
        itemCount: state.accounts.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: context.colorTheme.border,
          thickness: 0.5,
        ),
        itemBuilder: (context, index) {
          final account = state.accounts[index];
          final isSelected = state.selectedAccount?.id == account.id;

          return _buildAccountItem(
            context,
            account: account,
            isSelected: isSelected,
            accountName: getIt<UserCubit>().currentUser?.realName ?? '',
            onAccountSelected: (account) {
              context.read<UserBankListCubit>().updateSelectedBank(account);
              context.read<WithdrawalCubit>().updateSelectedBank(account);
              Navigator.of(context).pop();
            },
            isEditMode: state.isEditMode,
            unbindWalletStatus: state.unbindWalletStatus,
          );
        },
      )),
    );
  }

  Widget _buildAccountItem(
    BuildContext context, {
    required UserBankEntity account,
    required bool isSelected,
    required String accountName,
    required Function(UserBankEntity?) onAccountSelected,
    required bool isEditMode,
    required ({int? id, DataStatus? status}) unbindWalletStatus,
  }) {
    final width = (1.gsw / 2) - (10.gw * 2);
    final style = context.textTheme.title;
    return InkWell(
      onTap: isEditMode ? null : () => onAccountSelected(account),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.gw),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(account.bankFullName, style: style, textAlign: TextAlign.center),
                  Text(account.bankAccount, style: style, textAlign: TextAlign.center),
                ],
              ),
            ),
            if (isEditMode)
              InkWell(
                onTap: () {
                  context.read<UserBankListCubit>().deleteUserBank(account.id);
                },
                child: unbindWalletStatus.id == account.id && unbindWalletStatus.status == DataStatus.loading
                    ? Container(
                        height: 18.gw,
                        alignment: Alignment.centerRight,
                        child: SizedBox(
                          width: 18.gw,
                          height: 18.gw,
                          child: CircularProgressIndicator(
                            color: context.colorTheme.stockRed,
                            strokeWidth: 1,
                          ),
                        ),
                      )
                    : Container(
                        height: 18.gw,
                        alignment: Alignment.centerRight,
                        child: Icon(Icons.delete_outlined, color: context.colorTheme.stockRed),
                      ),
              )
            else
              Container(
                alignment: Alignment.centerRight,
                child: Container(
                  width: 18.gw,
                  height: 18.gw,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? context.theme.primaryColor : Colors.transparent,
                    border: Border.all(
                      color: isSelected ? context.theme.primaryColor : context.colorTheme.border,
                      width: 1,
                    ),
                  ),
                  child: isSelected ? Icon(Icons.check, size: 10.gw, color: context.textTheme.secondary.color) : null,
                ),
              ),
            SizedBox(width: 15.gw),
          ],
        ),
      ),
    );
  }
}
