import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account/widgets/table_loading.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/order_list_state.dart';
import 'package:gp_stock_app/features/account_v2/spot/utils/account_cell_builder.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:scrollview_observer/scrollview_observer.dart';

class FTradeAcctPositionList extends StatefulWidget {
  final DataStatus dataStatus;
  final FTradeAcctOrderModel? orderModel;
  final RefreshController refreshCtrl;
  final Function(List<int> visibleIds)? onVisibleRecordsChanged; // 可见记录 ID 变化回调
  final VoidCallback onSellSuccess; // 卖出成功回调

  /// actionType=> 'refresh' 'loadMore' 'clickRecordCell'
  const FTradeAcctPositionList({
    super.key,
    required this.dataStatus,
    required this.orderModel,
    required this.refreshCtrl,
    this.onVisibleRecordsChanged,
    required this.onSellSuccess,
  });

  @override
  State<FTradeAcctPositionList> createState() => _FTradeAcctPositionListState();
}

class _FTradeAcctPositionListState extends State<FTradeAcctPositionList> {
  final ListObserverController _observerController = ListObserverController();

  @override
  void dispose() {
    _observerController.controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final status = widget.dataStatus;
    final orderModel = widget.orderModel;

    if (status == DataStatus.loading) {
      return const TableLoadingState();
    }

    if (orderModel == null || orderModel.records.isEmpty) {
      return const TableEmptyWidget();
    }

    // 由于使用 shrinkWrap，取前5个作为可见项（或全部如果少于5个）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.onVisibleRecordsChanged != null && orderModel.records.isNotEmpty) {
        final visibleIds = orderModel.records.take(5).map((e) => e.id).toList();
        widget.onVisibleRecordsChanged?.call(visibleIds);
        LogD("📋 [期货持仓] 可见ID: $visibleIds 总数: ${orderModel.records.length}");
      }
    });

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: orderModel.records.length,
      itemBuilder: (_, index) {
        final isLast = index == orderModel.records.length - 1;
        final data = orderModel.records[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 30.0,
            child: FadeInAnimation(
              child: Padding(
                  padding: EdgeInsets.only(bottom: isLast ? 0 : 16),
                  child: AccountCellBuilder.buildCell(
                    context,
                    category: MarketCategory.fromSecurity(data.market, data.securityType),
                    item: data,
                    index: index,
                    isLast: isLast,
                    isIndexTrading: false,
                    orderType: OrderListType.positions,
                    onSellSuccess: widget.onSellSuccess,
                  )),
            ),
          ),
        );
      },
    );
  }
}
