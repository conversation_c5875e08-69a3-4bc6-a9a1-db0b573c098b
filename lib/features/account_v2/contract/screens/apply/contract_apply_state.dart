part of 'contract_apply_cubit.dart';

/// 合约申请入口状态
class ContractApplyState extends Equatable {
  final DataStatus fetchStatus;
  final List<int>? openContractTypes;

  const ContractApplyState({
    this.fetchStatus = DataStatus.idle,
    this.openContractTypes,
  });

  @override
  List<Object?> get props => [fetchStatus, openContractTypes];

  ContractApplyState copyWith({
    DataStatus? fetchStatus,
    List<int>? openContractTypes,
  }) {
    return ContractApplyState(
      fetchStatus: fetchStatus ?? this.fetchStatus,
      openContractTypes: openContractTypes ?? this.openContractTypes,
    );
  }
}
