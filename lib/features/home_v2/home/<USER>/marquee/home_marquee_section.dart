import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/text/common_marquee_text.dart';
import 'package:html/parser.dart' as html_parser;

import '../home_section_style.dart';

/// 首页跑马灯区域（通用样式）
class HomeMarqueeSection extends StatelessWidget {
  final List<NotificationEntity> notificationList;
  final DataStatus status;
  final HomeSectionStyle style;

  const HomeMarqueeSection({
    super.key,
    required this.notificationList,
    required this.status,
    this.style = const HomeSectionStyle(),
  });

  @override
  Widget build(BuildContext context) {
    // kTemplateA (tempa) 和 kTemplateC (xyzq, dyzb) 不显示跑马灯
    if (AppConfig.instance.skinStyle == AppSkinStyle.kTemplateA ||
        AppConfig.instance.skinStyle == AppSkinStyle.kTemplateC) {
      return const SizedBox.shrink();
    }

    // 获取类型为 2 的通知（跑马灯通知）
    final marqueeNotificationList = notificationList.where((e) => e.type == 2).toList();
    if (marqueeNotificationList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: _getHeight(),
      margin: style.margin,
      padding: EdgeInsets.symmetric(horizontal: 13.gw),
      decoration: BoxDecoration(
        color: _getBackgroundColor(context),
        borderRadius: style.borderRadius,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildIcon(context),
          SizedBox(width: 6.gw),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(top: 2.gw),
              child: CommonMarqueeText(
                items: marqueeNotificationList
                    .map((e) => TextSpan(
                          text: html_parser.parse(e.content).body?.text ?? e.content,
                          style: _getTextStyle(context),
                        ))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  double? _getHeight() {
    return switch (AppConfig.instance.skinStyle) {
      _ when AppConfig.instance.flavor == Flavor.yhxt => 43.gw,
      AppSkinStyle.kGP => 43.gw,
      AppSkinStyle.kTemplateD => 54.gw,
      _ => null,
    };
  }

  Color _getBackgroundColor(BuildContext context) {
    switch (AppConfig.instance.flavor) {
      case Flavor.yhxt:
        return Colors.white.withValues(alpha: 0.5);
      default:
        return context.theme.cardColor;
    }
  }

  Widget _buildIcon(BuildContext context) {
    switch (AppConfig.instance.flavor) {
      case Flavor.yhxt:
        return IconHelper.loadAsset(
          'assets/images/icon_home_marquee.png',
          width: 18.gw,
          height: 18.gw,
          color: context.theme.primaryColor,
        );
      default:
        return switch (AppConfig.instance.skinStyle) {
          AppSkinStyle.kGP => SvgPicture.asset(
              Assets.soundUpIcon,
              width: 20.gw,
              height: 20.gw,
              colorFilter: ColorFilter.mode(
                context.theme.primaryColor,
                BlendMode.srcIn,
              ),
            ),
          AppSkinStyle.kTemplateD => IconHelper.loadAsset(
              'assets/svg/marquee.svg',
              width: 28,
              height: 28,
              color: context.theme.primaryColor,
            ),
          _ => Icon(
              Icons.volume_up_rounded,
              color: context.theme.primaryColor,
            ),
        };
    }
  }

  TextStyle _getTextStyle(BuildContext context) {
    switch (AppConfig.instance.flavor) {
      default:
        return context.textTheme.title;
    }
  }
}
