import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

/// 梯形 Tab 构建器 / Trapezoid tab builder
class TrapezoidTabBuilder {
  TrapezoidTabBuilder._();

  /// 构建梯形 Tabs / Build trapezoid tabs
  static Widget build(
    BuildContext context,
    List<String> data,
    int currentIndex,
    Function(int)? onTap,
    bool isScrollable,
  ) {
    final rowWidget = Row(
      children: data.asMap().entries.map((entry) {
        final index = entry.key;
        final label = entry.value;

        Widget child = ClipPath(
          clipper: index.isEven ? LeftTabClipper() : RightTabClipper(),
          child: Container(
            height: 48.gw,
            padding: EdgeInsets.symmetric(horizontal: 2.gw, vertical: 8.gw),
            color: currentIndex == index ? context.theme.primaryColor : context.colorTheme.tabInactiveBg,
            alignment: Alignment.center,
            child: Text(
              label,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(
                color: currentIndex == index ? Colors.white : context.theme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );

        child = GestureDetector(
          onTap: () => onTap?.call(index),
          child: child,
        );

        final padding = EdgeInsets.only(
          right: index < data.length - 1 ? 2.gw : 0,
          left: index != 0 && index.isEven ? 4.gw : 0.gw,
        );

        return isScrollable
            ? Padding(padding: padding, child: child)
            : Expanded(child: Padding(padding: padding, child: child));
      }).toList(),
    );

    return isScrollable
        ? SingleChildScrollView(scrollDirection: Axis.horizontal, child: rowWidget)
        : rowWidget;
  }
}

/// 左侧梯形裁剪器 / Left trapezoid clipper
class LeftTabClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final double width = size.width;
    final double height = size.height;

    final path_0 = Path();

    path_0.moveTo(0, height * 0.175); // M0 7
    path_0.cubicTo(
      0, height * 0.07835, // C0 3.13401
      width * 0.0212, 0, // 3.13401 0
      width * 0.0473, 0, // 7 0
    );
    path_0.lineTo(width * 0.9466, 0); // L140.09 0
    path_0.cubicTo(
      width * 0.99999, 0, // C144.924 0
      width * 1.002, height * 0.1196, // 148.303 4.78402
      width * 0.9914, height * 0.2335, // 146.687 9.34023
    );
    path_0.lineTo(width * 0.9288, height * 0.8835); // L137.464 35.3402
    path_0.cubicTo(
      width * 0.9225, height * 0.9533, // C136.473 38.1335
      width * 0.9037, height, // 133.83 40
      width * 0.8842, height, // 130.867 40
    );
    path_0.lineTo(width * 0.0473, height); // L6.99999 40
    path_0.cubicTo(
      width * 0, height, // C3.134 40
      0, height * 0.925, // 0 36.866
      0, height * 0.825, // 0 33
    );
    path_0.lineTo(0, height * 0.175); // L0 7
    path_0.close();

    return path_0;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
}

/// 右侧梯形裁剪器 / Right trapezoid clipper
class RightTabClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    double width = size.width;
    double height = size.height;

    return Path()
      ..moveTo(width, height * 0.825) // M148 33
      ..cubicTo(
        width, height * 0.9216, // C148 36.866
        width * 0.978, height, // 144.866 40
        width * 0.952, height, // 141 40
      )
      ..lineTo(width * 0.0535, height) // L7.91048 40
      ..cubicTo(
        width * 0.0208, height, // C3.07608 40
        width * -0.002, height * 0.8804, // -0.302979 35.216
        width * 0.0089, height * 0.7665, // 1.31325 30.6598
      )
      ..lineTo(width * 0.0712, height * 0.1165) // L10.5362 4.65977
      ..cubicTo(
        width * 0.0779, height * 0.0466, // C11.5271 1.86651
        width * 0.0957, 0, // 14.1696 0
        width * 0.1158, 0, // 17.1334 0
      )
      ..lineTo(width * 0.952, 0) // L141 0
      ..cubicTo(
        width * 0.999, 0, // C144.866 0
        width, height * 0.175, // 148 3.13401
        width, height * 0.175, // 148 7
      )
      ..lineTo(width, height * 0.825) // L148 33
      ..close();
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
}
