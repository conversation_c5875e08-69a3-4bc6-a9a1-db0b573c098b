import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:extra_hittest_area/extra_hittest_area.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/money_util/money_util.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/layout/expanded_row.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:popover/popover.dart';
import 'package:shimmer/shimmer.dart';

/// 持仓单元格操作类型 / Position cell action type
enum PositionCellAction {
  tap,        // 点击单元格 / Tap cell
  tpSL,       // 止盈止损 / Take profit & stop loss
  addMargin,  // 追加保证金 / Add margin
  sell,       // 卖出 / Sell
  quote,      // 行情 / Quote
  detail,     // 详情 / Detail
}

extension PositionCellActionExtension on PositionCellAction {
  /// 根据市场类型获取操作按钮的标题 / Get action button title based on market category
  String getTitle(MarketCategory marketCategory) {
    return switch (this) {
      PositionCellAction.tap => '',
      PositionCellAction.tpSL => 'take_profit_stop_loss'.tr(),
      PositionCellAction.addMargin => 'add_shares'.tr(),
      PositionCellAction.sell => marketCategory == MarketCategory.cnStocks
          ? 'sell'.tr()
          : 'closePosition'.tr(),
      PositionCellAction.quote => 'quote'.tr(),
      PositionCellAction.detail => 'detail'.tr(),
    };
  }
}

class AccountPositionCell extends StatelessWidget {
  final MarketCategory marketCategory;
  final FTradeAcctOrderRecords data;
  final int debugIndex;

  /// 统一的操作回调 / Unified action callback
  final void Function(PositionCellAction action)? onAction;

  const AccountPositionCell({
    super.key,
    required this.marketCategory,
    required this.data,
    required this.debugIndex,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    // 1. 一次性算好 TradeTypeOption 和 upColor
    final tradeOpt = TradeTypeOption.fromValue(data.tradeType);

    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(10.gw),
      ),
      margin: EdgeInsets.zero,
      child: Stack(
        children: [
          InkWell(
            onTap: onAction != null ? () => onAction!(PositionCellAction.tap) : null,
            child: Padding(
              padding: EdgeInsets.all(10.gw),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [

                  Wrap(
                    spacing: 4.gw,
                    children: [
                      SymbolChip(
                        name: tradeOpt.text,
                        chipColor: tradeOpt.color(context),
                      ),
                      SymbolChip(
                        name: 'openSymbol'.tr(),
                        chipColor: context.upColor,
                      ),
                      SymbolChip(
                        name: data.market.substring(0, 2),
                        chipColor: context.theme.primaryColor,
                      ),
                      Text(
                        data.symbolName,
                        style: context.textTheme.regular.fs12.w700.copyWith(
                          color: switch (AppConfig.instance.skinStyle) {
                            AppSkinStyle.kTemplateD => context.theme.primaryColor,
                            AppSkinStyle.kZangGolden => Color(0xFF2F4591),
                            _ => null
                          },
                          fontWeight: switch (AppConfig.instance.skinStyle) {
                            AppSkinStyle.kZangGolden => FontWeight.w400,
                            _ => null
                          },
                        ),
                      ),
                      Text(
                        '(${data.currency})',
                        style: context.textTheme.regular.fs13.w300.copyWith(
                          color: switch (AppConfig.instance.skinStyle) {
                            AppSkinStyle.kTemplateD => context.theme.primaryColor,
                            AppSkinStyle.kZangGolden => Color(0xFF2F4591),
                            _ => context.colorTheme.textRegular
                          },
                        ),
                      ),
                      Text(
                        '(${data.id})',
                        style: context.textTheme.regular.fs13.w300.copyWith(
                          color: switch (AppConfig.instance.skinStyle) {
                            AppSkinStyle.kTemplateD => context.theme.primaryColor,
                            AppSkinStyle.kZangGolden => Color(0xFF2F4591),
                            _ => context.colorTheme.textRegular
                          },
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 10.gw),

                  // 第一行数据
                  _PositionRow(
                    configs: [
                      _FieldConfig(
                        'available'.tr(), // 可用
                        data.restNum,
                      ),
                      _FieldConfig(
                        'currentPrice'.tr(), // 现价
                        data.stockPrice,
                        isCenter: true,
                        fractionDigits: MoneyUtil.getScaleByCurrency(data.currency),
                      ),
                      _FieldConfig(
                        'positionProfitLoss'.tr(), // 持仓盈亏
                        data.floatingProfitLoss,
                        titleSuffix: Builder(
                          builder: (context) => GestureDetectorHitTestWithoutSizeLimit(
                            extraHitTestArea: const EdgeInsets.all(16),
                            onTap: () {
                              showPopover(
                                context: context,
                                bodyBuilder: (context) => Container(
                                  padding: EdgeInsets.all(12.gw),
                                  child: Text(
                                    "position_profit_loss_note".tr(), // 不包含交易手续费
                                    style: context.textTheme.regular,
                                  ),
                                ),
                                barrierColor: Colors.black.withValues(alpha: 0.1),
                                direction: PopoverDirection.top,
                                arrowHeight: 8,
                                arrowWidth: 16,
                              );
                            },
                            child: Icon(
                              Icons.help_outline_sharp,
                              color: context.colorTheme.tabInactive,
                              size: 15.gw,
                            ),
                          ),
                        ),
                        isRight: true,
                        showSymbol: true,
                        color: data.floatingProfitLoss > 0
                            ? context.upColor
                            : data.floatingProfitLoss < 0
                                ? context.downColor
                                : null,
                        secondaryValue: data.floatingProfitLossRate,
                        secondaryValueSuffix: '%',
                      ),
                    ],
                  ),

                  SizedBox(height: 10.gw),

                  // 第二行数据
                  _PositionRow(
                    configs: [
                      _FieldConfig(
                        'totalQuantity'.tr(), // 总量
                        data.positionTotalNum,
                      ),
                      _FieldConfig(
                        'averagePrice'.tr(), // 均价
                        data.buyAvgPrice,
                        isCenter: true,
                        fractionDigits: MoneyUtil.getScaleByCurrency(data.currency),
                      ),
                      if (marketCategory.isFutures) ...[
                        _FieldConfig(
                          'available_margin'.tr(), // 可用保证金
                          data.availableMargin,
                          isRight: true,
                        ),
                      ] else ...[
                        _FieldConfig(
                          'marketValue'.tr(), // 市值
                          data.marketValue,
                          isRight: true,
                        ),
                      ],
                    ],
                  ),

                    SizedBox(height: 10.gw),
                    _buildOperateSection(),

                ],
              ),
            ),
          ),

          // if (kDebugMode)
          // Positioned(
          //     top: 0,
          //   right: 0,
          //     child: Text("$debugIndex", style: TextStyle(color: Colors.red.withValues(alpha: 0.3), fontSize: 50))),

        ],
      ),
    );
  }

  Widget _buildOperateSection() {
    return ExpandedRow(
        spacing: 13.gw,
        children: marketCategory.isFutures
            ? [
                // 2.8需求 隐藏止盈止损
                // _buildOperateButton(PositionCellAction.tpSL), // 止盈止损
                _buildOperateButton(PositionCellAction.addMargin), // 追加
                _buildOperateButton(PositionCellAction.sell), // 卖出
                _buildOperateButton(PositionCellAction.detail), // 详情
              ]
            : [
                _buildOperateButton(PositionCellAction.quote), // 行情
                _buildOperateButton(PositionCellAction.sell), // 卖出
              ]);
  }

  Widget _buildOperateButton(PositionCellAction action) {
    return CommonButton(
      title: action.getTitle(marketCategory),
      height: 28.gw,
      fontSize: 14.gsp,
      radius: 6.gr,
      color: switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kZangGolden => Color(0xFF2F4591),
        _ => null,
      },
      onPressed: onAction != null ? () => onAction!(action) : null,
    );
  }
}

class _FieldConfig {
  final String title;
  final Widget? titleSuffix;
  final double value;
  final bool isRight;
  final bool isCenter;
  final bool showSymbol;
  final int fractionDigits;
  final Color? color;
  final double? secondaryValue;
  final String? secondaryValueSuffix;

  const _FieldConfig(
    this.title,
    this.value, {
    this.titleSuffix,
    this.isRight = false,
    this.isCenter = false,
    this.showSymbol = false,
    this.fractionDigits = 2,
    this.color,
    this.secondaryValue,
    this.secondaryValueSuffix,
  });
}

class _PositionRow extends StatelessWidget {
  final List<_FieldConfig> configs;

  const _PositionRow({required this.configs});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: configs.map((cfg) {
        MainAxisAlignment rowMainAxisAlignment = MainAxisAlignment.start;
        CrossAxisAlignment align = CrossAxisAlignment.start;
        if (cfg.isRight) {
          rowMainAxisAlignment = MainAxisAlignment.end;
          align = CrossAxisAlignment.end;
        } else if (cfg.isCenter) {
          rowMainAxisAlignment = MainAxisAlignment.center;
          align = CrossAxisAlignment.center;
        }

        return Expanded(
          child: Column(
            crossAxisAlignment: align,
            children: [
              Row(
                mainAxisAlignment: rowMainAxisAlignment,
                children: [
                  Text(
                    cfg.title,
                    style: context.textTheme.regular.fs12,
                  ),
                  if (cfg.titleSuffix != null) cfg.titleSuffix!,
                ],
              ),
              Column(
                crossAxisAlignment: align,
                children: [
                  AnimatedFlipCounter(
                    fractionDigits: cfg.fractionDigits,
                    value: cfg.value,
                    prefix: cfg.showSymbol && cfg.value > 0 ? '+' : '',
                    thousandSeparator: ',',
                    textStyle: context.textTheme.primary.w700.ffAkz.copyWith(
                        color: cfg.color ??
                            switch (AppConfig.instance.skinStyle) {
                              AppSkinStyle.kTemplateD => context.colorTheme.textTitle,
                              AppSkinStyle.kZangGolden => Color(0xFF2F4591),
                              _ => null
                            }),
                  ),
                  if (cfg.secondaryValue != null) ...[
                    SizedBox(height: 2.gw),
                    Text(
                      '(${(cfg.showSymbol && cfg.secondaryValue! > 0) ? '+' : ''}${cfg.secondaryValue!.toStringAsFixed(2)}${cfg.secondaryValueSuffix ?? ''})',
                      style: context.textTheme.regular.fs11.w300.copyWith(
                        color: cfg.color ??
                            switch (AppConfig.instance.skinStyle) {
                              AppSkinStyle.kTemplateD => context.colorTheme.textTitle,
                              _ => context.colorTheme.textPrimary
                            },
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

class AccountPositionShimmerCell extends StatelessWidget {
  final MarketCategory marketCategory;

  const AccountPositionShimmerCell({
    super.key,
    required this.marketCategory,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Shimmer.fromColors(
        baseColor: context.theme.dividerColor,
        highlightColor: context.theme.cardColor.withValues(alpha: 0.5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 顶部：tag + 标题
            Row(
              children: [
                _box(36, 15, borderRadius: 2), // tag
                const SizedBox(width: 5),
                _box(80, 15, borderRadius: 2), // 标题骨架
              ],
            ),

            const SizedBox(height: 10),

            // 中间第一行三列
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _dataColumnSkeleton(), // 可卖数量
                _dataColumnSkeleton(), // 现价
                _dataColumnSkeleton(isLast: true), // 浮动盈亏
              ],
            ),

            const SizedBox(height: 6),

            // 中间第二行三列
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _dataColumnSkeleton(), // 持仓数量
                _dataColumnSkeleton(), // 均价
                _dataColumnSkeleton(isLast: true), // 可加保证金
              ],
            ),

              SizedBox(height: 10.gw),
              ExpandedRow(
                spacing: 13.gw,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _box(75, 20, borderRadius: 4),
                  _box(75, 20, borderRadius: 4),
                  if (marketCategory.isFutures) ...[
                    _box(75, 20, borderRadius: 4),
                    _box(75, 20, borderRadius: 4),
                  ]
                ] ,
              ),

          ],
        ),
      ),
    );
  }

  /// 单个数据列的骨架：上下两块
  Widget _dataColumnSkeleton({bool isLast = false}) {
    return Column(
      crossAxisAlignment: isLast ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        _box(50, 15), // label 骨架
        const SizedBox(height: 3),
        _box(43, 12), // value 骨架
      ],
    );
  }

  /// 通用灰色骨架块
  Widget _box(double width, double height, {double borderRadius = 2}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}
