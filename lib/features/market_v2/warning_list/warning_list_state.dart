import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/market/warn_response_entity.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class WarningListState extends Equatable {
  /// 列表获取状态 / List fetch status
  final DataStatus listFetchStatus;

  /// 加载更多状态 / Load more status
  final DataStatus loadMoreStatus;

  /// 删除状态 / Delete status
  final DataStatus deleteStatus;

  /// 预警列表数据 / Warning list data
  final WarnResponseEntity warnings;

  /// 选中的市场分类 / Selected market category
  final MarketCategory selectedMarketCategory;

  /// 当前页码 / Current page number
  final int currentPage;

  /// 总页数 / Total pages
  final int totalPages;

  /// 是否有更多数据 / Has more data
  final bool hasMore;

  /// 是否正在加载更多 / Is loading more
  final bool isLoadMore;

  /// 错误信息 / Error message
  final String error;

  const WarningListState({
    this.listFetchStatus = DataStatus.idle,
    this.loadMoreStatus = DataStatus.idle,
    this.deleteStatus = DataStatus.idle,
    this.warnings = const WarnResponseEntity(),
    this.selectedMarketCategory = MarketCategory.cnStocks,
    this.currentPage = 1,
    this.totalPages = 1,
    this.hasMore = true,
    this.isLoadMore = false,
    this.error = '',
  });

  WarningListState copyWith({
    DataStatus? listFetchStatus,
    DataStatus? loadMoreStatus,
    DataStatus? deleteStatus,
    WarnResponseEntity? warnings,
    MarketCategory? selectedMarketCategory,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
    bool? isLoadMore,
    String? error,
  }) {
    return WarningListState(
      listFetchStatus: listFetchStatus ?? this.listFetchStatus,
      loadMoreStatus: loadMoreStatus ?? this.loadMoreStatus,
      deleteStatus: deleteStatus ?? this.deleteStatus,
      warnings: warnings ?? this.warnings,
      selectedMarketCategory: selectedMarketCategory ?? this.selectedMarketCategory,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
      isLoadMore: isLoadMore ?? this.isLoadMore,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
        listFetchStatus,
        loadMoreStatus,
        deleteStatus,
        warnings,
        selectedMarketCategory,
        currentPage,
        totalPages,
        hasMore,
        isLoadMore,
        error,
      ];

  @override
  String toString() {
    return 'WarningListState('
        'listFetchStatus: $listFetchStatus, '
        'loadMoreStatus: $loadMoreStatus, '
        'deleteStatus: $deleteStatus, '
        'warnings: $warnings, '
        'selectedMarketCategory: $selectedMarketCategory, '
        'currentPage: $currentPage, '
        'totalPages: $totalPages, '
        'hasMore: $hasMore, '
        'isLoadMore: $isLoadMore, '
        'error: $error'
        ')';
  }
}
