import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';
import 'package:gp_stock_app/features/ranking/index.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

/// 首页 V2 状态
/// 统一管理首页所有数据，包括 Banner、通知、新闻、行情 Tab 等
class HomeV2State extends Equatable {
  // ==================== Banner ====================

  /// Banner 数据列表
  final List<BannerEntity>? bannerList;

  /// Banner 加载状态
  final DataStatus bannerStatus;

  /// 当前 Banner 索引
  final int bannerIndex;

  // ==================== 通知/跑马灯 ====================

  /// 通知数据列表
  final List<NotificationEntity> notifications;

  /// 通知加载状态
  final DataStatus notificationStatus;

  /// 是否已显示过弹窗（防止重复弹出）
  final bool shouldSuppressPopup;

  // ==================== 新闻 ====================

  /// 新闻数据
  final NewsRecordsList? newsData;

  /// 新闻加载状态
  final DataStatus newsStatus;

  // ==================== 行情 Tab ====================

  /// 当前选中的行情 Tab 索引
  final int marketTabIndex;

  /// 行情 Tab 类型列表（根据 TradingMode 动态生成）
  final List<MarketSectionTab> marketTabTypes;

  // ==================== 排行榜 ====================

  /// 排行榜类型
  final RankTabType currentRankTabType;

  final Map<RankTabType, List<RankingEntity>> rankMap;

  const HomeV2State({
    this.bannerList,
    this.bannerStatus = DataStatus.idle,
    this.bannerIndex = 0,
    this.notifications = const [],
    this.notificationStatus = DataStatus.idle,
    this.shouldSuppressPopup = false,
    this.newsData,
    this.newsStatus = DataStatus.idle,
    this.marketTabIndex = 0,
    this.marketTabTypes = const [],
    this.rankMap = const {
      RankTabType.daily: [],
      RankTabType.weekly: [],
      RankTabType.monthly: [],
    },
    this.currentRankTabType = RankTabType.daily,
  });

  HomeV2State copyWith({
    ValueGetter<List<BannerEntity>?>? bannerList,
    DataStatus? bannerStatus,
    int? bannerIndex,
    List<NotificationEntity>? notifications,
    DataStatus? notificationStatus,
    bool? shouldSuppressPopup,
    ValueGetter<NewsRecordsList?>? newsData,
    DataStatus? newsStatus,
    int? marketTabIndex,
    List<MarketSectionTab>? marketTabTypes,
    RankTabType? currentRankTabType,
    Map<RankTabType, List<RankingEntity>>? rankMap,
    RankingEntity? selfRank,
    DataStatus? rankStatus,
  }) {
    return HomeV2State(
      bannerList: bannerList != null ? bannerList() : this.bannerList,
      bannerStatus: bannerStatus ?? this.bannerStatus,
      bannerIndex: bannerIndex ?? this.bannerIndex,
      notifications: notifications ?? this.notifications,
      notificationStatus: notificationStatus ?? this.notificationStatus,
      shouldSuppressPopup: shouldSuppressPopup ?? this.shouldSuppressPopup,
      newsData: newsData != null ? newsData() : this.newsData,
      newsStatus: newsStatus ?? this.newsStatus,
      marketTabIndex: marketTabIndex ?? this.marketTabIndex,
      marketTabTypes: marketTabTypes ?? this.marketTabTypes,
      currentRankTabType: currentRankTabType ?? this.currentRankTabType,
      rankMap: rankMap ?? this.rankMap,
    );
  }

  @override
  List<Object?> get props => [
        bannerList,
        bannerStatus,
        bannerIndex,
        notifications,
        notificationStatus,
        shouldSuppressPopup,
        newsData,
        newsStatus,
        marketTabIndex,
        marketTabTypes,
        currentRankTabType,
        rankMap,
      ];
}
