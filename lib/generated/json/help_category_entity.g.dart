import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/system/help_category_entity.dart';

HelpCategoryEntityList $HelpCategoryEntityListFromJson(Map<String, dynamic> json) {
  final HelpCategoryEntityList helpCategoryEntityList = HelpCategoryEntityList();
  final List<HelpCategoryEntity>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<HelpCategoryEntity>(e) as HelpCategoryEntity).toList();
  if (list != null) {
    helpCategoryEntityList.list = list;
  }
  return helpCategoryEntityList;
}

Map<String, dynamic> $HelpCategoryEntityListToJson(HelpCategoryEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension HelpCategoryEntityListExtension on HelpCategoryEntityList {
  HelpCategoryEntityList copyWith({
    List<HelpCategoryEntity>? list,
  }) {
    return HelpCategoryEntityList()
      ..list = list ?? this.list;
  }
}

HelpCategoryEntity $HelpCategoryEntityFromJson(Map<String, dynamic> json) {
  return HelpCategoryEntity(icon: jsonConvert.convert<String>(json['icon']) ?? '',
      name: jsonConvert.convert<String>(json['name']) ?? '',
      questions: jsonConvert.convertListNotNull<HelpQuestion>(json['questions']) ?? <HelpQuestion>[],
      typeId: jsonConvert.convert<int>(json['typeId']) ?? 0);
}

Map<String, dynamic> $HelpCategoryEntityToJson(HelpCategoryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['icon'] = entity.icon;
  data['name'] = entity.name;
  data['questions'] = entity.questions.map((v) => v.toJson()).toList();
  data['typeId'] = entity.typeId;
  return data;
}

extension HelpCategoryEntityExtension on HelpCategoryEntity {
  HelpCategoryEntity copyWith({
    String? icon,
    String? name,
    List<HelpQuestion>? questions,
    int? typeId,
  }) {
    return HelpCategoryEntity(icon: icon ?? this.icon,
        name: name ?? this.name,
        questions: questions ?? this.questions,
        typeId: typeId ?? this.typeId);
  }
}

HelpQuestionList $HelpQuestionListFromJson(Map<String, dynamic> json) {
  final HelpQuestionList helpQuestionList = HelpQuestionList();
  final List<HelpQuestion>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<HelpQuestion>(e) as HelpQuestion).toList();
  if (list != null) {
    helpQuestionList.list = list;
  }
  return helpQuestionList;
}

Map<String, dynamic> $HelpQuestionListToJson(HelpQuestionList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension HelpQuestionListExtension on HelpQuestionList {
  HelpQuestionList copyWith({
    List<HelpQuestion>? list,
  }) {
    return HelpQuestionList()
      ..list = list ?? this.list;
  }
}

HelpQuestion $HelpQuestionFromJson(Map<String, dynamic> json) {
  return HelpQuestion(content: jsonConvert.convert<String>(json['content']) ?? '',
      hitNum: jsonConvert.convert<int>(json['hitNum']) ?? 0,
      id: jsonConvert.convert<int>(json['id']) ?? 0,
      sort: jsonConvert.convert<int>(json['sort']) ?? 0,
      title: jsonConvert.convert<String>(json['title']) ?? '',
      typeId: jsonConvert.convert<int>(json['typeId']) ?? 0);
}

Map<String, dynamic> $HelpQuestionToJson(HelpQuestion entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['content'] = entity.content;
  data['hitNum'] = entity.hitNum;
  data['id'] = entity.id;
  data['sort'] = entity.sort;
  data['title'] = entity.title;
  data['typeId'] = entity.typeId;
  return data;
}

extension HelpQuestionExtension on HelpQuestion {
  HelpQuestion copyWith({
    String? content,
    int? hitNum,
    int? id,
    int? sort,
    String? title,
    int? typeId,
  }) {
    return HelpQuestion(content: content ?? this.content,
        hitNum: hitNum ?? this.hitNum,
        id: id ?? this.id,
        sort: sort ?? this.sort,
        title: title ?? this.title,
        typeId: typeId ?? this.typeId);
  }
}