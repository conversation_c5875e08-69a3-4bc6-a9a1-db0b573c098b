import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_bank_entity_list.dart';

UserBankEntityList $UserBankEntityListFromJson(Map<String, dynamic> json) {
  final List<UserBankEntity>? list = (json['list'] as List<dynamic>?)?.map(
                      (e) => jsonConvert.convert<UserBankEntity>(e) as UserBankEntity).toList();
  return UserBankEntityList(
    list: list ?? const [],
  );
}

Map<String, dynamic> $UserBankEntityListToJson(UserBankEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension UserBankEntityListExtension on UserBankEntityList {
  UserBankEntityList copyWith({
    List<UserBankEntity>? list,
  }) {
    return UserBankEntityList(
      list: list ?? this.list,
    );
  }
}

UserBankEntity $UserBankEntityFromJson(Map<String, dynamic> json) {
  final String? bankAccount = jsonConvert.convert<String>(json['bankAccount']);
  final String? bankCode = jsonConvert.convert<String>(json['bankCode']);
  final String? bankFullName = jsonConvert.convert<String>(json['bankFullName']);
  final String? bankIcon = jsonConvert.convert<String>(json['bankIcon']);
  final String? icon = jsonConvert.convert<String>(json['icon']);
  final int? id = jsonConvert.convert<int>(json['id']);
  final String? realName = jsonConvert.convert<String>(json['realName']);
  final String? status = jsonConvert.convert<String>(json['status']);
  final String? tailNumber = jsonConvert.convert<String>(json['tailNumber']);
  final int? userId = jsonConvert.convert<int>(json['userId']);
  return UserBankEntity(
    bankAccount: bankAccount ?? '',
    bankCode: bankCode ?? '',
    bankFullName: bankFullName ?? '',
    bankIcon: bankIcon ?? '',
    icon: icon ?? '',
    id: id ?? 0,
    realName: realName ?? '',
    status: status ?? '',
    tailNumber: tailNumber ?? '',
    userId: userId ?? 0,
  );
}

Map<String, dynamic> $UserBankEntityToJson(UserBankEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['bankAccount'] = entity.bankAccount;
  data['bankCode'] = entity.bankCode;
  data['bankFullName'] = entity.bankFullName;
  data['bankIcon'] = entity.bankIcon;
  data['icon'] = entity.icon;
  data['id'] = entity.id;
  data['realName'] = entity.realName;
  data['status'] = entity.status;
  data['tailNumber'] = entity.tailNumber;
  data['userId'] = entity.userId;
  return data;
}

extension UserBankEntityExtension on UserBankEntity {
  UserBankEntity copyWith({
    String? bankAccount,
    String? bankCode,
    String? bankFullName,
    String? bankIcon,
    String? icon,
    int? id,
    String? realName,
    String? status,
    String? tailNumber,
    int? userId,
  }) {
    return UserBankEntity(
      bankAccount: bankAccount ?? this.bankAccount,
      bankCode: bankCode ?? this.bankCode,
      bankFullName: bankFullName ?? this.bankFullName,
      bankIcon: bankIcon ?? this.bankIcon,
      icon: icon ?? this.icon,
      id: id ?? this.id,
      realName: realName ?? this.realName,
      status: status ?? this.status,
      tailNumber: tailNumber ?? this.tailNumber,
      userId: userId ?? this.userId,
    );
  }
}