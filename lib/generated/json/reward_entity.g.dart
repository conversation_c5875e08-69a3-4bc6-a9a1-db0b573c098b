import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/task/reward_entity.dart';

RewardEntity $RewardEntityFromJson(Map<String, dynamic> json) {
  final int? amount = jsonConvert.convert<int>(json['amount']);
  final int? type = jsonConvert.convert<int>(json['type']);
  return RewardEntity(
    amount: amount ?? 0,
    type: type ?? 0,
  );
}

Map<String, dynamic> $RewardEntityToJson(RewardEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['amount'] = entity.amount;
  data['type'] = entity.type;
  return data;
}

extension RewardEntityExtension on RewardEntity {
  RewardEntity copyWith({
    int? amount,
    int? type,
  }) {
    return RewardEntity(
      amount: amount ?? this.amount,
      type: type ?? this.type,
    );
  }
}