import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';

import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/market_table/market_table_row.dart';
import 'package:gp_stock_app/shared/widgets/sort_header.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/stock_plate.dart';
import 'package:gp_stock_app/core/models/entities/market/market_plate_list.dart';

class MarketDataTable extends StatefulWidget {
  final int? limit;
  final bool isHome;
  final bool? showInCard;
  const MarketDataTable({super.key, this.limit, this.isHome = false, this.showInCard});

  @override
  State<MarketDataTable> createState() => _MarketDataTableState();
}

class _MarketDataTableState extends State<MarketDataTable> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMarketTabs(),
        12.verticalSpace,
        _buildTableContainer(),
      ],
    );
  }

  Widget _buildMarketTabs() {
    return BlocBuilder<MarketCubit, MarketState>(
      builder: (context, state) {
        final selectedCategory = state.selectedMarketCategory;
        List<String> tabData;
        int currentIndex;
        Function(int) onTap;

        if (widget.isHome) {
          final tabs = MarketCategory.values.where((e) => e.securityType == '1').toList();

          tabData = tabs.map((tab) => tr(tab.nameKey)).toList();
          currentIndex = tabs.indexWhere((tab) => tab == selectedCategory);
          onTap = (index) => context
              .read<MarketCubit>()
              .updateMarketCategory(tabs[index], isHome: true, skipLoading: true);
        } else {
          final plates = StockPlate.values.where((p) => p.category == selectedCategory).toList();
          tabData = plates.map((plate) => plate.titleKey.tr()).toList();
          currentIndex = plates.indexWhere((p) => p == state.selectedStockPlate);
          if (currentIndex < 0) currentIndex = 0;
          onTap = (index) => context.read<MarketCubit>().updateStockPlate(plates[index]);
        }

        final tabBar = CommonTabBar.withAutoKey(
          tabData,
          currentIndex: currentIndex.clamp(0, tabData.length - 1),
          onTap: onTap,
          style: CommonTabBarStyle.line,
          isScrollable: true,
          backgroundColor: Colors.transparent,
          height: 40.gw,
        );

        // If on home screen, return a Row with tab bar and the more button
        if (widget.isHome) {
          return Row(
            children: [
              Expanded(child: tabBar),
              TextButton.icon(
                icon: const Icon(Icons.chevron_right_outlined),
                iconAlignment: IconAlignment.end,
                onPressed: () => context.read<MainCubit>().selectedNavigationItem(BottomNavType.trade),
                label: Text("more".tr()),
                style: TextButton.styleFrom(
                  foregroundColor: context.colorTheme.textRegular,
                  iconColor: context.colorTheme.textRegular,
                  padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 4.gw),
                  minimumSize: const Size(0, 0),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ],
          );
        }

        // If not on home screen, just return the tab bar
        return tabBar;
      },
    );
  }

  Widget _buildTableContainer() {
    if (widget.showInCard == true) {
      return Column(
        children: [
          _buildTableHeader(),
          10.verticalSpace,
          Divider(color: context.theme.dividerColor),
          _buildTableContent(),
        ],
      );
    }
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        boxShadow: [
          BoxShadow(
            color: context.theme.shadowColor,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(18.gr),
        child: Column(
          children: [
            _buildTableHeader(),
            10.verticalSpace,
            Divider(color: context.theme.dividerColor),
            _buildTableContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildTableHeader() {
    return BlocBuilder<MarketCubit, MarketState>(
      builder: (context, state) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 3,
              child: Text(
                'name'.tr(),
                style: _headerTextStyle,
              ),
            ),
            Expanded(
              flex: 3,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SortHeader(
                    title: 'currentPrice'.tr(),
                    sortType: state.sortByPriceAsc,
                    onTap: context.read<MarketCubit>().handleSortByPrice,
                    textStyle: context.textTheme.primary.fs12.w500,
                  ),
                  SortHeader(
                    title: 'rise_fall'.tr(),
                    sortType: state.sortByChangeAsc,
                    onTap: context.read<MarketCubit>().handleSortByChange,
                    textStyle: context.textTheme.primary.fs12.w500,
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTableContent() {
    return BlocSelector<MarketCubit, MarketState, (DataStatus, MarketPlateList?, bool)>(
      selector: (state) => (
        state.tableFetchStatus,
        state.tableData,
        state.isPaginating,
      ),
      builder: (context, data) {
        final (status, items, isPaginating) = data;

        final isEmptyItems = items == null || items.list.isEmpty;

        if (status == DataStatus.loading && !isPaginating && isEmptyItems) {
          return _buildLoadingList();
        }

        if (isEmptyItems) {
          return _buildEmptyState();
        }

        return ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          itemCount: _calculateItemCount(items),
          itemBuilder: (context, index) => MarketTableRow(data: items.list[index], tabType: TradeTabType.Quotes),
        );
      },
    );
  }

  int _calculateItemCount(MarketPlateList items) =>
      widget.limit != null ? min(items.list.length, widget.limit!) : items.list.length;

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        6,
        (_) => Padding(
          padding: EdgeInsets.only(bottom: 8.gw),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.gr),
            child: ShimmerWidget(
              height: 45.gw,
              width: double.infinity,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Text(
        'no_data_available'.tr(),
        style: context.textTheme.regular.w500,
      ),
    );
  }

  TextStyle get _headerTextStyle => context.textTheme.regular.w500;
}
