import 'package:gp_stock_app/core/utils/contract_utils.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/order_detail.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/order_detail.g.dart';

@JsonSerializable()
class OrderDetail {
	/// 撤单时间
	final String cancelTime;
	/// 手续费收取详情
	final List<OrderDetailChargeDetailList> chargeDetailList;
	/// 合约id
	final int contractId;
	/// 合约类型 1：普通 2：体验 3：彩金
	final int contractType;
	/// 成本价
	final int costPrice;
	/// 账户币种
	final String currency;
	/// 成交股数
	final int dealNum;
	/// 成交单价
	final double dealPrice;
	/// 成交时间
	final String dealTime;
	/// 交易方向 1 买入 2 卖出
	final int direction;
	/// 唯一标识id
	final int id;
	/// 市场
	final String market;
	/// 倍数
	final int multiple;
	/// 期限类型 1：按天 2：按周 3：按月 4：免息
	final int periodType;
	/// 1.市价单 2.限价单
	final int priceType;
	final String securityType;
	/// 1.卖出 2.赎回 3.强平
	final int settleType;
	/// 委托状态 0 委托中 1委托撤销 2.订单成交成功 3.合约到期自动撤销
	final int status;
	/// 当前股票最新价格
	final int stockPrice;
	/// 股票代码
	final String symbol;
	/// 股票名称
	final String symbolName;
	/// 手续费
	final double tradeFee;
	/// 委托股数
	final int tradeNum;
	/// 委托价格
	final double tradePrice;
	/// 订单下单的时候的汇率
	final double tradeRate;
	/// 委托时间
	final String tradeTime;
	/// 交易类型 1 做多 2 做空
	final int tradeType;
	/// 成交金额
	final int transactionAmount;
	/// 订单类型 1：股票 2：股指
	final int type;
	/// 盈亏金额
	final int winAmount;

	const OrderDetail({
		this.cancelTime = '',
		this.chargeDetailList = const [],
		this.contractId = 0,
		this.contractType = 0,
		this.costPrice = 0,
		this.currency = '',
		this.dealNum = 0,
		this.dealPrice = 0.0,
		this.dealTime = '',
		this.direction = 0,
		this.id = 0,
		this.market = '',
		this.multiple = 0,
		this.periodType = 0,
		this.priceType = 0,
		this.securityType = '',
		this.settleType = 0,
		this.status = 0,
		this.stockPrice = 0,
		this.symbol = '',
		this.symbolName = '',
		this.tradeFee = 0.0,
		this.tradeNum = 0,
		this.tradePrice = 0.0,
		this.tradeRate = 0.0,
		this.tradeTime = '',
		this.tradeType = 0,
		this.transactionAmount = 0,
		this.type = 0,
		this.winAmount = 0,
	});

	factory OrderDetail.fromJson(Map<String, dynamic> json) => $OrderDetailFromJson(json);

	Map<String, dynamic> toJson() => $OrderDetailToJson(this);

	/// 合约标签
	String get contractLabel => ContractUtils.buildLabel(
		id: contractId,
		type: contractType,
		periodType: periodType,
		marketType: market,
		multiple: multiple,
	);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class OrderDetailChargeDetailList {
	/// 手续费用
	final double fee;
	/// 手续费用的名称
	final String feeName;

	const OrderDetailChargeDetailList({
		this.fee = 0.0,
		this.feeName = '',
	});

	factory OrderDetailChargeDetailList.fromJson(Map<String, dynamic> json) => $OrderDetailChargeDetailListFromJson(json);

	Map<String, dynamic> toJson() => $OrderDetailChargeDetailListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}