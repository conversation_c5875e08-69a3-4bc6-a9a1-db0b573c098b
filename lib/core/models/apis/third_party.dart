import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/wallet/third_party_success_response_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/models/entities/wallet/third_party_channel_entity_list_entity.dart';

class ThirdPartyService {
  static Future<List<ThirdPartyChannelEntity>> getThirdPartyChannelList(int? type) async {
    final response = await Http().request<ThirdPartyChannelListEntity>(
      ApiEndpoints.thirdPartyChannelList,
      method: HttpMethod.get,
      queryParameters: {
        if (type != null) 'type': type,
      },
    );
    if (response.isSuccess && response.data != null) {
      return response.data!.list;
    } else {
      return [];
    }
  }

  static Future<ThirdPartySuccessEntity?> doPayin({
    required int channelId,
    required double amount,
  }) async {
    final response = await Http().request<ThirdPartySuccessEntity>(
      ApiEndpoints.doPay,
      method: HttpMethod.post,
      params: {
        'channelId': channelId,
        'amount': amount,
      },
    );
    return response.data;
  }

  static Future<bool> bindUserWallet({
    required String bankCode,
    required String payAddress,
  }) async {
    final response = await Http().request<bool>(
      ApiEndpoints.bindUserWallet,
      method: HttpMethod.post,
      params: {
        'bankCode': bankCode,
        'payAddress': payAddress,
      },
    );
    return response.data == true;
  }

  static Future<bool> unbindUserWallet(int id) async {
    final res = await Http().request<bool>(
      '${ApiEndpoints.unbindUserWallet}/$id',
      method: HttpMethod.delete,
    );
    return res.data ?? false;
  }
}
