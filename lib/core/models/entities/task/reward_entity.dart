import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/reward_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/reward_entity.g.dart';

@JsonSerializable()
class RewardEntity {
  final int amount;
  final int type;

  RewardEntity({
    this.amount = 0,
    this.type = 0,
  });

  factory RewardEntity.fromJson(Map<String, dynamic> json) => $RewardEntityFromJson(json);

  Map<String, dynamic> toJson() => $RewardEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}