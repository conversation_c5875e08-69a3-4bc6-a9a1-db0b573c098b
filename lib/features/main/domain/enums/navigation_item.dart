import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_page_v2.dart';
import 'package:gp_stock_app/features/activity/activity_screen.dart';
import 'package:gp_stock_app/features/debug/screens/debug_ui_kit_screen.dart';
import 'package:gp_stock_app/features/home_v2/home/<USER>';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/features/market/market_section_screen.dart';
import 'package:gp_stock_app/features/profile/screens/profile_screen.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/logic/selected_exchange_cubit/selected_exchange_cubit.dart';

/// 底部导航栏类型
enum BottomNavType { home, account, trade, activity, profile, debug }

/// 底部导航栏页面配置
class BottomNavConfig {
  final BottomNavType type;
  final String title; // 本地化 key
  final String icon;
  final Widget page;
  final bool isShowAppBar;
  final bool isVisible;

  const BottomNavConfig({
    required this.type,
    required this.title,
    required this.icon,
    required this.page,
    this.isShowAppBar = true,
    this.isVisible = true,
  });

  static List<BottomNavConfig> allConfigs(context) => [
        BottomNavConfig(
          type: BottomNavType.home,
          title: 'home',
          icon: Assets.homeIcon,
          page: BlocProvider(
            create: (context) => getIt<MarketCubit>()..init(),
            child: const HomeV2Screen(),
          ),
          isShowAppBar: AppConfig.instance.flavor == Flavor.yhxt ? false : true,
        ),
        BottomNavConfig(
          type: BottomNavType.account,
          title: AppConfig.instance.flavor == Flavor.yhxt ? 'account' : 'contract',
          icon: Assets.accountIcon,
          page: BlocProvider(
            create: (context) => getIt<SelectedExchangeCubit>(param1: 'account'),
            child: const AccountScreenV2(),
          ),
        ),
        BottomNavConfig(
          type: BottomNavType.trade,
          title: 'quote',
          icon: Assets.tradeIcon,
          page: const MarketSectionScreen(),
        ),
        BottomNavConfig(
          type: BottomNavType.activity,
          title: 'activity',
          icon: Assets.activityIcon,
          page: const ActivityScreen(),
        ),
        BottomNavConfig(
          type: BottomNavType.profile,
          title: 'profile',
          icon: Assets.profileIcon,
          page: BlocProvider(
            create: (context) => getIt<SelectedExchangeCubit>(param1: 'profile'),
            child: const ProfileScreen(),
          ),
          isShowAppBar: false,
        ),
        if (kDebugMode) ...[
          BottomNavConfig(
            type: BottomNavType.debug,
            title: 'UIkit',
            icon: Assets.settingsIcon,
            page: const DebugUIKitScreen(),
            isShowAppBar: false,
          ),
        ]
      ];
}
