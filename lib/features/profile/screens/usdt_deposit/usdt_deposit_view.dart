import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_channel.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_input_formatter/no_leading_zero_int_formatter.dart';

import 'usdt_deposit_cubit.dart';
import 'usdt_deposit_state.dart';

class UsdtDepositPage extends StatefulWidget {
  const UsdtDepositPage({super.key});

  @override
  State<UsdtDepositPage> createState() => _UsdtDepositPageState();
}

class _UsdtDepositPageState extends State<UsdtDepositPage> {
  final TextEditingController _amountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _amountController.addListener(_onAmountChanged);

    // 设置amountController到cubit
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cubit = context.read<UsdtDepositCubit>();
      cubit.setAmountController(_amountController);
    });
  }

  @override
  void dispose() {
    _amountController.removeListener(_onAmountChanged);
    _amountController.dispose();
    super.dispose();
  }

  void _onAmountChanged() {
    final cubit = context.read<UsdtDepositCubit>();
    final isValid = _validateAmount(_amountController.text);

    // 更新金额验证状态
    cubit.updateAmountValidation(isValid);
    // 仅当输入值与当前选中金额不一致时，才清空预设选择
    final currentSelected = cubit.state.selectedAmount;
    if (_amountController.text != (currentSelected ?? '')) {
      cubit.updateAmount('');
    }
  }

  bool _validateAmount(String value) {
    if (value.isEmpty) return true;
    try {
      final amount = double.parse(value);
      final selected = context.read<UsdtDepositCubit>().state.selectedChannel;
      final min = selected?.minAmount ?? 0;
      final max = selected?.maxAmount ?? double.infinity;
      return amount >= min && amount <= max;
    } catch (_) {
      return false;
    }
  }

  void _onChannelChanged(USDTDepositChannel? channel) {
    if (channel != null) {
      context.read<UsdtDepositCubit>().selectChannel(channel.id);
    }
    _onAmountChanged();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UsdtDepositCubit, UsdtDepositState>(
      builder: (context, state) {
        // 不再使用本地字段，全部以 state.selectedChannel 为准
        return Scaffold(
          body: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  16.verticalSpace,
                  _buildChannelSection(state),
                  10.verticalSpace,
                  _buildAmountSection(state),

                  100.verticalSpace, // 为底部按钮留出空间
                ],
              ),
            ),
          ),
          bottomNavigationBar: Container(
            padding: EdgeInsets.all(16.gw),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildSubmitButton(state),
                16.verticalSpace,
                SupportWidget(),
                16.verticalSpace,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildChannelSection(UsdtDepositState state) {
    final channels = state.usdtList;
    if (state.isLoadingChannels) {
      return ShadowBox(
        padding: EdgeInsets.symmetric(vertical: 18.gw, horizontal: 15.gw),
        borderRadius: BorderRadius.circular(8.gr),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'rechargeNetwork'.tr(), // 充值网络
                  style: context.textTheme.title.w600,
                ),
                ShimmerWidget(height: 16, width: 160),
              ],
            ),
            16.verticalSpace,
            ShimmerWidget(height: 44),
            10.verticalSpace,
            ShimmerWidget(height: 16),
          ],
        ),
      );
    }
    if (channels.isEmpty) {
      return ShadowBox(
        borderRadius: BorderRadius.circular(8.gr),
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(20.gw),
            child: Text(
              'no_available_recharge_channels'.tr(), // 暂无可用充值渠道
              style: context.textTheme.regular,
            ),
          ),
        ),
      );
    }

    return ShadowBox(
      padding: EdgeInsets.symmetric(vertical: 18.gw, horizontal: 15.gw),
      borderRadius: BorderRadius.circular(8.gr),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 4.gw,
            children: [
              Text(
                'recharge_network'.tr(), // 充值网络
                style: context.textTheme.title.w600,
              ),
              if (state.selectedChannel?.minAmount != null && state.selectedChannel?.maxAmount != null)
                Text(
                  'single_transaction_limit'.tr(args: [
                    state.selectedChannel!.minAmount.formattedMoney,
                    state.selectedChannel!.maxAmount.formattedMoney
                  ]), // 单笔限额
                  style: context.textTheme.regular.copyWith(
                    color: context.theme.hintColor,
                  ),
                ),
            ],
          ),
          16.verticalSpace,
          CommonDropdown<USDTDepositChannel>(
            dropDownValue: channels
                .map((channel) => DropDownValue(
                      id: channel.id.toString(),
                      value: channel.networkName,
                    ))
                .toList(),
            selectedItem: state.selectedChannel == null
                ? null
                : DropDownValue(
                    id: state.selectedChannel!.id.toString(),
                    value: state.selectedChannel!.networkName,
                  ),
            hintText: 'please_select_recharge_network'.tr(),
            // 请选择充值网络
            showSearchBox: false,
            onChanged: (value) {
              final selectedChannel = channels
                  .where(
                    (e) => e.id.toString() == value?.id,
                  )
                  .firstOrNull;
              _onChannelChanged(selectedChannel);
              if (selectedChannel != null) {
                context.read<UsdtDepositCubit>().selectChannel(selectedChannel.id);
              }
            },
            itemBuilder: (context, item, isDisabled, isSelected) {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 16.gw),
                child: Text(item.value ?? ''),
              );
            },
          ),
          10.verticalSpace,
          Text(
            'wallet_address_protocol_warning'.tr(), // *不同协议钱包地址不互通,操作请务必鉴别,转账应严格注意充值对应地址
            style: context.textTheme.regular.fs12.copyWith(
              color: context.theme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountSection(UsdtDepositState state) {
    final cubit = context.read<UsdtDepositCubit>();
    if (state.isLoadingChannels) {
      return ShadowBox(
        borderRadius: BorderRadius.circular(8.gr),
        child: Padding(
          padding: EdgeInsets.all(12.gw),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ShimmerWidget(height: 20, width: 120),
              12.verticalSpace,
              ShimmerWidget(height: 16, width: 160),
              16.verticalSpace,
              ShimmerWidget(height: 44),
            ],
          ),
        ),
      );
    }
    if (state.selectedChannel == null) return SizedBox.shrink();

    final amountOptions = state.selectedChannel!.rechargeAmountOptionsList;
    final predefinedAmounts = amountOptions;

    return ShadowBox(
      borderRadius: BorderRadius.circular(8.gr),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'recharge_amount'.tr(), // 充值金额
                style: context.textTheme.title.w600,
              ),
              Text(
                'current_exchange_rate'.tr(args: [state.selectedChannel!.exchangeRate.formattedMoney]), // 当前汇率:1:7.16
                style: context.textTheme.regular.fs12.copyWith(
                  color: context.theme.hintColor,
                ),
              ),
            ],
          ),
          16.verticalSpace,
          // 只有当有预设金额选项时才显示GridView
          if (predefinedAmounts.isNotEmpty) ...[
            GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: predefinedAmounts.length,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  childAspectRatio: 100 / 28, //宽高比
                  crossAxisSpacing: 10.gw, //水平间距
                  mainAxisSpacing: 12.gw, //垂直间距
                  crossAxisCount: 3),
              padding: EdgeInsets.zero,
              itemBuilder: (BuildContext context, int index) {
                final amount = predefinedAmounts[index];
                return _buildAmountOptionButton(context, cubit, state, amount);
              },
            ),
            16.verticalSpace,
          ],
          TextFieldWidget(
            controller: _amountController,
            hintText: 'select_amount_or_enter_amount'.tr(), // 选定金额或输入金额
            textInputType: TextInputType.number,
            inputFormatters: [
              /// 正整数且第一位不为0
              NoLeadingZeroIntFormatter(),
            ],
            errorText: !state.isAmountValid ? 'amount_exceeds_limit_range'.tr() : null, // 金额超出限额范围
          ),
        ],
      ),
    );
  }

  Widget _buildAmountOptionButton(BuildContext context, UsdtDepositCubit cubit, UsdtDepositState state, String amount) {
    final isSelected = state.selectedAmount == amount;

    return GestureDetector(
      onTap: () {
        cubit.updateAmount(amount);
        _amountController.text = amount;
      },
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? context.theme.primaryColor : context.theme.inputDecorationTheme.fillColor,
          borderRadius: BorderRadius.circular(6.gr),
        ),
        child: Center(
          child: Text(
            amount,
            style: context.textTheme.title.w500.copyWith(
              color: isSelected ? context.colorTheme.textSecondary : null,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubmitButton(UsdtDepositState state) {
    final isValid = state.selectedChannel != null && _amountController.text.isNotEmpty && state.isAmountValid;

    return SizedBox(
      width: double.infinity,
      child: CommonButton(
        onPressed: isValid
            ? () {
                final cubit = context.read<UsdtDepositCubit>();
                cubit.submitDeposit(_amountController.text);
              }
            : null,
        title: 'submit'.tr(), // 提交
        enable: isValid,
        showLoading: state.isSubmitting,
      ),
    );
  }
}
