part of 'market_cubit.dart';

class MarketState extends Equatable {
  const MarketState({
    this.plateFetchStatusA = DataStatus.idle,
    this.plateList,
    this.gainDistributionFetchStatus = DataStatus.idle,
    this.gainDistributionList,
    this.tableData,
    this.tableFetchStatus = DataStatus.idle,
    this.selectedTableTab = 0, // 0: Hot List, 1: <PERSON>aine<PERSON>, 2: Losers
    this.isPaginating = false,
    this.selectedMarketCategory = MarketCategory.cnStocks,
    this.selectedStockPlate = StockPlate.cnSzse,
    this.sortByPriceAsc,
    this.sortByChangeAsc,
    this.sortByChangePlateListAsc,
    this.pageNumber = 1,
    this.selectedMarketSectionTab = MarketSectionTab.stocks,
    this.paginationStatus = DataStatus.idle,
    this.hasReachedMax = false,
    this.currentPageSize = 20,
    this.sortType = 0,
    this.order = 'DESC',
  });

  final DataStatus plateFetchStatusA;
  final PlateInfoList? plateList;
  final DataStatus gainDistributionFetchStatus;
  final List<int>? gainDistributionList;
  final MarketPlateList? tableData;
  final DataStatus tableFetchStatus;
  final int selectedTableTab;
  final bool isPaginating;
  final MarketCategory selectedMarketCategory;
  final StockPlate selectedStockPlate;
  final MarketSectionTab selectedMarketSectionTab;
  final SortType? sortByPriceAsc;
  final SortType? sortByChangeAsc;
  final SortType? sortByChangePlateListAsc;
  final int pageNumber;
  final DataStatus paginationStatus;
  final bool hasReachedMax;
  final int currentPageSize;
  final int sortType;
  final String order;
  @override
  List<Object?> get props => [
        plateFetchStatusA,
        plateList,
        gainDistributionFetchStatus,
        gainDistributionList,
        tableData,
        tableFetchStatus,
        selectedTableTab,
        isPaginating,
        selectedMarketCategory,
        selectedStockPlate,
        selectedMarketSectionTab,
        sortByPriceAsc,
        sortByChangeAsc,
        sortByChangePlateListAsc,
        pageNumber,
        paginationStatus,
        hasReachedMax,
        currentPageSize,
        sortType,
        order,
      ];

  MarketState copyWith({
    DataStatus? plateFetchStatusA,
    PlateInfoList? plateList,
    DataStatus? gainDistributionFetchStatus,
    List<int>? gainDistributionList,
    MarketPlateList? tableData,
    DataStatus? tableFetchStatus,
    int? selectedTableTab,
    bool? isPaginating,
    MarketCategory? selectedMarketCategory,
    StockPlate? selectedStockPlate,
    MarketSectionTab? selectedMarketSectionTab,
    SortType? sortByPriceAsc,
    SortType? sortByChangeAsc,
    SortType? sortByChangePlateListAsc,
    bool resetSortByChange = false,
    bool resetSortByPrice = false,
    bool resetSortByChangePlateList = false,
    int? pageNumber,
    DataStatus? paginationStatus,
    bool? hasReachedMax,
    int? currentPageSize,
    int? sortType,
    String? order,
  }) {
    return MarketState(
      plateFetchStatusA: plateFetchStatusA ?? this.plateFetchStatusA,
      plateList: plateList ?? this.plateList,
      gainDistributionFetchStatus: gainDistributionFetchStatus ?? this.gainDistributionFetchStatus,
      gainDistributionList: gainDistributionList ?? this.gainDistributionList,
      tableData: tableData ?? this.tableData,
      tableFetchStatus: tableFetchStatus ?? this.tableFetchStatus,
      selectedTableTab: selectedTableTab ?? this.selectedTableTab,
      isPaginating: isPaginating ?? this.isPaginating,
      selectedMarketCategory: selectedMarketCategory ?? this.selectedMarketCategory,
      selectedStockPlate: selectedStockPlate ?? this.selectedStockPlate,
      sortByChangeAsc: resetSortByChange ? null : sortByChangeAsc ?? this.sortByChangeAsc,
      sortByPriceAsc: resetSortByPrice ? null : sortByPriceAsc ?? this.sortByPriceAsc,
      sortByChangePlateListAsc:
          resetSortByChangePlateList ? null : sortByChangePlateListAsc ?? this.sortByChangePlateListAsc,
      selectedMarketSectionTab: selectedMarketSectionTab ?? this.selectedMarketSectionTab,
      pageNumber: pageNumber ?? this.pageNumber,
      paginationStatus: paginationStatus ?? this.paginationStatus,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPageSize: currentPageSize ?? this.currentPageSize,
      sortType: sortType ?? this.sortType,
      order: order ?? this.order,
    );
  }
}
