import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/widgets/filter_sheet.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';

import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../shared/constants/enums.dart';
import '../../../account/widgets/table_empty.dart';
import 'fund_records_cubit.dart';
import 'widgets/fund_record_cell.dart';

/// 资金记录
class FundRecordsScreen extends StatefulWidget {
  final int? contractId;
  final bool isContractAccount;
  const FundRecordsScreen({super.key, required this.contractId, this.isContractAccount = false});

  @override
  State<FundRecordsScreen> createState() => _FundRecordsScreenState();
}

class _FundRecordsScreenState extends State<FundRecordsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    Helper.afterInit(_init);
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 && !_isLoadingMore) {
      _loadMore();
    }
  }

  Future<void> _init() async {
    if (widget.isContractAccount) {
      context.read<FundRecordsCubit>().getContractAccountRecord(contractId: widget.contractId);
    } else {
      final commentAssetId = getIt<UserCubit>().state.accountInfo?.assetId;
      context
          .read<FundRecordsCubit>()
          .getAssetRecord(contractId: widget.contractId, commentAssetId: commentAssetId?.toString());
    }
  }

  Future<void> _onRefresh() async {
    if (widget.isContractAccount) {
      await context.read<FundRecordsCubit>().getContractAccountRecord(contractId: widget.contractId);
    } else {
      final commentAssetId = getIt<UserCubit>().state.accountInfo?.assetId;
      await context
          .read<FundRecordsCubit>()
          .getAssetRecord(contractId: widget.contractId, commentAssetId: commentAssetId?.toString());
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore) return;

    final state = context.read<FundRecordsCubit>().state;
    final hasNext = state.fundsRecords?.hasNext ?? false;

    if (!hasNext) return; // No more data to load

    setState(() {
      _isLoadingMore = true;
    });

    try {
      if (widget.isContractAccount) {
        await context.read<FundRecordsCubit>().getContractAccountRecord(
              contractId: widget.contractId,
              isLoadMore: true,
            );
      } else {
        final commentAssetId = getIt<UserCubit>().state.accountInfo?.assetId;
        await context.read<FundRecordsCubit>().getAssetRecord(
              contractId: widget.contractId,
              isLoadMore: true,
              commentAssetId: commentAssetId?.toString(),
            );
      }
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        surfaceTintColor: Colors.transparent,
        title: Text('fundRecords'.tr()),
        actions: [
          IconButton(
            onPressed: () {
              showModalBottomSheet(
                context: context,
                backgroundColor: context.theme.cardColor,
                isScrollControlled: true,
                showDragHandle: true,
                builder: (_) => BlocProvider.value(
                  value: context.read<FundRecordsCubit>(),
                  child: FundRecordFilterSheet(),
                ),
              );
            },
            icon: Icon(LucideIcons.filter),
          ),
        ],
      ),
      body: BlocBuilder<FundRecordsCubit, FundRecordsState>(
        builder: (context, state) {
          return _buildTableContent(state);
        },
      ),
    );
  }

  Widget _buildTableContent(FundRecordsState state) {
    if (state.assetsRecordsFetchStatus == DataStatus.loading && (state.fundsRecords?.records.isEmpty ?? true)) {
      return _buildLoadingList();
    }

    if (state.fundsRecords?.records.isEmpty ?? true) {
      return _buildEmptyState();
    }

    return RefreshIndicator.adaptive(
      backgroundColor: context.theme.cardColor,
      onRefresh: _onRefresh,
      child: ListView.separated(
        controller: _scrollController,
        padding: EdgeInsets.fromLTRB(8.gw, 10.gw, 8.gw, 0),
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: (state.fundsRecords?.records.length ?? 0) + 1, // +1 for loading indicator or end message
        itemBuilder: (context, index) {
          // If we've reached the end of the list
          if (index == (state.fundsRecords?.records.length ?? 0)) {
            // Check if we're loading more or if there's no more data
            if (_isLoadingMore) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 16.gw),
                child: const Center(child: CircularProgressIndicator.adaptive()),
              );
            } else if (state.fundsRecords?.hasNext ?? false) {
              // If there's more data but we're not currently loading, show a button to load more
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 16.gw),
                child: Center(
                  child: TextButton(
                    onPressed: _loadMore,
                    child: Text('loadMore'.tr()),
                  ),
                ),
              );
            } else {
              // If there's no more data, show a message
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 16.gw),
                child: Center(
                  child: Text(
                    'noMoreData'.tr(),
                    style: context.textTheme.regular.w600,
                  ),
                ),
              );
            }
          }

          // Regular item
          final record = state.fundsRecords?.records[index];
          return FundRecordCell(record: record, isContract: widget.isContractAccount);
        },
        separatorBuilder: (context, index) => 8.verticalSpace,
      ),
    );
  }

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        6,
        (_) => Padding(
          padding: EdgeInsets.only(bottom: 10.gw),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.gr),
            child: ShimmerWidget(
              height: 80.gw,
              width: double.infinity,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return TableEmptyWidget();
  }
}
