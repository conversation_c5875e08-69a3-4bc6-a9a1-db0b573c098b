// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'convert_rate_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ConvertRateState {
  DataStatus get status => throw _privateConstructorUsedError;
  List<ExchangeRate>? get rates => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  String get customAmount => throw _privateConstructorUsedError;
  String get selectedCurrency => throw _privateConstructorUsedError;
  bool get isEditingAmount => throw _privateConstructorUsedError;
  bool get isCalculating => throw _privateConstructorUsedError;
  double? get convertedUSD => throw _privateConstructorUsedError;
  double? get convertedCNY => throw _privateConstructorUsedError;
  double? get convertedHKD => throw _privateConstructorUsedError;

  /// Create a copy of ConvertRateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConvertRateStateCopyWith<ConvertRateState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConvertRateStateCopyWith<$Res> {
  factory $ConvertRateStateCopyWith(
          ConvertRateState value, $Res Function(ConvertRateState) then) =
      _$ConvertRateStateCopyWithImpl<$Res, ConvertRateState>;
  @useResult
  $Res call(
      {DataStatus status,
      List<ExchangeRate>? rates,
      String? error,
      String customAmount,
      String selectedCurrency,
      bool isEditingAmount,
      bool isCalculating,
      double? convertedUSD,
      double? convertedCNY,
      double? convertedHKD});
}

/// @nodoc
class _$ConvertRateStateCopyWithImpl<$Res, $Val extends ConvertRateState>
    implements $ConvertRateStateCopyWith<$Res> {
  _$ConvertRateStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConvertRateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? rates = freezed,
    Object? error = freezed,
    Object? customAmount = null,
    Object? selectedCurrency = null,
    Object? isEditingAmount = null,
    Object? isCalculating = null,
    Object? convertedUSD = freezed,
    Object? convertedCNY = freezed,
    Object? convertedHKD = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      rates: freezed == rates
          ? _value.rates
          : rates // ignore: cast_nullable_to_non_nullable
              as List<ExchangeRate>?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      customAmount: null == customAmount
          ? _value.customAmount
          : customAmount // ignore: cast_nullable_to_non_nullable
              as String,
      selectedCurrency: null == selectedCurrency
          ? _value.selectedCurrency
          : selectedCurrency // ignore: cast_nullable_to_non_nullable
              as String,
      isEditingAmount: null == isEditingAmount
          ? _value.isEditingAmount
          : isEditingAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isCalculating: null == isCalculating
          ? _value.isCalculating
          : isCalculating // ignore: cast_nullable_to_non_nullable
              as bool,
      convertedUSD: freezed == convertedUSD
          ? _value.convertedUSD
          : convertedUSD // ignore: cast_nullable_to_non_nullable
              as double?,
      convertedCNY: freezed == convertedCNY
          ? _value.convertedCNY
          : convertedCNY // ignore: cast_nullable_to_non_nullable
              as double?,
      convertedHKD: freezed == convertedHKD
          ? _value.convertedHKD
          : convertedHKD // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ConvertRateStateImplCopyWith<$Res>
    implements $ConvertRateStateCopyWith<$Res> {
  factory _$$ConvertRateStateImplCopyWith(_$ConvertRateStateImpl value,
          $Res Function(_$ConvertRateStateImpl) then) =
      __$$ConvertRateStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DataStatus status,
      List<ExchangeRate>? rates,
      String? error,
      String customAmount,
      String selectedCurrency,
      bool isEditingAmount,
      bool isCalculating,
      double? convertedUSD,
      double? convertedCNY,
      double? convertedHKD});
}

/// @nodoc
class __$$ConvertRateStateImplCopyWithImpl<$Res>
    extends _$ConvertRateStateCopyWithImpl<$Res, _$ConvertRateStateImpl>
    implements _$$ConvertRateStateImplCopyWith<$Res> {
  __$$ConvertRateStateImplCopyWithImpl(_$ConvertRateStateImpl _value,
      $Res Function(_$ConvertRateStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConvertRateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? rates = freezed,
    Object? error = freezed,
    Object? customAmount = null,
    Object? selectedCurrency = null,
    Object? isEditingAmount = null,
    Object? isCalculating = null,
    Object? convertedUSD = freezed,
    Object? convertedCNY = freezed,
    Object? convertedHKD = freezed,
  }) {
    return _then(_$ConvertRateStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      rates: freezed == rates
          ? _value._rates
          : rates // ignore: cast_nullable_to_non_nullable
              as List<ExchangeRate>?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      customAmount: null == customAmount
          ? _value.customAmount
          : customAmount // ignore: cast_nullable_to_non_nullable
              as String,
      selectedCurrency: null == selectedCurrency
          ? _value.selectedCurrency
          : selectedCurrency // ignore: cast_nullable_to_non_nullable
              as String,
      isEditingAmount: null == isEditingAmount
          ? _value.isEditingAmount
          : isEditingAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isCalculating: null == isCalculating
          ? _value.isCalculating
          : isCalculating // ignore: cast_nullable_to_non_nullable
              as bool,
      convertedUSD: freezed == convertedUSD
          ? _value.convertedUSD
          : convertedUSD // ignore: cast_nullable_to_non_nullable
              as double?,
      convertedCNY: freezed == convertedCNY
          ? _value.convertedCNY
          : convertedCNY // ignore: cast_nullable_to_non_nullable
              as double?,
      convertedHKD: freezed == convertedHKD
          ? _value.convertedHKD
          : convertedHKD // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

class _$ConvertRateStateImpl implements _ConvertRateState {
  const _$ConvertRateStateImpl(
      {this.status = DataStatus.idle,
      final List<ExchangeRate>? rates,
      this.error,
      this.customAmount = "1",
      this.selectedCurrency = "CNY",
      this.isEditingAmount = false,
      this.isCalculating = false,
      this.convertedUSD,
      this.convertedCNY,
      this.convertedHKD})
      : _rates = rates;

  @override
  @JsonKey()
  final DataStatus status;
  final List<ExchangeRate>? _rates;
  @override
  List<ExchangeRate>? get rates {
    final value = _rates;
    if (value == null) return null;
    if (_rates is EqualUnmodifiableListView) return _rates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? error;
  @override
  @JsonKey()
  final String customAmount;
  @override
  @JsonKey()
  final String selectedCurrency;
  @override
  @JsonKey()
  final bool isEditingAmount;
  @override
  @JsonKey()
  final bool isCalculating;
  @override
  final double? convertedUSD;
  @override
  final double? convertedCNY;
  @override
  final double? convertedHKD;

  @override
  String toString() {
    return 'ConvertRateState(status: $status, rates: $rates, error: $error, customAmount: $customAmount, selectedCurrency: $selectedCurrency, isEditingAmount: $isEditingAmount, isCalculating: $isCalculating, convertedUSD: $convertedUSD, convertedCNY: $convertedCNY, convertedHKD: $convertedHKD)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConvertRateStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._rates, _rates) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.customAmount, customAmount) ||
                other.customAmount == customAmount) &&
            (identical(other.selectedCurrency, selectedCurrency) ||
                other.selectedCurrency == selectedCurrency) &&
            (identical(other.isEditingAmount, isEditingAmount) ||
                other.isEditingAmount == isEditingAmount) &&
            (identical(other.isCalculating, isCalculating) ||
                other.isCalculating == isCalculating) &&
            (identical(other.convertedUSD, convertedUSD) ||
                other.convertedUSD == convertedUSD) &&
            (identical(other.convertedCNY, convertedCNY) ||
                other.convertedCNY == convertedCNY) &&
            (identical(other.convertedHKD, convertedHKD) ||
                other.convertedHKD == convertedHKD));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      status,
      const DeepCollectionEquality().hash(_rates),
      error,
      customAmount,
      selectedCurrency,
      isEditingAmount,
      isCalculating,
      convertedUSD,
      convertedCNY,
      convertedHKD);

  /// Create a copy of ConvertRateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConvertRateStateImplCopyWith<_$ConvertRateStateImpl> get copyWith =>
      __$$ConvertRateStateImplCopyWithImpl<_$ConvertRateStateImpl>(
          this, _$identity);
}

abstract class _ConvertRateState implements ConvertRateState {
  const factory _ConvertRateState(
      {final DataStatus status,
      final List<ExchangeRate>? rates,
      final String? error,
      final String customAmount,
      final String selectedCurrency,
      final bool isEditingAmount,
      final bool isCalculating,
      final double? convertedUSD,
      final double? convertedCNY,
      final double? convertedHKD}) = _$ConvertRateStateImpl;

  @override
  DataStatus get status;
  @override
  List<ExchangeRate>? get rates;
  @override
  String? get error;
  @override
  String get customAmount;
  @override
  String get selectedCurrency;
  @override
  bool get isEditingAmount;
  @override
  bool get isCalculating;
  @override
  double? get convertedUSD;
  @override
  double? get convertedCNY;
  @override
  double? get convertedHKD;

  /// Create a copy of ConvertRateState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConvertRateStateImplCopyWith<_$ConvertRateStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
