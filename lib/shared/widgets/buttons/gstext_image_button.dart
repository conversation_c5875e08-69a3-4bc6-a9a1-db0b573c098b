import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:cached_network_image/cached_network_image.dart';

enum GSTextImageButtonPosition { left, right, top, bottom }

class GSTextImageButton extends StatefulWidget {
  final String text;
  final String imageAssets;
  final TextStyle? textStyle;
  final String? selImageAssets;
  final Color bgColor;
  final Size imageSize;
  final double cornerRadius;
  final double interval;
  final Color titleColor;
  final double titleSize;
  final FontWeight fontWeight;
  final GSTextImageButtonPosition position;
  final VoidCallback? onPressed;
  final EdgeInsetsGeometry padding;
  final bool selected;
  final int delayMilliseconds;
  final Widget? titleChild;

  const GSTextImageButton({
    super.key,
    required this.text,
    required this.imageAssets,
    this.textStyle,
    this.selImageAssets,
    this.bgColor = Colors.transparent,
    this.cornerRadius = 2.0,
    this.imageSize = const Size(16, 16),
    this.titleColor = const Color(0xff6A7391),
    this.titleSize = 16.0,
    this.fontWeight = FontWeight.w400,
    this.interval = 8.0,
    this.padding = EdgeInsets.zero,
    this.position = GSTextImageButtonPosition.left,
    this.onPressed,
    this.selected = false,
    this.delayMilliseconds = 2000, // 默认延迟2秒
    this.titleChild,
  });

  @override
  State<StatefulWidget> createState() => _GSTextImageButtonState();
}

class _GSTextImageButtonState extends State<GSTextImageButton> {
  bool _isClickable = true;

  void _handleOnPressed() async {
    if (_isClickable && widget.onPressed != null) {
      widget.onPressed!();
      if (widget.delayMilliseconds > 0) {
        setState(() {
          _isClickable = false;
        });
        await Future.delayed(Duration(milliseconds: widget.delayMilliseconds));
        if (mounted) {
          setState(() {
            _isClickable = true;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      padding: widget.padding,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(widget.cornerRadius),
        color: widget.bgColor,
      ),
      child: TextButton(
        style: ButtonStyle(
          padding: WidgetStateProperty.all(EdgeInsets.zero), // 移除内边距
          minimumSize: WidgetStateProperty.all(const Size(0, 0)), // 设置最小尺寸
          tapTargetSize: MaterialTapTargetSize.shrinkWrap, // 缩小触摸目标区域
          overlayColor: WidgetStateProperty.all(Colors.transparent), // 设置点击时的颜色
        ),
        onPressed: () => _handleOnPressed(),
        child: (widget.position == GSTextImageButtonPosition.top || widget.position == GSTextImageButtonPosition.bottom)
            ? _getTopAndBottomPositionWidget(widget.position == GSTextImageButtonPosition.top)
            : _getLeftAndRightPositionWidget(widget.position == GSTextImageButtonPosition.left),
      ),
    );
  }

  Widget _getTopAndBottomPositionWidget(bool isTop) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: isTop
          ? [
              _getImage(),
              SizedBox(height: widget.interval),
              _getTitle(),
            ]
          : [
              _getTitle(),
              SizedBox(height: widget.interval),
              _getImage(),
            ],
    );
  }

  Widget _getLeftAndRightPositionWidget(bool isLeft) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: isLeft
          ? [_getImage(), SizedBox(width: widget.interval), _getTitle()]
          : [_getTitle(), SizedBox(width: widget.interval), _getImage()],
    );
  }

  Widget _getImage() {
    final imagePath = widget.selected && widget.selImageAssets != null ? widget.selImageAssets! : widget.imageAssets;

    return imagePath.startsWith('http')
        ? CachedNetworkImage(
            imageUrl: imagePath,
            width: widget.imageSize.width,
            height: widget.imageSize.height,
            placeholder: (context, url) => const CircularProgressIndicator(),
            errorWidget: (context, url, error) => const Icon(Icons.error),
          )
        : imagePath.endsWith('.svg')
            ? SvgPicture.asset(
                imagePath,
                width: widget.imageSize.width,
                height: widget.imageSize.height,
              )
            : Image.asset(
                imagePath,
                width: widget.imageSize.width,
                height: widget.imageSize.height,
              );
  }

  Widget _getTitle() {
    return widget.titleChild ??
        AutoSizeText(
          widget.text,
          maxLines: 2,
          style: widget.textStyle ??
          TextStyle(
            color: widget.titleColor,
            fontSize: widget.titleSize,
            fontWeight: widget.fontWeight,
          ),
    );
  }
}
