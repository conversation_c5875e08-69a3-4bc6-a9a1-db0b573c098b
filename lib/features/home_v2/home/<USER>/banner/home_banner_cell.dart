import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/home/<USER>';
import 'package:gp_stock_app/core/utils/app_navigation_handler.dart';

/// Banner 单元格组件
class HomeBannerCell extends StatelessWidget {
  final BannerEntity banner;
  final double height;
  final double radius;
  final EdgeInsetsGeometry margin;
  final VoidCallback? onTap;

  const HomeBannerCell({
    super.key,
    required this.banner,
    required this.height,
    required this.radius,
    required this.margin,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap ?? () => _handleTap(context),
      child: Container(
        margin: margin,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(radius),
          image: DecorationImage(
            image: CachedNetworkImageProvider(banner.imageUrl),
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  void _handleTap(BuildContext context) {
    AppNavigationHandler.handleNavigation(
      context,
      jumpType: banner.jumpType,
      jumpUrl: banner.jumpUrl,
    );
  }
}
