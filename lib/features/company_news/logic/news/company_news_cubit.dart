import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/models/apis/market.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import 'company_news_state.dart';

@injectable
class CompanyNewsCubit extends Cubit<CompanyNewsDetailsState> {

  CompanyNewsCubit() : super(const CompanyNewsDetailsState());

  Future<void> getCompanyNewsDetails(String id) async {
    emit(state.copyWith(status: DataStatus.loading));

    final result = await MarketApi.getCompanyNewsDetails(id: id);

    if (result != null) {
      emit(state.copyWith(
        status: DataStatus.success,
        newsDetails: result,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        status: DataStatus.failed,
      ));
    }
  }
}
