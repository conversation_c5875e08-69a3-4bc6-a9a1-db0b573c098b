import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/market_alert.dart';
import 'package:gp_stock_app/core/models/entities/market/warn_response_entity.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

import 'warning_form_state.dart';

class WarningFormCubit extends Cubit<WarningFormState> {
  WarningFormCubit() : super(const WarningFormState());

  /// 根据股票代码获取预警详情（编辑时使用）/ Get warning by instrument (for editing)
  ///
  /// [instrument] 股票代码 / Stock instrument
  Future<void> getWarnByInstrument(String instrument) async {
    emit(state.copyWith(fetchStatus: DataStatus.loading));
    final result = await MarketAlertApi.getWarnByInstrument(instrument);
    if (result != null) {
      emit(state.copyWith(
        fetchStatus: DataStatus.success,
        warnData: () => result,
      ));
    } else {
      emit(state.copyWith(
        fetchStatus: DataStatus.failed,
      ));
    }
  }

  /// 添加或更新预警 / Add or update warning
  ///
  /// [warnData] 预警数据 / Warning data
  Future<void> addOrUpdateWarn(WarnResponse warnData, {bool isUpdate = false}) async {
    GPEasyLoading.showLoading(message: 'loading'.tr());
    emit(state.copyWith(
      addUpdateStatus: DataStatus.loading,
    ));
    final success = await (isUpdate ? MarketAlertApi.updateWarn(warnData) : MarketAlertApi.addWarn(warnData));
    GPEasyLoading.dismiss();
    if (!success) {
      emit(state.copyWith(addUpdateStatus: DataStatus.failed));
      return;
    }
    GPEasyLoading.showSuccess(message: isUpdate ? 'updatedSuccessfully'.tr() : 'addedSuccessfully'.tr());
    emit(state.copyWith(
      addUpdateStatus: DataStatus.success,
      warnData: () => warnData,
    ));
    getIt<NavigatorService>().pop(result: isUpdate);
  }
}
