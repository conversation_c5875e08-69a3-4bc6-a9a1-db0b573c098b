import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:gp_stock_app/core/models/apis/deposit.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_channel.dart';
import 'package:gp_stock_app/core/utils/system_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';

import 'usdt_deposit_in_process_state.dart';

class UsdtDepositInProcessCubit extends Cubit<UsdtDepositInProcessState> {
  UsdtDepositInProcessCubit({required UsdtRechargeOrder order}) : super(UsdtDepositInProcessState(order: order));

  void contactCustomerService(BuildContext context) async {
    emit(state.copyWith(isSubmitting: true));
    try {
      final flag = await DepositApi.confirmUsdtPaid(id: state.order.id);
      if (flag) {
        GPEasyLoading.showToast("success".tr()); // 提交成功
        if (context.mounted) {
          SystemUtil.contactService(context);
        }
      }
    } finally {
      emit(state.copyWith(isSubmitting: false));
    }
  }
}
