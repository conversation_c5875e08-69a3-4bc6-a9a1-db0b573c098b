plugins {
    id "com.android.application" version "8.7.0"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.gpmember.app"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }


    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.gpmember.app"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdkVersion 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a' // Optional: excludes x86, x86_64 if not needed
        }
    }

    signingConfigs {
        // Default release config (fallback)
        release {
            keyAlias keystoreProperties['keyAlias'] ?: "upload"
            keyPassword keystoreProperties['keyPassword'] ?: "gp@2025"
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : file("${System.getenv('HOME')}/jks/GP/key.jks")
            storePassword keystoreProperties['storePassword'] ?: "gp@2025"
        }

        // Flavor-specific signing configs
        preRelease {
            keyAlias keystoreProperties['keyAlias'] ?: "upload"
            keyPassword keystoreProperties['keyPassword'] ?: "gp@2025"
            storeFile file(keystoreProperties['pre.storeFile'] ?: "${System.getenv('HOME')}/jks/PRE/key.jks")
            storePassword keystoreProperties['storePassword'] ?: "gp@2025"
        }

        gpRelease {
            keyAlias keystoreProperties['keyAlias'] ?: "upload"
            keyPassword keystoreProperties['keyPassword'] ?: "gp@2025"
            storeFile file(keystoreProperties['gp.storeFile'] ?: "${System.getenv('HOME')}/jks/GP/key.jks")
            storePassword keystoreProperties['storePassword'] ?: "gp@2025"
        }

        rsypRelease {
            keyAlias keystoreProperties['keyAlias'] ?: "upload"
            keyPassword keystoreProperties['keyPassword'] ?: "gp@2025"
            storeFile file(keystoreProperties['rsyp.storeFile'] ?: "${System.getenv('HOME')}/jks/RSYP/key.jks")
            storePassword keystoreProperties['storePassword'] ?: "gp@2025"
        }

        yhxtRelease {
            keyAlias keystoreProperties['keyAlias'] ?: "upload"
            keyPassword keystoreProperties['keyPassword'] ?: "gp@2025"
            storeFile file(keystoreProperties['yhxt.storeFile'] ?: "${System.getenv('HOME')}/jks/YHXT/key.jks")
            storePassword keystoreProperties['storePassword'] ?: "gp@2025"
        }

        tempaRelease {
            keyAlias keystoreProperties['keyAlias'] ?: "upload"
            keyPassword keystoreProperties['keyPassword'] ?: "gp@2025"
            storeFile file(keystoreProperties['tempa.storeFile'] ?: "${System.getenv('HOME')}/jks/TEMPA/key.jks")
            storePassword keystoreProperties['storePassword'] ?: "gp@2025"
        }

        bszbRelease {
            keyAlias keystoreProperties['keyAlias'] ?: "upload"
            keyPassword keystoreProperties['keyPassword'] ?: "gp@2025"
            storeFile file(keystoreProperties['bszb.storeFile'] ?: "${System.getenv('HOME')}/jks/BSZB/key.jks")
            storePassword keystoreProperties['storePassword'] ?: "gp@2025"
        }

        dyzbRelease {
            keyAlias keystoreProperties['keyAlias'] ?: "upload"
            keyPassword keystoreProperties['keyPassword'] ?: "gp@2025"
            storeFile file(keystoreProperties['dyzb.storeFile'] ?: "${System.getenv('HOME')}/jks/DYZB/key.jks")
            storePassword keystoreProperties['storePassword'] ?: "gp@2025"
        }

        xyzqRelease {
            keyAlias keystoreProperties['keyAlias'] ?: "upload"
            keyPassword keystoreProperties['keyPassword'] ?: "gp@2025"
            storeFile file(keystoreProperties['xyzq.storeFile'] ?: "${System.getenv('HOME')}/jks/XYZQ/key.jks")
            storePassword keystoreProperties['storePassword'] ?: "gp@2025"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            // 使用 ProGuard 规则文件
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source = "../.."
}








// ----- BEGIN flavorDimensions (autogenerated by flutter_flavorizr) -----
apply from: "flavorizr.gradle"
// ----- END flavorDimensions (autogenerated by flutter_flavorizr) -----