import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/generated/json/account.g.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'dart:convert';

@JsonSerializable()
class AccountInfo extends Equatable {
  /// 账户总资产：合约账户
  final double accountAmount;

  /// 现货账户总资产：现货持仓市值
  final double assetAmount;

  /// 现货id
  final int assetId;

  /// 法币类型
  final String currency;

  /// 现货冻结现金
  final double freezeCash;

  /// 利息券
  final double interestCash;

  /// 今日收益：废弃
  final double todayWinAmount;

  /// 今日收益率：废弃
  final double todayWinRate;

  /// 现货可用现金
  final double usableCash;

  /// 浮动盈亏
  final double profitLoss;

  const AccountInfo({
    this.accountAmount = 0,
    this.assetAmount = 0,
    this.assetId = 0,
    this.currency = '',
    this.freezeCash = 0,
    this.interestCash = 0,
    this.todayWinAmount = 0,
    this.todayWinRate = 0,
    this.usableCash = 0,
    this.profitLoss = 0,
  });

	factory AccountInfo.fromJson(Map<String, dynamic> json) => $AccountInfoFromJson(json);

	Map<String, dynamic> toJson() => $AccountInfoToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}

  @override
  List<Object?> get props => [
		accountAmount,
		assetAmount,
		assetId,
		currency,
		freezeCash,
		interestCash,
		todayWinAmount,
		todayWinRate,
		usableCash,
		profitLoss,
	];
}



/// 交易详情/持仓信息
@JsonSerializable()
class PositionEntity {
	double appendMargin = 0;
	int availableMargin = 0;
	double buyAvgPrice = 0;
	double buyTotalNum = 0;
	dynamic closeLine;
	double costPrice = 0;
	String createTime = '';
	String currency = '';
	int direction = 0;
	double disableNum = 0;
	double distanceCloseLine = 0;
	double distanceWarningLine = 0;
	double feeAmount = 0;
	double floatingProfitLoss = 0;
	double floatingProfitLossRate = 0;
	int id = 0;
	double marginAmount = 0;
	double marginRatio = 0;
	String market = '';
	double marketValue = 0;
	String orderNo = '';
	int positionDays = 0;
	double positionTotalNum = 0;
	double restNum = 0;
	String securityType = '';
	int stockPrice = 0;
	double stopLossValue = 0;
	String symbol = '';
	String symbolName = '';
	double takeProfitValue = 0;
	int tradeType = 0;
	double tradeUnit = 0;
	int type = 0;
	double warningLine = 0;

	PositionEntity();

	factory PositionEntity.fromJson(Map<String, dynamic> json) => $PositionEntityFromJson(json);

	Map<String, dynamic> toJson() => $PositionEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}