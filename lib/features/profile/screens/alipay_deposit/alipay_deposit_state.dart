import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/alipay_account_entity.dart';
import 'package:gp_stock_app/core/models/entities/wallet/withdraw_config_entity.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AlipayDepositState extends Equatable {
  final int? depositAmount;
  final DataStatus submitStatus;
  final bool isAmountValid;
  final WithdrawConfigEntity? depositConfigEntity;
  final DataStatus depositConfigStatus;
  final List<AlipayAccountEntity> alipayAccounts;
  final DataStatus alipayAccountStatus;
  final AlipayAccountEntity? selectedAlipayAccount;
  final DataStatus bindWalletStatus;
  final String? orderNumber;
  final String? error;

  const AlipayDepositState({
    this.depositAmount,
    this.submitStatus = DataStatus.idle,
    this.isAmountValid = true,
    this.depositConfigEntity,
    this.depositConfigStatus = DataStatus.idle,
    this.alipayAccounts = const [],
    this.alipayAccountStatus = DataStatus.idle,
    this.selectedAlipayAccount,
    this.bindWalletStatus = DataStatus.idle,
    this.orderNumber,
    this.error,
  });

  AlipayDepositState copyWith({
    int? Function()? depositAmount,
    DataStatus? submitStatus,
    bool? isAmountValid,
    double? minAmount,
    double? maxAmount,
    WithdrawConfigEntity? depositConfigEntity,
    DataStatus? depositConfigStatus,
    List<AlipayAccountEntity>? alipayAccounts,
    DataStatus? alipayAccountStatus,
    AlipayAccountEntity? selectedAlipayAccount,
    DataStatus? bindWalletStatus,
    String? orderNumber,
    String? Function()? error,
  }) {
    return AlipayDepositState(
      depositAmount: depositAmount != null ? depositAmount() : this.depositAmount,
      submitStatus: submitStatus ?? this.submitStatus,
      isAmountValid: isAmountValid ?? this.isAmountValid,
      depositConfigEntity: depositConfigEntity ?? this.depositConfigEntity,
      depositConfigStatus: depositConfigStatus ?? this.depositConfigStatus,
      alipayAccounts: alipayAccounts ?? this.alipayAccounts,
      alipayAccountStatus: alipayAccountStatus ?? this.alipayAccountStatus,
      selectedAlipayAccount: selectedAlipayAccount ?? this.selectedAlipayAccount,
      bindWalletStatus: bindWalletStatus ?? this.bindWalletStatus,
      orderNumber: orderNumber ?? this.orderNumber,
      error: error != null ? error() : this.error,
    );
  }

  bool get canSubmit {
    return depositAmount != null &&
        depositAmount! > 0 &&
        isAmountValid &&
        selectedAlipayAccount != null &&
        submitStatus != DataStatus.loading;
  }

  @override
  List<Object?> get props => [
        depositAmount,
        submitStatus,
        isAmountValid,
        depositConfigEntity,
        depositConfigStatus,
        alipayAccounts,
        alipayAccountStatus,
        selectedAlipayAccount,
        bindWalletStatus,
        orderNumber,
        error,
      ];
}