import 'dart:math' show max, min;

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/market/stock_kline_entity.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/time_range.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/sort_color/sort_color_cubit.dart';

/// 指数股票卡片数据模型
class IndexStockCardModel {
  final String name;
  final double price;
  final double change;
  final double gainPercentage;
  final Color textColor;
  final Color? lineColor;
  final List<FlSpot>? spots;
  final String? bgImgPath;

  const IndexStockCardModel({
    required this.name,
    required this.price,
    required this.change,
    required this.gainPercentage,
    required this.textColor,
    this.lineColor,
    this.spots,
    this.bgImgPath,
  });

  /// 从 IndexStockInfo 提取数据 (用于 visual_graph_section)
  static IndexStockCardModel fromStockInfoWithSpots(
    BuildContext context,
    IndexStockInfo stockInfo,
    String market,
  ) {
    final info = stockInfo.stockInfo;
    final name = info.name;
    final price = info.latestPrice;
    final change = price - info.close;
    final closePrice = info.close;
    final gain = closePrice != 0.0 ? change / closePrice : 0.0;
    final textColor = gain.getValueColor(context);
    final spots = _getSpots(stockInfo.klineData, market);

    return IndexStockCardModel(
      name: name,
      price: price,
      change: change,
      gainPercentage: gain * 100,
      textColor: textColor,
      lineColor: textColor,
      spots: spots,
    );
  }

  /// 从 IndexStockInfo 提取数据 (用于 yhxt_visual_graph_section)
  static IndexStockCardModel fromStockInfoWithBgImage(
    BuildContext context,
    IndexStockInfo stockInfo,
  ) {
    final info = stockInfo.stockInfo;
    final name = info.name;
    final price = info.latestPrice;
    final change = price - info.close;
    final closePrice = info.close;
    final gain = closePrice != 0.0 ? change / closePrice : 0.0;
    final textColor = gain.getValueColor(context);

    final bgGreenPath = "assets/images/bg_home_stock_card_green.png";
    final bgRedPath = "assets/images/bg_home_stock_card_red.png";
    final isGain = gain > 0;
    final marketColor = context.watch<SortColorCubit>().state.marketColor;
    final isRedUp = marketColor == MarketColor.redUpGreenDown;
    final bgImgPath = switch ((isRedUp, isGain)) {
      (true, true) => bgRedPath,
      (true, false) => bgGreenPath,
      (false, true) => bgGreenPath,
      (false, false) => bgRedPath,
    };

    return IndexStockCardModel(
      name: name,
      price: price,
      change: change,
      gainPercentage: gain * 100,
      textColor: textColor,
      bgImgPath: bgImgPath,
    );
  }

  /// 将K线数据转换为FlSpot列表
  static List<FlSpot> _getSpots(StockKlineEntity? klineData, String market) {
    final limit = TimeRange.shareMinutesPerDay(market);
    if (klineData?.list == null || klineData!.list.isEmpty) return [];

    final list = klineData.list;

    try {
      final klinePrices = list.map((item) {
        return item.price == 0.0 ? item.open : item.price;
      }).toList();

      if (klinePrices.isEmpty) return [];

      final paddedPrices = List.generate(limit, (index) => index < klinePrices.length ? klinePrices[index] : null);
      final validPrices = paddedPrices.whereType<double>().toList();

      if (validPrices.isEmpty) return [];

      final minValue = validPrices.reduce(min);
      final maxValue = validPrices.reduce(max);
      final range = maxValue - minValue;

      if (range == 0) {
        return klinePrices
            .asMap()
            .entries
            .map((entry) => FlSpot(entry.key.toDouble(), 5.0))
            .toList();
      }

      return paddedPrices.asMap().entries.map((entry) {
        if (entry.value == null) return FlSpot.nullSpot;
        return FlSpot(entry.key.toDouble(), ((entry.value! - minValue) / range * 10).toDouble());
      }).toList();
    } catch (e) {
      return [];
    }
  }
}
