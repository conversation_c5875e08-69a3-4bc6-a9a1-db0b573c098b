// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdraw_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WithdrawalState {
  DataStatus get withdrawStatus => throw _privateConstructorUsedError;
  DataStatus get withdrawConfigFetchStatus =>
      throw _privateConstructorUsedError;
  DataStatus get recordsFetchStatus => throw _privateConstructorUsedError;
  WithdrawConfigEntity? get withdrawalConfig =>
      throw _privateConstructorUsedError;
  List<WithdrawalRecord>? get records => throw _privateConstructorUsedError;
  int? get current => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  String? get withdrawAmount => throw _privateConstructorUsedError;
  UserBankEntity? get selectedBank => throw _privateConstructorUsedError;
  bool? get isFormValid => throw _privateConstructorUsedError;

  /// Create a copy of WithdrawalState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WithdrawalStateCopyWith<WithdrawalState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WithdrawalStateCopyWith<$Res> {
  factory $WithdrawalStateCopyWith(
          WithdrawalState value, $Res Function(WithdrawalState) then) =
      _$WithdrawalStateCopyWithImpl<$Res, WithdrawalState>;
  @useResult
  $Res call(
      {DataStatus withdrawStatus,
      DataStatus withdrawConfigFetchStatus,
      DataStatus recordsFetchStatus,
      WithdrawConfigEntity? withdrawalConfig,
      List<WithdrawalRecord>? records,
      int? current,
      int? total,
      String? error,
      String? withdrawAmount,
      UserBankEntity? selectedBank,
      bool? isFormValid});
}

/// @nodoc
class _$WithdrawalStateCopyWithImpl<$Res, $Val extends WithdrawalState>
    implements $WithdrawalStateCopyWith<$Res> {
  _$WithdrawalStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WithdrawalState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? withdrawStatus = null,
    Object? withdrawConfigFetchStatus = null,
    Object? recordsFetchStatus = null,
    Object? withdrawalConfig = freezed,
    Object? records = freezed,
    Object? current = freezed,
    Object? total = freezed,
    Object? error = freezed,
    Object? withdrawAmount = freezed,
    Object? selectedBank = freezed,
    Object? isFormValid = freezed,
  }) {
    return _then(_value.copyWith(
      withdrawStatus: null == withdrawStatus
          ? _value.withdrawStatus
          : withdrawStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      withdrawConfigFetchStatus: null == withdrawConfigFetchStatus
          ? _value.withdrawConfigFetchStatus
          : withdrawConfigFetchStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      recordsFetchStatus: null == recordsFetchStatus
          ? _value.recordsFetchStatus
          : recordsFetchStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      withdrawalConfig: freezed == withdrawalConfig
          ? _value.withdrawalConfig
          : withdrawalConfig // ignore: cast_nullable_to_non_nullable
              as WithdrawConfigEntity?,
      records: freezed == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<WithdrawalRecord>?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      withdrawAmount: freezed == withdrawAmount
          ? _value.withdrawAmount
          : withdrawAmount // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as UserBankEntity?,
      isFormValid: freezed == isFormValid
          ? _value.isFormValid
          : isFormValid // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WithdrawalStateImplCopyWith<$Res>
    implements $WithdrawalStateCopyWith<$Res> {
  factory _$$WithdrawalStateImplCopyWith(_$WithdrawalStateImpl value,
          $Res Function(_$WithdrawalStateImpl) then) =
      __$$WithdrawalStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DataStatus withdrawStatus,
      DataStatus withdrawConfigFetchStatus,
      DataStatus recordsFetchStatus,
      WithdrawConfigEntity? withdrawalConfig,
      List<WithdrawalRecord>? records,
      int? current,
      int? total,
      String? error,
      String? withdrawAmount,
      UserBankEntity? selectedBank,
      bool? isFormValid});
}

/// @nodoc
class __$$WithdrawalStateImplCopyWithImpl<$Res>
    extends _$WithdrawalStateCopyWithImpl<$Res, _$WithdrawalStateImpl>
    implements _$$WithdrawalStateImplCopyWith<$Res> {
  __$$WithdrawalStateImplCopyWithImpl(
      _$WithdrawalStateImpl _value, $Res Function(_$WithdrawalStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of WithdrawalState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? withdrawStatus = null,
    Object? withdrawConfigFetchStatus = null,
    Object? recordsFetchStatus = null,
    Object? withdrawalConfig = freezed,
    Object? records = freezed,
    Object? current = freezed,
    Object? total = freezed,
    Object? error = freezed,
    Object? withdrawAmount = freezed,
    Object? selectedBank = freezed,
    Object? isFormValid = freezed,
  }) {
    return _then(_$WithdrawalStateImpl(
      withdrawStatus: null == withdrawStatus
          ? _value.withdrawStatus
          : withdrawStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      withdrawConfigFetchStatus: null == withdrawConfigFetchStatus
          ? _value.withdrawConfigFetchStatus
          : withdrawConfigFetchStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      recordsFetchStatus: null == recordsFetchStatus
          ? _value.recordsFetchStatus
          : recordsFetchStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      withdrawalConfig: freezed == withdrawalConfig
          ? _value.withdrawalConfig
          : withdrawalConfig // ignore: cast_nullable_to_non_nullable
              as WithdrawConfigEntity?,
      records: freezed == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<WithdrawalRecord>?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      withdrawAmount: freezed == withdrawAmount
          ? _value.withdrawAmount
          : withdrawAmount // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as UserBankEntity?,
      isFormValid: freezed == isFormValid
          ? _value.isFormValid
          : isFormValid // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$WithdrawalStateImpl implements _WithdrawalState {
  const _$WithdrawalStateImpl(
      {this.withdrawStatus = DataStatus.idle,
      this.withdrawConfigFetchStatus = DataStatus.idle,
      this.recordsFetchStatus = DataStatus.idle,
      this.withdrawalConfig,
      final List<WithdrawalRecord>? records,
      this.current,
      this.total,
      this.error,
      this.withdrawAmount,
      this.selectedBank,
      this.isFormValid})
      : _records = records;

  @override
  @JsonKey()
  final DataStatus withdrawStatus;
  @override
  @JsonKey()
  final DataStatus withdrawConfigFetchStatus;
  @override
  @JsonKey()
  final DataStatus recordsFetchStatus;
  @override
  final WithdrawConfigEntity? withdrawalConfig;
  final List<WithdrawalRecord>? _records;
  @override
  List<WithdrawalRecord>? get records {
    final value = _records;
    if (value == null) return null;
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? current;
  @override
  final int? total;
  @override
  final String? error;
  @override
  final String? withdrawAmount;
  @override
  final UserBankEntity? selectedBank;
  @override
  final bool? isFormValid;

  @override
  String toString() {
    return 'WithdrawalState(withdrawStatus: $withdrawStatus, withdrawConfigFetchStatus: $withdrawConfigFetchStatus, recordsFetchStatus: $recordsFetchStatus, withdrawalConfig: $withdrawalConfig, records: $records, current: $current, total: $total, error: $error, withdrawAmount: $withdrawAmount, selectedBank: $selectedBank, isFormValid: $isFormValid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WithdrawalStateImpl &&
            (identical(other.withdrawStatus, withdrawStatus) ||
                other.withdrawStatus == withdrawStatus) &&
            (identical(other.withdrawConfigFetchStatus,
                    withdrawConfigFetchStatus) ||
                other.withdrawConfigFetchStatus == withdrawConfigFetchStatus) &&
            (identical(other.recordsFetchStatus, recordsFetchStatus) ||
                other.recordsFetchStatus == recordsFetchStatus) &&
            (identical(other.withdrawalConfig, withdrawalConfig) ||
                other.withdrawalConfig == withdrawalConfig) &&
            const DeepCollectionEquality().equals(other._records, _records) &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.withdrawAmount, withdrawAmount) ||
                other.withdrawAmount == withdrawAmount) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            (identical(other.isFormValid, isFormValid) ||
                other.isFormValid == isFormValid));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      withdrawStatus,
      withdrawConfigFetchStatus,
      recordsFetchStatus,
      withdrawalConfig,
      const DeepCollectionEquality().hash(_records),
      current,
      total,
      error,
      withdrawAmount,
      selectedBank,
      isFormValid);

  /// Create a copy of WithdrawalState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WithdrawalStateImplCopyWith<_$WithdrawalStateImpl> get copyWith =>
      __$$WithdrawalStateImplCopyWithImpl<_$WithdrawalStateImpl>(
          this, _$identity);
}

abstract class _WithdrawalState implements WithdrawalState {
  const factory _WithdrawalState(
      {final DataStatus withdrawStatus,
      final DataStatus withdrawConfigFetchStatus,
      final DataStatus recordsFetchStatus,
      final WithdrawConfigEntity? withdrawalConfig,
      final List<WithdrawalRecord>? records,
      final int? current,
      final int? total,
      final String? error,
      final String? withdrawAmount,
      final UserBankEntity? selectedBank,
      final bool? isFormValid}) = _$WithdrawalStateImpl;

  @override
  DataStatus get withdrawStatus;
  @override
  DataStatus get withdrawConfigFetchStatus;
  @override
  DataStatus get recordsFetchStatus;
  @override
  WithdrawConfigEntity? get withdrawalConfig;
  @override
  List<WithdrawalRecord>? get records;
  @override
  int? get current;
  @override
  int? get total;
  @override
  String? get error;
  @override
  String? get withdrawAmount;
  @override
  UserBankEntity? get selectedBank;
  @override
  bool? get isFormValid;

  /// Create a copy of WithdrawalState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WithdrawalStateImplCopyWith<_$WithdrawalStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
