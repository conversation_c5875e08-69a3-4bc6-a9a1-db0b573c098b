import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/money_util/money_util.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trade/trading_form_section/3_position_fraction_selector/position_fraction_selector.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/utils/trade_calculator.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown_v2.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/simple_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/layout/expanded_row.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/stepper_text_field.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import 'trade_sell_cubit.dart';
import 'trade_sell_state.dart';
import 'widgets/sell_confirm_dialog.dart';

class TradeSellScreen extends StatelessWidget {
  const TradeSellScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = BlocProvider.of<TradeSellCubit>(context);

    return BlocBuilder<TradeSellCubit, TradeSellState>(
      builder: (context, state) {
        // 根据市场类型决定标题 / Determine title based on market category
        final marketCategory = MarketCategory.fromSecurity(
          state.record.market,
          state.record.securityType,
        );
        final title = marketCategory == MarketCategory.cnStocks ? 'sell'.tr() : 'closePosition'.tr();

        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: AppBar(
            backgroundColor: context.theme.cardColor,
            title: Text(title),
          ),
          body: SafeArea(
            child: state.stockInfoStatus ==
              DataStatus.failed ?_buildFailedState(context, cubit: cubit) :
               Container(
                padding: EdgeInsets.fromLTRB(14.gw, 16.gw, 14.gw, 5.gw),
                child: Column(
                  spacing: 16.gw,
                  children: [
                    Expanded(
                      child: _buildContent(context, state: state, cubit: cubit),
                    ),
                    _buildBottomButton(context, cubit: cubit),
                  ],
                ),
              ),


        ));
      },
    );
  }

  Widget _buildContent(
    BuildContext context, {
    required TradeSellState state,
    required TradeSellCubit cubit,
  }) {
    return Container(
      padding: EdgeInsets.fromLTRB(14.gw, 12.gw, 14.gw, 0),
      decoration: BoxDecoration(

        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gw),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: 12.gw,
        children: [
          // 证券名称
          _buildSecurityName(context, state: state),

          AmountRow(
            title: 'tradeType'.tr(), // 交易类型
            value: _getTradeTypeText(state),
            fontSize: 13.gr,
            color: context.downColor, // 卖出颜色 / Sell color
          ),

          AmountRow(
            title: 'averageBuyPrice'.tr(), // 买入均价
            value: state.record.buyAvgPrice.toString(),
            fontSize: 13.gr,
            fractionDigits: state.pricePrecision, // 使用价格精度 / Use price precision
          ),

          AmountRow(
            title: 'availableQuantity'.tr(), // 可卖数量
            value: state.record.restNum.removeZeros,
            fontSize: 13.gr,
          ),


          // 价格显示/输入区域：市价单显示"以实际价格成交"，限价单显示输入框
          // Price display/input area: market order shows "execute at market price", limit order shows input field
          _buildPriceSection(context, state: state, cubit: cubit),

          // 卖出数量输入
          _buildQuantityInput(context, state: state, cubit: cubit, loading: state.stockInfoStatus == DataStatus.loading),

          // 快捷比例选择器
          PositionFractionSelector(
            selectedFraction: state.selectedFraction,
            onFractionChanged: cubit.onFractionChanged,
          ),

          // 总价（实际得到的金额，已扣除手续费）/ Total amount (actual received, fee deducted)
          _buildTotalPriceSection(context, state: state),
        ],
      ));
  }

  /// 加载失败状态 / Failed state
  Widget _buildFailedState(BuildContext context, {required TradeSellCubit cubit}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [

          Text(
            'networkError'.tr(),
            style: context.textTheme.regular.fs13,
          ),
          SizedBox(height: 16.gw),
          CommonButton(
            title: 'retry'.tr(), // 重试
            width: 80.gw,
            height: 40.gw,
            fontSize: 14.gw,
            onPressed: () => cubit.retryLoadStockInfo(),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityName(BuildContext context, {required TradeSellState state}) {
    final record = state.record;

    // 期货显示产品代码
    if (record.isFutures) {
      return Row(
        children: [
          Text(
            record.symbolName,
            style: context.textTheme.primary,
          ),
          const SizedBox(width: 8),
          Container(
            height: 15.gw,
            padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 2.gw),
            decoration: BoxDecoration(
              color: const Color(0xffE9F0FD),
              borderRadius: BorderRadius.circular(2.gw),
            ),
            child: Text(
              record.productCode,
              style: context.textTheme.primary.fs8,
            ),
          ),
        ],
      );
    }

    // 股票显示名称和代码
    return Text(
      '${record.symbolName} (${record.symbol})',
      style: switch (AppConfig.instance.skinStyle) {
        AppSkinStyle.kTemplateD => context.textTheme.title,
        _ => context.textTheme.primary,
      },
    );
  }

  String _getTradeTypeText(TradeSellState state) {
    if (state.record.tradeType == 1) {
      return 'closeLong'.tr(); // 平多
    } else if (state.record.tradeType == 2) {
      return 'closeShort'.tr(); // 平空
    }
    return 'sell'.tr(); // 卖出
  }

  Widget _buildOrderTypeDropdown(
    BuildContext context, {
    required TradeSellState state,
    required TradeSellCubit cubit,
  }) {

    final orderPriceTypeList = state.record.isIndex ? [OrderPriceType.market] : OrderPriceType.values;

    final orderTypeItems = orderPriceTypeList.map((type) {
      return DropDownValueV2(
        id: type.name,
        label: type.tr,
        enabled: true,
      );
    }).toList();

    final selectedItem = orderTypeItems.firstWhere(
      (item) => item.id == state.orderPriceType.name,
    );

    return Row(
      children: [
        Container(
          width: 96.gw,
          alignment: Alignment.centerLeft,
          child: SimpleDropdown(
            hintText: 'orderType'.tr(),
            // 订单类型
            items: orderTypeItems,
            selectedItem: selectedItem,
            hideDropdownArrow: false,
            onChanged: (item) {
              if (item != null) {
                final orderType = OrderPriceType.values.firstWhere(
                  (type) => type.name == item.id,
                );
                cubit.setOrderType(orderType);
              }
            },
          ),
        ),
      ],
    );
  }

  /// 价格区域：订单类型下拉框 + 价格显示/输入
  /// Price section: order type dropdown + price display/input
  Widget _buildPriceSection(
    BuildContext context, {
    required TradeSellState state,
    required TradeSellCubit cubit,
  }) {
    return ExpandedRow(
      spacing: 10.gw,
      children: [
        // 订单类型下拉框 / Order type dropdown
        _buildOrderTypeDropdown(context, state: state, cubit: cubit),
        // 价格输入框或提示文本 / Price input or hint text
        _buildPriceTF(context, state: state, cubit: cubit),
      ],
    );
  }

  /// 价格输入框或提示文本 / Price input field or hint text
  Widget _buildPriceTF(BuildContext context, {
    required TradeSellState state,
    required TradeSellCubit cubit,
  }) {
    // 市价单：显示"以实时价格成交"文本 / Market order: show text message
    if (state.orderPriceType == OrderPriceType.market) {
      return Container(
        alignment: Alignment.centerRight,
        child: Text(
          'realTimePrice'.tr(), // 以实时价格成交
          style: context.textTheme.regular.fs13,
        ),
      );
    }

    // 限价单：显示价格输入框 / Limit order: show price input field
    final currentPrice = state.inputPrice;
    final decimalDigits = state.pricePrecision; // 价格精度（从stockInfo获取）/ Price precision (from stockInfo)

    return StepperTextField(
      displayValue: currentPrice.toStringAsFixed(decimalDigits),
      decimalDigits: decimalDigits,
      canDecrement: currentPrice > state.priceDownLimited,
      canIncrement: currentPrice < state.priceUpLimited,
      onIncrement: cubit.incrementPrice,
      onDecrement: cubit.decrementPrice,
      onValueChanged: (value) {
        final price = double.tryParse(value);
        if (price != null) {
          cubit.onInputPriceChanged(price, needClamp: false); // 实时输入不 clamp
        }
      },
      onFocusChanged: (value) {
        final price = double.tryParse(value);
        if (price == null) return null;
        final aligned = cubit.onInputPriceChanged(price);
        return aligned.toStringAsFixed(decimalDigits);
      },
    );
  }

  Widget _buildQuantityInput(
    BuildContext context, {
    required TradeSellState state,
    required TradeSellCubit cubit,
    bool loading = false,
  }) {
    final maxQty = state.record.restNum;
    final currentQty = state.sellQuantity;
    // 根据步进值计算小数位数 / Calculate decimal digits based on step
    final decimalDigits = TradeCalculator.calculateFractionDigits(state.quantityStep);

    return ExpandedRow(
      spacing: 10.gw,
      children: [
        Text(
          'sellQuantity'.tr(), // 卖出数量
          style: context.textTheme.regular.fs13,
        ),
        if (loading) ...[
          ShimmerWidget(height: 30.gw, width: double.infinity),
        ] else ...[
        StepperTextField(
          displayValue: currentQty.toStringAsFixed(decimalDigits),
          decimalDigits: decimalDigits,
          canDecrement: currentQty > state.quantityStep,
          canIncrement: currentQty < maxQty,
          onIncrement: cubit.incrementQuantity,
          onDecrement: cubit.decrementQuantity,
          onValueChanged: (value) {
            final qty = double.tryParse(value);
            if (qty != null) {
              cubit.onQuantityInputChanged(qty);
            }
          },
          onFocusChanged: (value) {
            final qty = double.tryParse(value);
            if (qty == null) return null;
            final aligned = cubit.onQuantityInputChanged(qty);
            return aligned.toStringAsFixed(decimalDigits);
          },
        ),
    ]
      ],
    );
  }

  /// 构建总价区域（含约等于人民币显示）
  /// Build total price section (with CNY equivalent display)
  Widget _buildTotalPriceSection(BuildContext context, {required TradeSellState state}) {
    final currency = state.record.currency;
    final scale = MoneyUtil.getScaleByCurrency(currency);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
          AmountRow(
            title: 'totalPrice'.tr(), // 总价
            amount: state.totalAmount,
            currency: currency,
            fontSize: 13.gr,
            fractionDigits: scale, // CNY: 2位小数，其他: 3
          ),

        if (currency != 'CNY') ...[
          SizedBox(height: 4.gw),
          Row(
            children: [
              Spacer(),
              AnimatedFlipCounter(
                prefix: '≈ ',
                duration: const Duration(milliseconds: 500),
                suffix: ' CNY',
                thousandSeparator: ',',
                fractionDigits: 2,
                textStyle: context.textTheme.primary.fs13.w800.ffAkz.copyWith(
                  height: 1,
                ),
                value: _calculateCNYEquivalent(context, state.totalAmount, currency),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 计算约等于人民币的金额 / Calculate CNY equivalent amount
  double _calculateCNYEquivalent(BuildContext context, double amount, String currency) {
    try {
      final exchangeRate = context.read<ExchangeRateCubit>().getCurrencyRateConfig(currency);
      return amount / exchangeRate.rate;
    } catch (e) {
      // 如果获取汇率失败，返回原金额 / Return original amount if exchange rate fetch fails
      return amount;
    }
  }

  Widget _buildBottomButton(BuildContext context, {required TradeSellCubit cubit}) {
    return BlocBuilder<TradeSellCubit, TradeSellState>(
      builder: (context, state) {
        return CommonButton(
          title: "sell".tr(), // 卖出
          enable: state.stockInfoStatus == DataStatus.success && state.sellQuantity > 0,
          onPressed: () {
            // 收起键盘 / Dismiss keyboard
            FocusScope.of(context).unfocus();
            // 显示确认对话框 / Show confirmation dialog
            showDialog(
              context: context,
              builder: (_) => BlocProvider.value(
                value: cubit,
                child: const SellConfirmDialog(),
              ),
            );
          },
        );
      },
    );
  }
}
