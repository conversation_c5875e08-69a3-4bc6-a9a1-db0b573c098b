import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/help.dart';
import 'package:gp_stock_app/core/models/entities/system/help_category_entity.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';

part 'help_state.dart';

@injectable
class HelpCubit extends Cubit<HelpState> {
  HelpCubit() : super(const HelpState());

  Future<void> getHelpList() async {
    emit(state.copyWith(helpListStatus: DataStatus.loading, updatingField: HelpField.list));
    final result = await HelpService.getHelpList();

    emit(state.copyWith(
      helpListStatus: DataStatus.success,
      helpCategories: result,
    ));
  }

  Future<void> getHelpQuestionDetail(String id) async {
    emit(
      state.copyWith(
        helpDetailStatus: DataStatus.loading,
        updatingField: HelpField.detail,
        selectedQuestion: () => null,
      ),
    );

    final result = await HelpService.getHelpQuestionDetail(id);
    if (result != null) {
      emit(state.copyWith(
        helpDetailStatus: DataStatus.success,
        selectedQuestion: () => result,
      ));
    }
  }

  Future<void> markQuestionSolved(String questionId) async {
    GPEasyLoading.showLoading(message: 'loading'.tr());
    emit(state.copyWith(feedbackStatus: DataStatus.loading, updatingField: HelpField.feedback));
    final result = await HelpService.markQuestionSolved(questionId);
    GPEasyLoading.dismiss();
    if (result != null) {
      GPEasyLoading.showToast(result);
      emit(state.copyWith(
        feedbackStatus: DataStatus.success,
      ));
    }
  }

  Future<void> markQuestionUnsolved(String questionId, String content) async {
    GPEasyLoading.showLoading(message: 'loading'.tr());
    emit(state.copyWith(feedbackStatus: DataStatus.loading, updatingField: HelpField.feedback));
    final result = await HelpService.markQuestionUnsolved(questionId, content);
    GPEasyLoading.dismiss();
    if (result != null) {
      GPEasyLoading.showToast(result);
      emit(state.copyWith(
        feedbackStatus: DataStatus.success,
      ));
    }
  }

  Future<void> searchHelpQuestions(String keyword) async {
    if (keyword.isEmpty) {
      emit(state.copyWith(
        searchStatus: DataStatus.success,
        searchResults: null,
        query: '',
      ));
      return;
    }
    emit(state.copyWith(
      searchStatus: DataStatus.loading,
      updatingField: HelpField.search,
      searchResults: null,
      query: keyword,
    ));

    final result = await HelpService.searchHelpQuestions(keyword);
    emit(state.copyWith(
      searchStatus: DataStatus.success,
      searchResults: result,
    ));
  }
}
