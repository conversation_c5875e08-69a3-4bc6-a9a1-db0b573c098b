import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_future_sltp.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_dialog_add_margin.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/order_list_state.dart';
import 'package:gp_stock_app/features/account_v2/1_common/cancel_order_dialog/cancel_order_dialog.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/logic/f_trade_acct_entrust_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/logic/f_trade_acct_position_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/routes/route_tracker.dart';
import 'package:gp_stock_app/shared/routes/trade_navigation_helper.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/account_v2/1_common/trade_order_sheet/trade_order_sheet.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_cubit.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown_v2.dart';
import '../sub_screen/widgets/list_view_cell/account_position_cell.dart' show AccountPositionCell, PositionCellAction;
import '../sub_screen/widgets/list_view_cell/account_entrust_cell.dart';
import '../sub_screen/widgets/list_view_cell/account_trade_cell.dart';

/// 账户单元格构建工具类
/// Account cell builder utility class
class AccountCellBuilder {
  AccountCellBuilder._();

  /// 统一的导航到标的和tab的处理函数 / Unified navigation to instrument and tab
  ///
  /// [futuresTab] - 期货页面的目标tab (trade/quotation)
  /// [spotTab] - 现货页面的目标tab (Trading/Quotes)
  /// [tradeAccountId] - 可选的交易账户ID，仅用于现货
  /// [shouldSwitchInstrument] - 是否需要切换标的（期货专用，现货始终切换）
  static Future<void> _navigateToInstrument(
    BuildContext context, {
    required FTradeAcctOrderRecords item,
    required TradeTabType futuresTab,
    required TradeTabType spotTab,
    String? tradeAccountId,
    bool shouldSwitchInstrument = true,
  }) {
    if (item.isFutures) {
      return TradeNavigationHelper.goToFuturesTradingCenter(
        context,
        data: item.toFTradeListItemModel(),
        marketCode: item.market,
        scope: item.futuresScope,
        tabType: futuresTab,
        shouldSwitchInstrument: shouldSwitchInstrument,
      );
    }

    return TradeNavigationHelper.goToTradingCenterV2(
      context,
      security: Security(instrument: item.instrument),
      tabType: spotTab,
      tradeAccountId: tradeAccountId,
      isIndexTradingOverride: item.isIndex,
    );
  }

  /// 处理期货持仓点击 / Handle futures position tap
  static Future<void> _handleFuturesPositionTap(
    BuildContext context, {
    required FTradeAcctOrderRecords item,
  }) async {
    return _navigateToInstrument(
      context,
      item: item,
      futuresTab: TradeTabType.Trading,
      spotTab: TradeTabType.Trading,
      shouldSwitchInstrument: true,
    );
  }

  /// 处理股指持仓点击 / Handle stock index position tap
  static void _handleStockIndexPositionTap(
    BuildContext context, {
    required FTradeAcctOrderRecords item,
  }) {
    // 切换到行情页
    context.read<MainCubit>().selectedNavigationItem(BottomNavType.trade);
    // 切换到股指tab
    context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.stockIndex);
    // 查找对应的股指索引
    final indexTradeCubit = getIt<IndexTradeCubit>();
    final indexes = indexTradeCubit.state.indexStockConfigList;
    final targetIndex = indexes.indexWhere(
      (index) => index.instrument == item.instrument,
    );

    // 如果找到对应的股指，切换到该股指并触发动画
    if (targetIndex != -1) {
      indexTradeCubit.updateSelectedIndex(targetIndex, animate: true);
    }
  }

  /// 导航到交易中心V2 / Navigate to trading center V2
  static Future<void> _navigateToTradingCenterV2(
    BuildContext context, {
    required FTradeAcctOrderRecords item,
    required TradeTabType tabType,
    required String? tradeAccountId,
  }) {
    return TradeNavigationHelper.goToTradingCenterV2(
      context,
      security: Security(instrument: item.instrument),
      tabType: tabType,
      tradeAccountId: tradeAccountId,
      isIndexTradingOverride: item.isIndex,
    );
  }

  /// 处理止盈止损设置 / Handle take profit and stop loss
  static Future<void> _handleTpSl(
    BuildContext context, {
    required FTradeAcctOrderRecords item,
    required TradeTypeOption tradeType,
    required Color tradeColor,
  }) async {
    final cubit = context.read<AccountScreenCubitV2>();
    await TradeBottomSheetFutureSlTp(
      context: context,
      contractName: item.symbolName,
      tradeType: tradeType,
      directionText: tradeType.shortText,
      directionColor: tradeColor,
      openPrice: item.buyAvgPrice,
      currentPrice: item.stockPrice,
      currency: item.currency,
      restNum: item.restNum,
      initialTakeProfitValue: item.takeProfitValue,
      initialStopLossValue: item.stopLossValue,
      onPressedOk: (takeProfitValue, stopLossValue) async {
        // 验证止盈止损价格
        if (tradeType == TradeTypeOption.openLong) {
          if (takeProfitValue != 0 && takeProfitValue <= item.buyAvgPrice) {
            return GPEasyLoading.showToast("take_profit_above_entry".tr());
          } else if (stopLossValue != 0 && stopLossValue >= item.buyAvgPrice) {
            return GPEasyLoading.showToast("stop_loss_below_entry".tr());
          }
        } else {
          if (takeProfitValue != 0 && takeProfitValue >= item.buyAvgPrice) {
            return GPEasyLoading.showToast("take_profit_below_entry".tr());
          } else if (stopLossValue != 0 && stopLossValue <= item.buyAvgPrice) {
            return GPEasyLoading.showToast("stop_loss_above_entry".tr());
          }
        }

        final flag = await cubit.setFuturesStopLine(
          positionId: item.id,
          takeProfitValue: takeProfitValue,
          stopLossValue: stopLossValue,
        );
        if (flag) {
          GPEasyLoading.showToast("success".tr());
          await Future.delayed(const Duration(milliseconds: 500));
          if (context.mounted) Navigator.of(context).pop();
        }
      },
    ).show();
  }

  /// 处理追加保证金 / Handle add margin
  static Future<void> _handleAddMargin(
    BuildContext context, {
    required FTradeAcctOrderRecords item,
    required OrderListType orderType,
  }) async {
    final cubit = context.read<AccountScreenCubitV2>();
    await TradeAddMarginDialog(
      context,
      data: item,
      availableMarginStream: cubit.stream.map((state) {
        final currentViewModel =
            state.spotViewModels.isEmpty ? null : state.spotViewModels[state.spotScreenCurrentIndex];
        if (currentViewModel == null) return item.availableMargin;

        final orderListState = currentViewModel.details[orderType];
        if (orderListState == null) return item.availableMargin;

        final updatedItem = orderListState.records.where((record) => record.id == item.id).firstOrNull;
        return updatedItem?.availableMargin ?? item.availableMargin;
      }).distinct(),
      onPressedSubmit: (amount) async {
        final flag = await cubit.addMargin(positionId: item.id, amount: amount);
        if (flag) {
          GPEasyLoading.showToast("success".tr());
          await Future.delayed(const Duration(milliseconds: 500));
          if (context.mounted) Navigator.of(context).pop();
        }
      },
    ).show();
  }

  /// 处理卖出操作 / Handle sell action
  static Future<void> _handleSell(
    FTradeAcctOrderRecords item, {
    int? contractAccountId,
    VoidCallback? onSellSuccess,
  }) async {
    if (item.restNum <= 0) {
      GPEasyLoading.showToast('noAvailablePosition'.tr());
      return;
    }

    final result = await getIt<NavigatorService>().push(
      AppRouter.routeTradeSell,
      arguments: {
        'record': item,
        'contractAccountId': contractAccountId,
      },
    );

    // 如果卖出成功，调用回调 / If sell successful, invoke callback
    if (result == true && onSellSuccess != null) {
      onSellSuccess();
    }
  }

  /// 处理持仓详情查看 / Handle position detail
  static Future<void> _handlePositionDetail(FTradeAcctOrderRecords item) async {
    await getIt<NavigatorService>().push(
      AppRouter.routeSpotPositionDetail,
      arguments: {'id': item.id},
    );
  }

  /// 处理成交明细/委托明细点击 / Handle trade/order detail tap
  static void _handleTradeOrOrderTap(
    BuildContext context,
    FTradeAcctOrderRecords item, {
    required OrderListType orderListType,
    required String? tradeAccountId,
    VoidCallback? onCancelSuccess,
  }) {
    final cubit = context.read<AccountScreenCubitV2>();

    TradeOrderSheet(
      order: item,
      orderListType: orderListType,
      tradeAccountId: tradeAccountId,
      onRefresh: () => cubit.fetchSpotScreenCurrentData(),
      onCancelSuccess: onCancelSuccess,
      onGoToQuotes: () => _handleGoToQuotes(context, item),
      onGoToTrade: () => _handleGoToTrade(context, item, tradeAccountId),
    ).show(context);
  }

  /// 处理跳转到行情页 / Handle go to quotes page
  static Future<void> _handleGoToQuotes(BuildContext context, FTradeAcctOrderRecords item) async {
    return _navigateToInstrument(
      context,
      item: item,
      futuresTab: TradeTabType.Quotes,
      spotTab: TradeTabType.Quotes,
      shouldSwitchInstrument: true, // 切换到该订单的标的并显示行情
    );
  }

  /// 处理跳转到交易页 / Handle go to trade page
  static Future<void> _handleGoToTrade(
      BuildContext context, FTradeAcctOrderRecords item, String? tradeAccountId) async {
    return _navigateToInstrument(
      context,
      item: item,
      futuresTab: TradeTabType.Trading,
      spotTab: TradeTabType.Trading,
      tradeAccountId: tradeAccountId,
      shouldSwitchInstrument: true, // 切换标的并切换到交易tab
    );
  }

  /// 处理委托撤单 / Handle order cancellation
  static void _handleOrderCancel(
    BuildContext context,
    FTradeAcctOrderRecords item,
    VoidCallback? onCancelSuccess,
  ) {
    final cubit = context.read<AccountScreenCubitV2>();

    CancelOrderDialog.show(
      context,
      orderId: item.id,
      onSuccess: () async {
        final currentRoute = RouteTracker().getCurrentRouteName();

        // 刷新订单列表和账户数据
        cubit.fetchSpotScreenCurrentData(forceRefresh: true);

        // 触发撤单成功回调
        onCancelSuccess?.call();

        if (!context.mounted) return;

        // 如果在现货交易中心 V2，刷新余额和订单列表
        if (currentRoute == AppRouter.routeTradingCenterV2) {
          try {
            final tradingCubitV2 = context.read<TradingCenterV2Cubit>();

            // 刷新余额（撤单会释放冻结资金）
            final isSpotAccount = tradingCubitV2.state.tradeForm.currentTradeAccount?.id == kDropDownValueSpotId ||
                tradingCubitV2.state.tradeForm.currentTradeAccount == null;
            if (isSpotAccount) {
              getIt<UserCubit>().fetchAccountInfo();
            } else {
              getIt<AccountScreenCubitV2>().fetchContractList();
            }

            // 全量刷新委托和持仓列表
            tradingCubitV2.refreshAllOrderLists();
          } catch (e) {
            debugPrint('Failed to refresh trading center v2: $e');
          }
        }

        // 如果在期货交易页面，刷新余额、委托列表和持仓列表
        if (currentRoute == AppRouter.routeFTradeAllInfo) {
          try {
            // 强制全量刷新委托列表
            final entrustCubit = context.read<FTradeAcctEntrustCubit>();
            entrustCubit.forceLoadAllData();

            // 强制全量刷新持仓列表
            final positionCubit = context.read<FTradeAcctPositionCubit>();
            positionCubit.forceLoadAllData();

            // 刷新余额
            getIt<UserCubit>().fetchAccountInfo();
          } catch (e) {
            debugPrint('Failed to refresh futures trading: $e');
          }
        }
      },
    );
  }

  /// 处理持仓单元格点击 / Handle position cell tap
  static Future<void> _handlePositionCellTap(
    BuildContext context, {
    required MarketCategory category,
    required FTradeAcctOrderRecords item,
    required String? tradeAccountId,
  }) async {
    return switch (category) {
      MarketCategory.cnFutures || MarketCategory.globalFutures => _handleFuturesPositionTap(context, item: item),
      MarketCategory.stockIndex => _handleStockIndexPositionTap(context, item: item),
      _ => _navigateToTradingCenterV2(
          context,
          item: item,
          tabType: TradeTabType.Trading,
          tradeAccountId: tradeAccountId,
        ),
    };
  }

  /// 构建账户订单单元格
  /// Build account order cell
  static Widget buildCell(
    BuildContext context, {
    required MarketCategory category,
    required FTradeAcctOrderRecords item,
    required int index,
    required bool isLast,
    required bool isIndexTrading,
    required OrderListType orderType,
    TradingAccountType? tradingAccountType,
    Contract? contractModel,
    bool allowTapCell = true,
    VoidCallback? onSellSuccess,
    VoidCallback? onCancelSuccess,
  }) {
    final tradeType = TradeTypeOption.fromValue(item.tradeType);
    final tradeColor = tradeType.color(context);

    // 根据账户类型决定传递的交易账户ID / Determine trade account ID based on account type
    final String? tradeAccountId = switch (tradingAccountType) {
      TradingAccountType.Contract => contractModel?.id.toString(),
      TradingAccountType.Spot => kDropDownValueSpotId,
      _ => null,
    };

    return switch (orderType) {
      OrderListType.positions => AccountPositionCell(
          debugIndex: index,
          marketCategory: category,
          data: item,
          onAction: (action) => switch (action) {
            PositionCellAction.tap => _handlePositionCellTap(
                context,
                category: category,
                item: item,
                tradeAccountId: tradeAccountId,
              ),
            PositionCellAction.tpSL => _handleTpSl(
                context,
                item: item,
                tradeType: tradeType,
                tradeColor: tradeColor,
              ),
            PositionCellAction.addMargin => _handleAddMargin(
                context,
                item: item,
                orderType: orderType,
              ),
            PositionCellAction.quote => switch (category) {
                MarketCategory.cnFutures ||
                MarketCategory.globalFutures =>
                  TradeNavigationHelper.goToFuturesTradingCenter(
                    context,
                    data: item.toFTradeListItemModel(),
                    marketCode: item.market,
                    scope: item.futuresScope,
                    tabType: TradeTabType.Quotes,
                  ),
                _ => _navigateToTradingCenterV2(
                    context,
                    item: item,
                    tabType: TradeTabType.Quotes,
                    tradeAccountId: tradeAccountId,
                  ),
              },
            PositionCellAction.sell => _handleSell(
                item,
                contractAccountId: contractModel?.id,
                onSellSuccess: onSellSuccess,
              ),
            PositionCellAction.detail => _handlePositionDetail(item),
          },
        ),
      OrderListType.trades => AccountTradeCell(
          data: item,
          isLast: isLast,
          onTap: () => _handleTradeOrOrderTap(
            context,
            item,
            orderListType: OrderListType.trades,
            tradeAccountId: tradeAccountId,
            onCancelSuccess: onCancelSuccess,
          ),
        ),
      OrderListType.orders => AccountEntrustCell(
          data: item,
          isLast: isLast,
          onTap: () => _handleTradeOrOrderTap(
            context,
            item,
            orderListType: OrderListType.orders,
            tradeAccountId: tradeAccountId,
            onCancelSuccess: onCancelSuccess,
          ),
          onTapCancelBtn: () => _handleOrderCancel(context, item, onCancelSuccess),
        ),
    };
  }
}
