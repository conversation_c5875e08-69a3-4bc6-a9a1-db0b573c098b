import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/core/models/entities/wallet/withdraw_config_entity.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/core/models/entities/wallet/user_bank_entity_list.dart';
import '../../domain/models/withdrawal_records/withdrawal_record.dart';

part 'withdraw_state.freezed.dart';

@freezed
class WithdrawalState with _$WithdrawalState {
  const factory WithdrawalState({
    @Default(DataStatus.idle) DataStatus withdrawStatus,
    @Default(DataStatus.idle) DataStatus withdrawConfigFetchStatus,
    @Default(DataStatus.idle) DataStatus recordsFetchStatus,
    WithdrawConfigEntity? withdrawalConfig,
    List<WithdrawalRecord>? records,
    int? current,
    int? total,
    String? error,
    String? withdrawAmount,
    UserBankEntity? selectedBank,
    bool? isFormValid,
  }) = _WithdrawalState;
}
