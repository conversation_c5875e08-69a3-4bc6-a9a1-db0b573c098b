import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/wallet.dart';
import 'package:gp_stock_app/core/models/apis/withdraw.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_bank_entity_list.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/account/domain/repository/bank_repository.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_state.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/app/extension/string_extension.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

import '../../domain/models/withdrawal_records/withdrawal_record.dart';

class WithdrawalCubit extends Cubit<WithdrawalState> {
  WithdrawalCubit() : super(const WithdrawalState());
  final _bankRepository = getIt<BankRepository>();
  final int _pageSize = 20;

  void withdraw({
    required String withdrawAmount,
    required int userBankId,
    required String password,
    int? type,
    int? channelId,
  }) async {
    if (state.withdrawStatus.isLoading) return;
    emit(state.copyWith(withdrawStatus: DataStatus.loading));
    try {
      final amount = int.parse(withdrawAmount);
      final flag = await WithdrawApi.applyWithdraw(
        amount: amount,
        userBankCardId: userBankId,
        password: password.toBase64(),
        type: type,
        channelId: channelId,
      );
      emit(state.copyWith(withdrawStatus: flag ? DataStatus.success : DataStatus.idle));
      if (flag) {
        Helper.showFlutterToast('withdrawalSubmittedSuccessfully'.tr());
      }
      getIt<NavigatorService>().popUntil(AppRouter.routeWithdrawMain);
    } on FormatException {
      // 金额格式不正确
      emit(state.copyWith(error: "tips_amount_invalid_format".tr(), withdrawStatus: DataStatus.failed));
    } on Exception catch (e) {
      emit(state.copyWith(error: e.toString(), withdrawStatus: DataStatus.failed));
    }
  }

  void updateSelectedBank(UserBankEntity? bank) {
    emit(state.copyWith(selectedBank: bank));
    validateForm();
  }

  void updateWithdrawAmount(String? amount) {
    emit(state.copyWith(withdrawAmount: amount));
    validateForm();
  }

  void validateForm() {
    final isValid = state.withdrawAmount.isNotNullNorEmpty && state.selectedBank != null;
    emit(state.copyWith(isFormValid: isValid));
  }

  void clearForm() {
    emit(state.copyWith(withdrawAmount: '', selectedBank: null));
  }

  void getConfig() async {
    if (state.withdrawConfigFetchStatus.isLoading) return;
    emit(state.copyWith(withdrawConfigFetchStatus: DataStatus.loading));
    final result = await WalletApi.getWithdrawalConfig();
    if (result != null) {
      emit(state.copyWith(withdrawConfigFetchStatus: DataStatus.success, withdrawalConfig: result));
    } else {
      emit(state.copyWith(withdrawConfigFetchStatus: DataStatus.failed));
    }
  }

  Future<void> getWithdrawalRecords({bool isLoadMore = false}) async {
    if (state.recordsFetchStatus == DataStatus.loading) return;

    final int pageNumber = isLoadMore ? (state.current ?? 0) + 1 : 1;

    if (!isLoadMore) {
      emit(state.copyWith(recordsFetchStatus: DataStatus.loading));
    }

    try {
      final result = await _bankRepository.getWithdrawalRecords(pageNumber, _pageSize);

      if (result.isSuccess && result.data != null) {
        // Use the pre-parsed records directly (no need to cast or re-parse)
        final List<WithdrawalRecord> parsedRecords = result.data!.records ?? [];

        // Combine records based on isLoadMore
        final List<WithdrawalRecord> currentList =
            isLoadMore ? (List<WithdrawalRecord>.from(state.records ?? [])..addAll(parsedRecords)) : parsedRecords;

        emit(state.copyWith(
          recordsFetchStatus: DataStatus.success,
          records: currentList,
          current: result.data?.current,
          total: result.data?.total,
        ));
      } else {
        emit(state.copyWith(
          recordsFetchStatus: DataStatus.failed,
          error: result.error ?? 'Failed to fetch withdrawal records',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        recordsFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }
}
