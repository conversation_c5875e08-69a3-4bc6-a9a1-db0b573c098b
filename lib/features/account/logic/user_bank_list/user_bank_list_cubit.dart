import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/models/apis/wallet.dart';
import 'package:gp_stock_app/core/models/entities/wallet/user_bank_entity_list.dart';
import 'package:gp_stock_app/features/account/domain/services/bank_service.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_state.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class UserBankListCubit extends Cubit<UserBankListState> {
  UserBankListCubit() : super(UserBankListState());
  final _bankRepository = BankService();

  void fetchBankList() async {
    emit(state.copyWith(bankListFetchStatus: DataStatus.loading));
    final result = await WalletApi.getUserBankList();
    emit(state.copyWith(bankList: result, bankListFetchStatus: DataStatus.success));
  }

  void addUserBank({
    required String bankCardNo,
    required int systemBankId,
    required String mobile,
    required String smsCode,
  }) async {
    if (state.bankListUpdateStatus == DataStatus.loading) return;
    emit(state.copyWith(bankListUpdateStatus: DataStatus.loading));

    BigInt cleanCardNo = BigInt.parse(bankCardNo.replaceAll(RegExp(r'[^0-9]'), ''));
    final isSuccess = await WalletApi.addUserBank(
        bankCardNo: cleanCardNo.toString(), systemBankId: systemBankId, mobile: mobile, smsCode: smsCode);
    if (isSuccess) {
      emit(state.copyWith(bankListUpdateStatus: DataStatus.success));
      fetchBankList();
    }
  }

  void deleteUserBank(int id) async {
    emit(state.copyWith(unbindBankStatus: (id: id, status: DataStatus.loading)));
    final isSuccess = await WalletApi.deleteUserBank(id);
    if (isSuccess) {
      emit(state.copyWith(
        unbindBankStatus: (id: null, status: DataStatus.success),
        selectedBank: () => state.selectedBank?.id == id ? null : state.selectedBank,
      ));
      GPEasyLoading.showToast('deleteSuccess'.tr());
      fetchBankList();
    } else {
      emit(state.copyWith(
        unbindBankStatus: (id: null, status: DataStatus.failed),
      ));
    }
  }

  void updateSelectedBank(UserBankEntity? bank) {
    emit(state.copyWith(selectedBank: () => bank));
  }

  void clearForm() {
    emit(state.copyWith(selectedBank: null));
  }

  void updateEditMode({bool? value}) {
    emit(state.copyWith(isEditMode: value ?? !state.isEditMode));
  }
}
