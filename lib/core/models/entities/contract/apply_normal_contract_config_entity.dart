import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/apply_normal_contract_config_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/apply_normal_contract_config_entity.g.dart';

@JsonSerializable()
class ApplyNormalContractConfigEntity {
	/// 保证金数据 / Margin data
	List<ApplyNormalContractConfigAmountList> amountList = [];
	/// 周期/倍数/利率 / Period/Multiple/Interest rate
	List<ApplyNormalContractConfigContractConfigMap> contractConfigMap = [];
	/// 余额法币类型 / Balance fiat currency type
	String currency = '';
	/// 当前用户账户利息券 / Current user account interest coupon
	int interestCash = 0;
	/// 普通合约的不同市场的风控数据 / Risk control data for different markets of normal contracts
	List<ApplyNormalContractConfigRuleMap> ruleMap = [];
	/// 当前用户账户可用余额 / Current user account available balance
	double useAmount = 0;

	ApplyNormalContractConfigEntity();

	factory ApplyNormalContractConfigEntity.fromJson(Map<String, dynamic> json) => $ApplyNormalContractConfigEntityFromJson(json);

	Map<String, dynamic> toJson() => $ApplyNormalContractConfigEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApplyNormalContractConfigAmountList {
	/// 保证金金额 / Margin amount
	int applyAmount = 0;
	/// 唯一标识 / Unique identifier
	int id = 0;

	ApplyNormalContractConfigAmountList();

	factory ApplyNormalContractConfigAmountList.fromJson(Map<String, dynamic> json) => $ApplyNormalContractConfigAmountListFromJson(json);

	Map<String, dynamic> toJson() => $ApplyNormalContractConfigAmountListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApplyNormalContractConfigContractConfigMap {
	List<ApplyNormalContractConfigContractConfigMapConfigList> configList = [];
	/// 周期类型 / Period type
	int periodType = 0;

	ApplyNormalContractConfigContractConfigMap();

	factory ApplyNormalContractConfigContractConfigMap.fromJson(Map<String, dynamic> json) => $ApplyNormalContractConfigContractConfigMapFromJson(json);

	Map<String, dynamic> toJson() => $ApplyNormalContractConfigContractConfigMapToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApplyNormalContractConfigContractConfigMapConfigList {
	/// 唯一标识 / Unique identifier
	int id = 0;
	/// 利率 / Interest rate
	double interestRate =0;
	/// 倍数 / Multiple/Leverage
	int multiple = 0;

	ApplyNormalContractConfigContractConfigMapConfigList();

	factory ApplyNormalContractConfigContractConfigMapConfigList.fromJson(Map<String, dynamic> json) => $ApplyNormalContractConfigContractConfigMapConfigListFromJson(json);

	Map<String, dynamic> toJson() => $ApplyNormalContractConfigContractConfigMapConfigListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApplyNormalContractConfigRuleMap {
	/// 平仓亏损比例 / Close position loss ratio
	int closeLossRadio = 0;
	/// 唯一标识 / Unique identifier
	int id = 0;
	/// 市场：A股 美股 港股 / Market: A-shares, US stocks, HK stocks
	String market = '';
	/// 预警亏损比例 / Warning loss ratio
	int warnLossRadio = 0;

	ApplyNormalContractConfigRuleMap();

	factory ApplyNormalContractConfigRuleMap.fromJson(Map<String, dynamic> json) => $ApplyNormalContractConfigRuleMapFromJson(json);

	Map<String, dynamic> toJson() => $ApplyNormalContractConfigRuleMapToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}
