#!/usr/bin/env bash

# ================================================================
# setup_flavor_keystores.sh
# Script to create flavor-specific JKS keystores for Android signing
# Requires bash 4.0+ for associative arrays
# ================================================================

# Common keystore settings
STORE_PASSWORD="gp@2025"
KEY_PASSWORD="gp@2025"
KEY_ALIAS="upload"
VALIDITY_DAYS=10000

# Base directory for keystores
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
JKS_BASE_DIR="$PROJECT_ROOT/android/app/keystores"

# Flavor configurations
declare -A FLAVORS=(
    ["PRE"]="GP Pre"
    ["GP"]="GP Stock"
    ["RSYP"]="荣顺优配"
    ["YHXT"]="沅和信投"
    ["TEMPA"]="tempa"
    ["BSZB"]="宝石资本"
    ["DYZB"]="德盈资本"
    ["XYZQ"]="鑫元優策略"
)

# Function to create keystore for a flavor
create_keystore() {
    local flavor=$1
    local app_name=$2
    local flavor_lower=$(echo "$flavor" | tr '[:upper:]' '[:lower:]')
    local keystore_path="$JKS_BASE_DIR/${flavor_lower}-key.jks"
    
    echo "Creating keystore for $flavor ($app_name)..."

    # Create directory if it doesn't exist
    mkdir -p "$JKS_BASE_DIR"
    
    # Check if keystore already exists
    if [ -f "$keystore_path" ]; then
        echo "⚠️  Keystore already exists: $keystore_path"
        read -p "Do you want to overwrite it? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Skipping $flavor..."
            return
        fi
        rm -f "$keystore_path"
    fi
    
    # Generate keystore
    keytool -genkey \
        -v \
        -keystore "$keystore_path" \
        -alias "$KEY_ALIAS" \
        -keyalg RSA \
        -keysize 2048 \
        -validity $VALIDITY_DAYS \
        -storepass "$STORE_PASSWORD" \
        -keypass "$KEY_PASSWORD" \
        -dname "CN=$app_name, OU=Mobile Development, O=GP Stock, L=City, ST=State, C=US"
    
    if [ $? -eq 0 ]; then
        echo "✅ Successfully created keystore: $keystore_path"
    else
        echo "❌ Failed to create keystore for $flavor"
    fi
    echo
}

# Function to list existing keystores
list_keystores() {
    echo "📋 Existing keystores:"
    echo "====================="

    for flavor in "${!FLAVORS[@]}"; do
        local flavor_lower=$(echo "$flavor" | tr '[:upper:]' '[:lower:]')
        local keystore_path="$JKS_BASE_DIR/${flavor_lower}-key.jks"
        if [ -f "$keystore_path" ]; then
            echo "✅ $flavor: $keystore_path"
        else
            echo "❌ $flavor: Missing"
        fi
    done
    echo
}

# Function to verify keystore
verify_keystore() {
    local flavor=$1
    local flavor_lower=$(echo "$flavor" | tr '[:upper:]' '[:lower:]')
    local keystore_path="$JKS_BASE_DIR/${flavor_lower}-key.jks"

    if [ ! -f "$keystore_path" ]; then
        echo "❌ Keystore not found: $keystore_path"
        return 1
    fi

    echo "🔍 Verifying keystore for $flavor..."
    keytool -list -v -keystore "$keystore_path" -storepass "$STORE_PASSWORD"
}

# Main script logic
case "${1:-help}" in
    "create")
        if [ -n "$2" ]; then
            # Create specific flavor
            flavor=$(echo "$2" | tr '[:lower:]' '[:upper:]')
            if [[ -n "${FLAVORS[$flavor]}" ]]; then
                create_keystore "$flavor" "${FLAVORS[$flavor]}"
            else
                echo "❌ Unknown flavor: $2"
                echo "Available flavors: ${!FLAVORS[@]}"
                exit 1
            fi
        else
            # Create all keystores
            echo "🔑 Creating keystores for all flavors..."
            echo "========================================"
            for flavor in "${!FLAVORS[@]}"; do
                create_keystore "$flavor" "${FLAVORS[$flavor]}"
            done
        fi
        ;;
    "list")
        list_keystores
        ;;
    "verify")
        if [ -n "$2" ]; then
            flavor=$(echo "$2" | tr '[:lower:]' '[:upper:]')
            verify_keystore "$flavor"
        else
            echo "❌ Please specify a flavor to verify"
            echo "Usage: $0 verify <flavor>"
            exit 1
        fi
        ;;
    "help"|*)
        echo "🔑 Flavor Keystore Management Script"
        echo "===================================="
        echo
        echo "Usage: $0 <command> [options]"
        echo
        echo "Commands:"
        echo "  create [flavor]  - Create keystore(s). If no flavor specified, creates all"
        echo "  list            - List all existing keystores"
        echo "  verify <flavor> - Verify a specific keystore"
        echo "  help            - Show this help message"
        echo
        echo "Available flavors: ${!FLAVORS[@]}"
        echo
        echo "Examples:"
        echo "  $0 create           # Create all keystores"
        echo "  $0 create gp        # Create only GP keystore"
        echo "  $0 list             # List existing keystores"
        echo "  $0 verify gp        # Verify GP keystore"
        ;;
esac
