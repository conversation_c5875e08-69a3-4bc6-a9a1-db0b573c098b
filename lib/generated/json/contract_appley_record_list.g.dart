import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract_appley_record_list.dart';
import 'package:gp_stock_app/core/utils/contract_utils.dart';


ContractAppleyRecordList $ContractAppleyRecordListFromJson(Map<String, dynamic> json) {
  return ContractAppleyRecordList(current: jsonConvert.convert<int>(json['current']) ?? 0,
      hasNext: jsonConvert.convert<bool>(json['hasNext']) ?? false,
      records: jsonConvert.convertListNotNull<ContractAppleyRecord>(json['records']) ?? <ContractAppleyRecord>[],
      total: jsonConvert.convert<int>(json['total']) ?? 0);
}

Map<String, dynamic> $ContractAppleyRecordListToJson(ContractAppleyRecordList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['hasNext'] = entity.hasNext;
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension ContractAppleyRecordListExtension on ContractAppleyRecordList {
  ContractAppleyRecordList copyWith({
    int? current,
    bool? hasNext,
    List<ContractAppleyRecord>? records,
    int? total,
  }) {
    return ContractAppleyRecordList(current: current ?? this.current,
        hasNext: hasNext ?? this.hasNext,
        records: records ?? this.records,
        total: total ?? this.total);
  }
}

ContractAppleyRecord $ContractAppleyRecordFromJson(Map<String, dynamic> json) {
  return ContractAppleyRecord(auditReason: jsonConvert.convert<String>(json['auditReason']) ?? '',
      auditStatus: jsonConvert.convert<int>(json['auditStatus']) ?? 0,
      auditTime: jsonConvert.convert<dynamic>(json['auditTime']),
      createTime: jsonConvert.convert<String>(json['createTime']) ?? '',
      id: jsonConvert.convert<int>(json['id']) ?? 0,
      marketType: jsonConvert.convert<String>(json['marketType']) ?? '',
      multiple: jsonConvert.convert<int>(json['multiple']) ?? 0,
      periodType: jsonConvert.convert<int>(json['periodType']) ?? 0,
      totalCash: jsonConvert.convert<int>(json['totalCash']) ?? 0,
      totalPower: jsonConvert.convert<int>(json['totalPower']) ?? 0,
      type: jsonConvert.convert<int>(json['type']) ?? 0);
}

Map<String, dynamic> $ContractAppleyRecordToJson(ContractAppleyRecord entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['auditReason'] = entity.auditReason;
  data['auditStatus'] = entity.auditStatus;
  data['auditTime'] = entity.auditTime;
  data['createTime'] = entity.createTime;
  data['id'] = entity.id;
  data['marketType'] = entity.marketType;
  data['multiple'] = entity.multiple;
  data['periodType'] = entity.periodType;
  data['totalCash'] = entity.totalCash;
  data['totalPower'] = entity.totalPower;
  data['type'] = entity.type;
  return data;
}

extension ContractAppleyRecordExtension on ContractAppleyRecord {
  ContractAppleyRecord copyWith({
    String? auditReason,
    int? auditStatus,
    dynamic auditTime,
    String? createTime,
    int? id,
    String? marketType,
    int? multiple,
    int? periodType,
    int? totalCash,
    int? totalPower,
    int? type,
  }) {
    return ContractAppleyRecord(auditReason: auditReason ?? this.auditReason,
        auditStatus: auditStatus ?? this.auditStatus,
        auditTime: auditTime ?? this.auditTime,
        createTime: createTime ?? this.createTime,
        id: id ?? this.id,
        marketType: marketType ?? this.marketType,
        multiple: multiple ?? this.multiple,
        periodType: periodType ?? this.periodType,
        totalCash: totalCash ?? this.totalCash,
        totalPower: totalPower ?? this.totalPower,
        type: type ?? this.type);
  }
}

ContractAvailableType $ContractAvailableTypeFromJson(Map<String, dynamic> json) {
  final ContractAvailableType contractAvailableType = ContractAvailableType();
  final List<int>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<int>(e) as int).toList();
  if (list != null) {
    contractAvailableType.list = list;
  }
  return contractAvailableType;
}

Map<String, dynamic> $ContractAvailableTypeToJson(ContractAvailableType entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list;
  return data;
}

extension ContractAvailableTypeExtension on ContractAvailableType {
  ContractAvailableType copyWith({
    List<int>? list,
  }) {
    return ContractAvailableType()
      ..list = list ?? this.list;
  }
}