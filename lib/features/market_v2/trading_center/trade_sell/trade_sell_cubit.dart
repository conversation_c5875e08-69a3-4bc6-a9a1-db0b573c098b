import 'dart:async';
import 'dart:math' as math;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/trade.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_service.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/utils/trade_calculator.dart';
import 'package:gp_stock_app/core/utils/money_util/fee_calculator.dart';
import 'package:gp_stock_app/shared/app/extension/web_socket_extension.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/web_socket_actions.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/models/web_socket_message.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/mixins/web_socket_reconnect_aware_mixin.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_socket_interface.dart';

import 'trade_sell_state.dart';

class TradeSellCubit extends WebSocketReconnectAwareCubit<TradeSellState> {
  TradeSellCubit({
    required FTradeAcctOrderRecords record,
    int? contractAccountId,
  }) : super(TradeSellState(
          record: record,
          contractAccountId: contractAccountId,
          currentPrice: record.stockPrice,
          inputPrice: record.stockPrice > 0 ? record.stockPrice : record.buyAvgPrice,
          sellQuantity: record.restNum,
        )) {
    // 初始化时计算总价和手续费 / Calculate total price and fee on init
    _initializeSummary();

    // 订阅 WebSocket 行情更新 / Subscribe to WebSocket quote updates
    _stockInfoSubscription =
        _webSocketService.onMessageWithAction('Q', loginRequired: true).listen(_onWebSocketStockInfoUpdate);

    // 订阅标的的 WebSocket 消息 / Subscribe to WebSocket messages for instrument
    _subscribeToMarketSymbols();

    // 加载手续费配置 / Load fee configs
    _loadFeeConfigs();

    // 加载股票信息（涨跌停价格、手数、精度）/ Load stock info (price limits, lot size, precision)
    _loadStockInfo();
  }

  final WebSocketService _webSocketService = getIt<WebSocketService>();

  // WebSocket 订阅对象，用于在 close 时清理 / WebSocket subscription for cleanup in close()
  StreamSubscription<WebSocketMessage>? _stockInfoSubscription;

  // 股票信息加载重试次数（内部临时变量，不存储在 state）/ Stock info load retry count (internal temp variable, not stored in state)
  int _loadRetryCount = 0;

  /// 获取标的标识符 / Get instrument identifier
  String get _instrument => '${state.record.market}|${state.record.securityType}|${state.record.symbol}';

  /// 初始化时计算汇总信息 / Initialize summary on construction
  void _initializeSummary() {
    final summary = _calculateSummary(state.sellQuantity, null);
    emit(state.copyWith(
      totalAmount: summary.totalPrice,
      fee: summary.fee,
    ));
  }

  /// 加载手续费配置 / Load fee configs
  Future<void> _loadFeeConfigs() async {
    if (state.record.isFutures) {
      // 国际期货：使用 FTradeConfigModel / International futures: use FTradeConfigModel
      final config = await FTradeService.fetchFTradeConfig(instrument: _instrument);
      if (config != null) {
        emit(state.copyWith(futuresConfig: config));

        // 重新计算总价（如果已经有数量输入）/ Recalculate total if quantity is already set
        if (state.sellQuantity > 0) {
          final summary = _calculateSummary(state.sellQuantity, null);
          emit(state.copyWith(
            totalAmount: summary.totalPrice,
            fee: summary.fee,
          ));
        }
      }
    } else {
      // 现货/股票：使用原有接口 / Spot/Stocks: use existing API
      final closeConfig = await TradeApi.fetchHandingFeeConfigList(
        market: state.record.market, // 市场 / Market
        direction: TradeDirection.close.value, // 平仓方向 / Close direction
        securityType: state.record.securityType, // 证券类型 / Security type
      );

      if (closeConfig != null && closeConfig.isNotEmpty) {
        emit(state.copyWith(feeConfigs: closeConfig));

        // 重新计算总价（如果已经有数量输入）
        // Recalculate total if quantity is already set
        if (state.sellQuantity > 0) {
          final summary = _calculateSummary(state.sellQuantity, null);
          emit(state.copyWith(
            totalAmount: summary.totalPrice,
            fee: summary.fee,
          ));
        }
      }
    }
  }

  /// 切换订单类型（市价/限价）
  void setOrderType(OrderPriceType orderPriceType) {
    // 切换到限价单时，使用当前价格作为初始价格
    if (orderPriceType == OrderPriceType.limit) {
      final currentPrice = state.currentPrice > 0 ? state.currentPrice : state.record.buyAvgPrice;
      final summary = _calculateSummary(state.sellQuantity, currentPrice);
      emit(state.copyWith(
        orderPriceType: orderPriceType,
        inputPrice: currentPrice,
        totalAmount: summary.totalPrice,
        fee: summary.fee,
      ));
    } else {
      // 市价单也显示总价
      final summary = _calculateSummary(state.sellQuantity, null);
      emit(state.copyWith(
        orderPriceType: orderPriceType,
        totalAmount: summary.totalPrice,
        fee: summary.fee,
      ));
    }
  }

  /// 增加价格（按 tick size）/ Increment price by tick size
  void incrementPrice() {
    final tickSize = state.priceTickSize;
    if (tickSize <= 0) return;

    final newPrice = state.inputPrice + tickSize;
    onInputPriceChanged(newPrice);
  }

  /// 减少价格（按 tick size）/ Decrement price by tick size
  void decrementPrice() {
    final tickSize = state.priceTickSize;
    if (tickSize <= 0) return;

    final newPrice = state.inputPrice - tickSize;
    onInputPriceChanged(newPrice);
  }

  /// 增加卖出数量（按步进值）/ Increment sell quantity by step
  void incrementQuantity() {
    final step = state.quantityStep.toDouble();
    final maxQty = state.record.restNum;
    final newQty = state.sellQuantity + step;
    if (newQty <= maxQty) {
      final summary = _calculateSummary(newQty, null);
      // 自动匹配比例 / Auto match fraction
      final matchedFraction = _matchQuantityToFraction(newQty);
      emit(state.copyWith(
        sellQuantity: newQty,
        selectedFraction: () => matchedFraction,
        totalAmount: summary.totalPrice,
        fee: summary.fee,
      ));
    }
  }

  /// 减少卖出数量（按步进值）/ Decrement sell quantity by step
  void decrementQuantity() {
    final step = state.quantityStep;
    final newQty = state.sellQuantity - step;
    // 最小值是一手 / Minimum is one lot
    if (newQty >= step) {
      final summary = _calculateSummary(newQty, null);
      // 自动匹配比例 / Auto match fraction
      final matchedFraction = _matchQuantityToFraction(newQty);
      emit(state.copyWith(
        sellQuantity: newQty,
        selectedFraction: () => matchedFraction,
        totalAmount: summary.totalPrice,
        fee: summary.fee,
      ));
    }
  }

  /// 手动输入数量（会对齐到步进值）/ Manual quantity input (aligned to step)
  double onQuantityInputChanged(double quantity) {
    final maxQty = state.record.restNum;
    final step = state.quantityStep;

    // 先限制范围，再对齐到步进值 / Clamp to range, then align to step
    // 允许输入任意值，由 TradeCalculator.alignToLotSize 自动对齐到步进值 / Allow any input, auto-align by alignToLotSize
    final clampedQty = quantity.clamp(0, maxQty).toDouble();
    var alignedQty = TradeCalculator.alignToLotSize(
      quantity: clampedQty,
      lotSize: step,
    );

    // 确保对齐后不超出最大数量（防止四舍五入导致超出）
    // Ensure aligned quantity doesn't exceed max (prevent rounding up beyond max)
    if (alignedQty > maxQty) {
      alignedQty = TradeCalculator.alignToLotSize(
        quantity: maxQty,
        lotSize: state.quantityStep,
      );
    }

    final summary = _calculateSummary(alignedQty, null);

    // 根据输入数量反推比例选中状态（参考 TradingCenterV2Cubit）
    // Match quantity back to fraction selection (reference TradingCenterV2Cubit)
    final matchedFraction = _matchQuantityToFraction(alignedQty);

    emit(state.copyWith(
      sellQuantity: alignedQty,
      selectedFraction: () => matchedFraction, // 自动匹配比例 / Auto match fraction
      totalAmount: summary.totalPrice,
      fee: summary.fee,
    ));
    return alignedQty;
  }

  /// 选择仓位比例 / Select position fraction
  void onFractionChanged(OrderFraction fraction) {
    final maxQty = state.record.restNum;

    // 使用 TradeCalculator 对齐到手数（参考 trading_center_v2）
    // Use TradeCalculator to align to lot size (reference trading_center_v2)
    final newQty = TradeCalculator.calculateQuantityByRatio(
      maxQuantity: maxQty,
      ratio: fraction.fraction,
      lotSize: state.quantityStep,
    );

    final summary = _calculateSummary(newQty, null);
    emit(state.copyWith(
      sellQuantity: newQty,
      selectedFraction: () => fraction,
      totalAmount: summary.totalPrice,
      fee: summary.fee,
    ));
  }

  /// 设置限价单价格 / Set limit order price
  ///
  /// [needClamp] 是否需要 clamp 到涨跌停范围，默认 true
  /// 实时输入时传 false，失焦验证时传 true
  double onInputPriceChanged(double price, {bool needClamp = true}) {
    // A股价格限制（涨跌停）/ A-Share price limit (price up/down limited)
    final effectivePrice = needClamp ? _clampPrice(price) : price;

    // 舍入到有效精度（参考 trading_center_v2）/ Round to valid precision (reference trading_center_v2)
    final roundedPrice = TradeCalculator.roundToTick(effectivePrice, state.pricePrecision);

    final summary = _calculateSummary(state.sellQuantity, roundedPrice);
    emit(state.copyWith(
      inputPrice: roundedPrice,
      totalAmount: summary.totalPrice,
      fee: summary.fee,
    ));
    return roundedPrice;
  }

  /// 对A股市场的价格进行涨跌停限制 / Clamp price for A-Share market
  ///
  /// A股限价单不得低于跌停价，不得高于涨停价
  /// A-Share limit orders cannot be lower than price down limited or higher than price up limited
  /// 非A股市场的限制为0和infinity，clamp不会有任何效果
  double _clampPrice(double price) {
    // 判断是否为A股市场 / Check if it's A-Share market
    final isAShare = _isAShareMarket();

    // 只有A股才需要限制（非A股的限制为0和infinity，不需要执行clamp）
    // Only A-Share needs clamping (non-A-Share limits are 0 and infinity, no need to clamp)
    if (isAShare) {
      return price.clamp(state.priceDownLimited, state.priceUpLimited);
    }

    return price;
  }

  /// 判断是否为A股市场 / Check if it's A-Share market
  bool _isAShareMarket() {
    // 使用 MarketCategory 判断（参考 TradingCenterV2Cubit）
    // Use MarketCategory to determine (reference TradingCenterV2Cubit)
    final marketCategory = MarketCategory.fromSecurity(
      state.record.market,
      state.record.securityType,
    );
    return marketCategory == MarketCategory.cnStocks;
  }

  /// 根据数量反推仓位比例 / Match quantity back to fraction selection
  ///
  /// 参考 TradingCenterV2Cubit._matchQuantityToFraction 实现
  /// Reference TradingCenterV2Cubit._matchQuantityToFraction implementation
  OrderFraction? _matchQuantityToFraction(double quantity) {
    if (quantity <= 0) return null;

    final maxQty = state.record.restNum; // 可卖数量 / Available quantity
    if (maxQty <= 0) return null;

    // 遍历所有比例，找到匹配的 / Iterate all fractions to find match
    for (final fraction in OrderFraction.values) {
      final ratioQty = TradeCalculator.calculateQuantityByRatio(
        maxQuantity: maxQty,
        ratio: fraction.fraction,
        lotSize: state.quantityStep,
      );

      if (ratioQty == quantity) {
        return fraction;
      }
    }

    return null;
  }

  /// 用户手动重试加载股票信息（点击重试按钮触发）/ User manually retry loading stock info (triggered by retry button)
  ///
  /// 重置重试次数，重新开始3次静默重试
  /// Reset retry count and restart 3 silent retries
  Future<void> retryLoadStockInfo() async {
    await _loadStockInfo(isRetry: false);
  }

  /// 加载股票/期货信息（含涨跌停价格、手数、精度）/ Load stock/futures info (including price limits, lot size, precision)
  ///
  /// 静默自动重试机制：失败后自动重试最多3次，不显示重试进度给用户
  /// Silent auto-retry mechanism: auto-retry up to 3 times on failure, without showing retry progress to user
  Future<void> _loadStockInfo({bool isRetry = false}) async {
    // 首次加载时重置重试次数 / Reset retry count on first load
    if (!isRetry) {
      _loadRetryCount = 0;
    }

    // 设置加载状态 / Set loading status
    emit(state.copyWith(stockInfoStatus: DataStatus.loading));

    try {
      if (state.record.isFutures) {
        await _loadFuturesConfig();
      } else {
        await _loadStockConfig();
      }
      // 加载成功 / Load success
      emit(state.copyWith(stockInfoStatus: DataStatus.success));
    } catch (e) {
      debugPrint('TradeSellCubit: 加载证券信息失败 / Failed to load security info (retry: $_loadRetryCount/3): $e');

      // 自动重试逻辑 / Auto-retry logic
      if (_loadRetryCount < 3) {
        _loadRetryCount++;
        // 延迟1秒后重试 / Retry after 1 second delay
        await Future.delayed(const Duration(seconds: 1));
        // 递归重试 / Recursive retry
        if (!isClosed) {
          await _loadStockInfo(isRetry: true);
        }
      } else {
        // 3次重试后仍失败，设置失败状态 / Set failed status after 3 retries
        emit(state.copyWith(stockInfoStatus: DataStatus.failed));
      }
    }
  }

  /// 加载股票配置 / Load stock configuration
  Future<void> _loadStockConfig() async {
    final stockInfo = await TradeApi.fetchStockInfo(instrument: _instrument);

    if (stockInfo == null) {
      throw Exception('股票信息获取失败，无法继续操作 / Stock info fetch failed, cannot proceed');
    }

    final isAShare = _isAShareMarket();
    final pricePrecision = stockInfo.precision > 0 ? stockInfo.precision : 2;
    final quantityStep = stockInfo.lotSize;

    // 只有A股才更新涨跌停价格，其他市场保持默认值（0 和 infinity）
    // Only update price limits for A-Share, other markets keep default values (0 and infinity)
    emit(state.copyWith(
      priceDownLimited: isAShare ? stockInfo.priceDownLimited : null,
      priceUpLimited: isAShare ? stockInfo.priceUpLimited : null,
      quantityStep: quantityStep,
      pricePrecision: pricePrecision,
      priceTickSize: 1.0 / math.pow(10, pricePrecision),
    ));

    _revalidateLimitPrice();
  }

  /// 加载期货/股指配置 / Load futures/index configuration
  Future<void> _loadFuturesConfig() async {
    final futuresConfig = await FTradeService.fetchFTradeConfig(instrument: _instrument);

    if (futuresConfig == null) {
      throw Exception('期货配置获取失败，无法继续操作 / Futures config fetch failed, cannot proceed');
    }

    // 期货使用 minTradeQuantity 作为最小交易单位
    // Use minTradeQuantity as minimum trading unit for futures
    final quantityStep = futuresConfig.minTradeQuantity > 0 ? futuresConfig.minTradeQuantity : 1.0;

    // 期货的价格精度，通常为2-3位小数
    // Price precision for futures, usually 2-3 decimal places
    final pricePrecision = 2; // 期货默认2位精度，如果接口有提供则用接口值

    emit(state.copyWith(
      quantityStep: quantityStep,
      pricePrecision: pricePrecision,
      priceTickSize: 1.0 / math.pow(10, pricePrecision),
    ));

    _revalidateLimitPrice();
  }

  /// 重新验证限价单价格 / Revalidate limit price
  void _revalidateLimitPrice() {
    if (state.orderPriceType == OrderPriceType.limit && state.inputPrice > 0) {
      final clampedPrice = _clampPrice(state.inputPrice);
      if (clampedPrice != state.inputPrice) {
        final summary = _calculateSummary(state.sellQuantity, clampedPrice);
        emit(state.copyWith(
          inputPrice: clampedPrice,
          totalAmount: summary.totalPrice,
          fee: summary.fee,
        ));
      }
    }
  }

  /// 更新当前市场价格（可选，用于实时更新价格）
  /// Update current market price (optional, for real-time price updates)
  void updateCurrentPrice(double price) {
    if (state.currentPrice == price) return;

    // 只有市价单需要重新计算总价
    // Only market orders need to recalculate total
    if (state.orderPriceType == OrderPriceType.market) {
      final summary = _calculateSummary(state.sellQuantity, null);
      emit(state.copyWith(
        currentPrice: price,
        totalAmount: summary.totalPrice,
        fee: summary.fee,
      ));
    } else {
      emit(state.copyWith(currentPrice: price));
    }
  }

  /// 计算交易汇总（含手续费）
  /// Calculate trade summary (including fees)
  ///
  /// [quantity] 交易数量 / Trade quantity
  /// [overridePrice] 覆盖价格（用于限价单）/ Override price (for limit orders)
  ///
  /// **注意：卖出计算逻辑** / Note: Sell calculation logic
  /// 卖出时用户得到的金额 = 订单金额 - 手续费
  /// When selling, user receives = order amount - fee
  ({double totalPrice, double fee}) _calculateSummary(double quantity, double? overridePrice) {
    if (quantity <= 0) {
      return (totalPrice: 0.0, fee: 0.0);
    }

    // 获取有效价格 / Get effective price
    final price = _getEffectivePrice(overridePrice);
    if (price <= 0) {
      return (totalPrice: 0.0, fee: 0.0);
    }

    // 订单金额 / Order amount
    final orderAmount = price * quantity;
    double totalFee;

    if (state.record.isFutures) {
      // 国际期货：使用 FeeCalculator.calculateFutureTradeHandlingFee
      // International futures: use FeeCalculator.calculateFutureTradeHandlingFee
      final config = state.futuresConfig;
      if (config == null) {
        // 期货配置未加载，手续费暂为 0 / Futures config not loaded, fee is 0 for now
        totalFee = 0.0;
      } else {
        totalFee = FeeCalculator.calculateFutureTradeHandlingFee(
          tradeAmount: orderAmount,
          tradeNum: quantity,
          calculateType: config.sellCalculateType,
          calculateValue: config.sellCalculateValue,
          currency: config.currency,
        );
      }
    } else {
      // 股票/现货：使用原有计算方式 / Stocks/Spot: use existing calculation
      final summary = TradeCalculator.calculateTradeSummary(
        price: price,
        quantity: quantity,
        feeConfigs: state.feeConfigs,
        quantityStep: state.quantityStep,
        isStockIndex: state.record.isIndex,
        currency: state.record.currency,
      );
      totalFee = summary.fee;
    }

    // ⚠️ 卖出逻辑：总价 = 订单金额 - 手续费（用户实际得到的金额）
    // ⚠️ Sell logic: total = order amount - fee (actual amount user receives)
    final sellTotalPrice = orderAmount - totalFee;

    return (totalPrice: sellTotalPrice, fee: totalFee);
  }

  /// 获取有效价格 / Get effective price
  ///
  /// 市价单使用当前市场价格，限价单使用输入价格
  /// Market order uses current price, limit order uses input price
  double _getEffectivePrice([double? overridePrice]) {
    if (overridePrice != null) {
      return overridePrice;
    }

    if (state.orderPriceType == OrderPriceType.market) {
      // 市价单：使用当前价格 / Market order: use current price
      return state.currentPrice > 0 ? state.currentPrice : state.record.buyAvgPrice;
    } else {
      // 限价单：使用输入价格 / Limit order: use input price
      return state.inputPrice;
    }
  }

  /// 执行卖出操作 / Execute sell operation
  Future<bool> executeSell() async {
    // 防止重复提交 / Prevent duplicate submission
    if (state.isLoading) {
      return false;
    }

    // 设置loading状态 / Set loading state
    emit(state.copyWith(isLoading: true));

    try {
      // 获取实际成交价格（市价单使用当前价格，限价单使用用户输入价格）
      // Get actual deal price (market order uses current price, limit order uses user input price)
      final dealPrice = state.orderPriceType == OrderPriceType.market ? state.record.stockPrice : state.inputPrice;

      // 调用API创建订单（自动根据 productCode 区分期货和现货）
      // Call API to create order (automatically distinguishes futures and spot by productCode)
      final success = await TradeApi.createOrder(
        contractAccountId: state.contractAccountId,
        currency: state.record.currency,
        direction: 2,
        expireType: 2,
        market: state.record.market,
        priceType: state.orderPriceType.value,
        securityType: state.record.securityType,
        symbol: state.record.symbol,
        symbolName: state.record.symbolName,
        tradeNum: state.sellQuantity,
        tradePrice: dealPrice,
        tradeType: state.record.tradeType,
        positionId: state.record.id,
        productCode: state.record.isFutures ? state.record.productCode : null,
        multiple: state.record.isFutures ? state.record.tradeUnit.toInt() : null,
      );

      if (success) {
        GPEasyLoading.showToast('successCommission'.tr()); // 委托成功

        // 刷新余额 / Refresh balance
        final isSpotAccount = state.contractAccountId == null;

        if (isSpotAccount) {
          // 现货账户：刷新账户信息（通过 BlocListener 自动同步）/ Spot account: refresh account info (auto-synced via BlocListener)
          await getIt<UserCubit>().fetchAccountInfo();
        } else {
          // 合约账户：刷新合约列表（通过 BlocListener 自动同步余额）/ Contract account: refresh contract list (balance auto-synced via BlocListener)
          await getIt<AccountScreenCubitV2>().fetchContractList();
        }

        // 刷新交易中心的所有订单列表（持仓+委托）/ Refresh all order lists in trading center (positions + orders)
        try {
          await getIt<TradingCenterV2Cubit>().refreshAllOrderLists();
        } catch (e) {
          debugPrint('TradeSellCubit: 刷新订单列表失败 / Failed to refresh order lists: $e');
        }

        // 关闭 dialog 和 TradeSellScreen / Close dialog and TradeSellScreen
        getIt<NavigatorService>().pop(); // 关闭 dialog
        getIt<NavigatorService>().pop(result: true); // 关闭 TradeSellScreen，传递结果
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('卖出订单失败 / Sell order failed: $e');
      return false;
    } finally {
      // 取消loading状态 / Cancel loading state
      if (!isClosed) {
        emit(state.copyWith(isLoading: false));
      }
    }
  }

  // ==================== WebSocket 订阅和消息处理 / WebSocket Subscription and Message Handling ====================

  /// 订阅标的的 WebSocket 消息 / Subscribe to WebSocket messages for instrument
  void _subscribeToMarketSymbols() {
    final security = Security(instrument: _instrument);
    final symbols = _buildSubscriptionSymbols(security: security, actions: ['Q']);
    _webSocketService.send({'action': SocketActions.subscribe, 'symbols': symbols});
  }

  /// 取消订阅标的的 WebSocket 消息 / Unsubscribe from WebSocket messages for instrument
  void _unsubscribeFromMarketSymbols() {
    final security = Security(instrument: _instrument);
    final symbols = _buildSubscriptionSymbols(security: security, actions: ['Q']);
    _webSocketService.send({'action': SocketActions.unsubscribe, 'symbols': symbols});
  }

  /// 构建订阅字符串 / Build subscription symbols
  String _buildSubscriptionSymbols({
    required Security security,
    List<String> actions = const ['Q'],
  }) {
    return actions
        .map((action) => [
              security.market,
              security.securityType,
              action,
              security.symbol,
            ].join('|'))
        .join(',');
  }

  /// WebSocket 回调：处理行情更新 (Action: 'Q') / WebSocket callback: Handle quote updates (Action: 'Q')
  void _onWebSocketStockInfoUpdate(WebSocketMessage message) {
    // 验证消息 / Validate message
    final filteredMessage = _validateWebSocketMessage(message);
    if (filteredMessage == null) return;

    // 解析行情数据 / Parse quote data
    final quoteData = filteredMessage.toQuoteData();
    if (quoteData == null) return;

    // 更新当前价格 / Update current price
    try {
      updateCurrentPrice(quoteData.latestPrice);
    } catch (e) {
      debugPrint('TradeSellCubit: 更新价格失败 / Failed to update price: $e');
    }
  }

  /// 返回经过验证和过滤的消息，验证失败返回 null / Returns validated and filtered message, or null if validation fails
  WebSocketMessage? _validateWebSocketMessage(WebSocketMessage message) {
    // 检查 Cubit 是否已关闭 / Check if cubit is closed
    if (isClosed) return null;

    // 检查市场是否开盘 / Check if market is open
    final isCurrentMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(state.record.market);
    if (!isCurrentMarketOpen) return null;

    // 过滤消息：只处理当前标的的消息 / Filter message: only process messages for current instrument
    final security = Security(instrument: _instrument);
    return message.withInstrument(security);
  }

  @override
  void onWebSocketReconnected() {
    // 先取消旧订阅，再重新订阅当前标的的行情数据
    _unsubscribeFromMarketSymbols();
    _subscribeToMarketSymbols();
  }

  @override
  Future<void> close() {
    // 取消订阅和清理资源 / Unsubscribe and cleanup resources
    _stockInfoSubscription?.cancel();
    _unsubscribeFromMarketSymbols();
    return super.close();
  }
}
