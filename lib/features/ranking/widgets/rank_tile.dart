import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/ranking/ranking_entity.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/avatar/app_avatar.dart';

class RankTile extends StatelessWidget {
  const RankTile({
    super.key,
    required this.rankingEntity,
    required this.label,
    this.isSelfRank = false,
  });

  final RankingEntity rankingEntity;
  final String label;
  final bool isSelfRank;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.gw, horizontal: 12.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: context.theme.cardColor,
      ),
      child: Row(
        spacing: 12.gw,
        children: [
          if (!isSelfRank && rankingEntity.rankingNo != 0)
            Text(
              '${rankingEntity.rankingNo}',
              style: context.textTheme.title.fs24.w600,
            ),
          AppAvatar(avatar: rankingEntity.avatar, size: 50.gw),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 4.gw,
              children: [
                Text(rankingEntity.nickName, style: context.textTheme.title.fs16.w600),
                Text(label.tr(namedArgs: {'rank': '${rankingEntity.rankingNo}'}),
                    style: context.textTheme.tertiary.fs12.w500),
              ],
            ),
          ),
          Column(
            spacing: 4.gw,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 4.gw),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7.gr),
                  color: context.theme.primaryColor,
                ),
                child: Text('${rankingEntity.profitRate}%', style: context.textTheme.secondary.w600),
              ),
              Text(
                rankingEntity.rankingNo == 0 ? 'rank.notInTheList'.tr() : '¥ ${rankingEntity.amount}',
                style: context.textTheme.tertiary.w500,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
