#!/bin/bash

# ================================================================
# switch_flavor.sh
# 执行顺序：先根据 flavor 置顶默认 skin/color-scheme → 先拷贝 skins → 再拷贝 flavors
# ================================================================

FLAVOR=""
SKIN=""
COLOR_SCHEME=""

# --------------------------------------
# 解析命令行参数
# --------------------------------------
while [[ $# -gt 0 ]]; do
  case $1 in
    --flavor)
      if [ -z "$2" ] || [[ "$2" == --* ]]; then
        echo "Error: Missing value for --flavor"; exit 1
      fi
      FLAVOR="$2"; shift 2;;
    --skin)
      if [ -z "$2" ] || [[ "$2" == --* ]]; then
        echo "Error: Missing value for --skin"; exit 1
      fi
      SKIN="$2"; shift 2;;
    --color-scheme)
      if [ -z "$2" ] || [[ "$2" == --* ]]; then
        echo "Error: Missing value for --color-scheme"; exit 1
      fi
      COLOR_SCHEME="$2"; shift 2;;
    *)
      echo "Unknown option: $1"; exit 1;;
  esac
done

SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PROJECT_ROOT=$(realpath "$SCRIPT_DIR/..")

# --------------------------------------
# 置顶：若提供了 FLAVOR，先根据映射为 SKIN / COLOR_SCHEME 赋默认值
# （这样即使未手动传入，也能在“skins步骤”生效）
# --------------------------------------
if [ -n "$FLAVOR" ]; then
  case "$FLAVOR" in
    gp|pre|rsyp|yhxt)
      DEFAULT_SKIN="gp"; DEFAULT_COLOR_SCHEME="default";;
    tempa)
      DEFAULT_SKIN="template_a"; DEFAULT_COLOR_SCHEME="default";;
    bszb)
      DEFAULT_SKIN="template_d"; DEFAULT_COLOR_SCHEME="default";;
    dyzb|xyzq)
      DEFAULT_SKIN="template_c"; DEFAULT_COLOR_SCHEME="default";;
    tempb)
      DEFAULT_SKIN="zang_golden"; DEFAULT_COLOR_SCHEME="default";;
    *)
      echo "Error: No default skin mapping found for flavor '$FLAVOR'."; exit 1;;
  esac

  if [ -z "$SKIN" ]; then
    SKIN="$DEFAULT_SKIN"
    echo "Auto-selected skin='$SKIN' for flavor '$FLAVOR'"
  fi
  if [ -z "$COLOR_SCHEME" ]; then
    COLOR_SCHEME="$DEFAULT_COLOR_SCHEME"
    echo "Auto-selected color_scheme='$COLOR_SCHEME' for flavor '$FLAVOR'"
  fi
fi

# --------------------------------------
# 先切换 Skin + Color Scheme
# --------------------------------------
if [ -n "$SKIN" ] && [ -n "$COLOR_SCHEME" ]; then
  SKIN_DIR="$PROJECT_ROOT/assets/skins/$SKIN"
  COLOR_SCHEME_DIR="$SKIN_DIR/$COLOR_SCHEME"
  TARGET_DIR="$PROJECT_ROOT/assets"

  if [ ! -d "$SKIN_DIR" ]; then
    echo "Error: Skin '$SKIN' not found in $PROJECT_ROOT/assets/skins/"
    echo "Available skins: $(ls -1 $PROJECT_ROOT/assets/skins | tr '\n' ' ')"
    exit 1
  fi
  if [ ! -d "$COLOR_SCHEME_DIR" ]; then
    echo "Error: Color scheme '$COLOR_SCHEME' not found in $SKIN_DIR/"
    echo "Available color schemes for '$SKIN': $(ls -1 $SKIN_DIR | tr '\n' ' ')"
    exit 1
  fi

  echo "-------------------------------------------"
  echo "Switching to skin: $SKIN with color scheme: $COLOR_SCHEME"
  echo "Copying files from $COLOR_SCHEME_DIR to $TARGET_DIR"
  echo "-------------------------------------------"

  ASSET_FOLDERS=("icons" "images" "svg")
  for folder in "${ASSET_FOLDERS[@]}"; do
    source_folder="$COLOR_SCHEME_DIR/$folder"
    target_folder="$TARGET_DIR/$folder"
    if [ -d "$source_folder" ]; then
      echo "Copying $folder from $source_folder to $target_folder"
      mkdir -p "$target_folder"
      find "$source_folder" -type f | while read -r file; do
        rel_path="${file#$source_folder/}"
        target_subdir="$target_folder/$(dirname "$rel_path")"
        mkdir -p "$target_subdir"
        cp -v "$file" "$target_folder/$rel_path"
      done
    else
      echo "Warning: $folder directory not found in $COLOR_SCHEME_DIR, skipping"
    fi
  done

  echo "Skin switched to '$SKIN' with color scheme '$COLOR_SCHEME' successfully!"
elif [ -n "$SKIN" ] || [ -n "$COLOR_SCHEME" ]; then
  # 仅传了一个时仍然报错（除非上面由 FLAVOR 自动补全了两个）
  echo "Error: You must provide both --skin and --color-scheme to switch skin."
  exit 1
fi

# --------------------------------------
# 再切换 Flavor（资源与翻译覆盖）
# --------------------------------------
if [ -n "$FLAVOR" ]; then
  FLAVOR_DIR="$PROJECT_ROOT/assets/flavors/$FLAVOR"
  TARGET_DIR="$PROJECT_ROOT/assets"
  TRANSLATIONS_DIR="$TARGET_DIR/translations"
  FLAVOR_TRANSLATIONS_DIR="$FLAVOR_DIR/translations"

  if [ ! -d "$FLAVOR_DIR" ]; then
    echo "Error: Flavor '$FLAVOR' not found in assets/flavors/"
    echo "Available flavors: $(ls -1 assets/flavors | tr '\n' ' ')"
    exit 1
  fi

  echo "-------------------------------------------"
  echo "Switching to flavor: $FLAVOR"
  echo "Copying files from $FLAVOR_DIR to $TARGET_DIR"
  echo "-------------------------------------------"

  # 拷贝 flavor 文件（除 translations）
  find "$FLAVOR_DIR" -type f -not -path "*/translations/*" | while read -r file; do
    rel_path="${file#$FLAVOR_DIR/}"
    target_dir="$TARGET_DIR/$(dirname "$rel_path")"
    mkdir -p "$target_dir"
    cp -v "$file" "$TARGET_DIR/$rel_path"
  done

  # 合并翻译文件
  echo "Updating translation files for flavor: $FLAVOR"
  if ! command -v jq &> /dev/null; then
    echo "Error: 'jq' is required for JSON manipulation but it's not installed."
    echo "Please install jq via your package manager (apt/brew/choco)."
    exit 1
  fi

  for lang_file in "en-US.json" "zh-CN.json" "zh-HK.json"; do
    orig_file="$TRANSLATIONS_DIR/$lang_file"
    flavor_override_file="$FLAVOR_TRANSLATIONS_DIR/$lang_file"

    if [ -f "$orig_file" ] && [ -f "$flavor_override_file" ]; then
      jq -s '.[0] * .[1]' "$orig_file" "$flavor_override_file" > "$orig_file.tmp"
      mv "$orig_file.tmp" "$orig_file"
    elif [ -f "$flavor_override_file" ]; then
      mkdir -p "$TRANSLATIONS_DIR"
      cp "$flavor_override_file" "$orig_file"
    else
      echo "No flavor-specific translations found for $lang_file, skipping"
    fi
  done

  echo "Flavor switched to '$FLAVOR' successfully!"
fi

echo
echo "🎉 All switches completed successfully!"
