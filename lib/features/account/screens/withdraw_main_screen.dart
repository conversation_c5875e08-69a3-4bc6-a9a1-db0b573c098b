import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/bank_list/bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/user_bank_list/user_bank_list_cubit.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_cubit.dart';
import 'package:gp_stock_app/features/account/screens/withdraw_screen.dart';
import 'package:gp_stock_app/features/profile/screens/alipay_withdraw/alipay_withdraw_cubit.dart';
import 'package:gp_stock_app/features/profile/screens/alipay_withdraw/alipay_withdraw_view.dart';
import 'package:gp_stock_app/features/profile/screens/third_party_channel/third_party_channel_screen.dart';
import 'package:gp_stock_app/features/profile/screens/usdt_deposit/usdt_deposit_cubit.dart';
import 'package:gp_stock_app/features/profile/screens/usdt_withdraw/usdt_withdraw_cubit.dart';
import 'package:gp_stock_app/shared/widgets/tab_bar/common_tab_bar/common_tab_bar.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

import '../../profile/screens/usdt_withdraw/usdt_withdraw_view.dart';

class WithdrawMainScreen extends StatefulWidget {
  const WithdrawMainScreen({super.key});

  @override
  State<WithdrawMainScreen> createState() => _WithdrawMainScreenState();
}

class _WithdrawMainScreenState extends State<WithdrawMainScreen> {
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tabData = [
      'bank'.tr(),
      'third'.tr(),
      'USDT',
      'alipay'.tr(),
    ];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        title: Text('withdrawal'.tr()),
        actions: [
          IconButton(
            icon: Icon(LucideIcons.notepad_text),
            onPressed: () {
              getIt<NavigatorService>().push(AppRouter.routeWithdrawRecords);
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(50),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gw),
            color: context.theme.cardColor,
            child: CommonTabBar.withAutoKey(
              tabData,
              currentIndex: _selectedTabIndex,
              onTap: (index) => setState(() => _selectedTabIndex = index),
              style: CommonTabBarStyle.line,
              isScrollable: true,
            ),
          ),
        ),
      ),
      body: IndexedStack(
        index: _selectedTabIndex,
        children: [
          MultiBlocProvider(
            providers: [
              BlocProvider<WithdrawalCubit>.value(value: context.read<WithdrawalCubit>()),
              BlocProvider<UserBankListCubit>.value(value: context.read<UserBankListCubit>()),
              BlocProvider<BankListCubit>.value(value: context.read<BankListCubit>()),
            ],
            child: const WithdrawScreen(
              showAppBar: false,
            ),
          ),
          ThirdPartyChannelScreen(showAppBar: false, transactionType: ThirdPartyTransactionType.withdraw),
          MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => getIt<UsdtWithdrawCubit>(),
              ),
              BlocProvider<UsdtDepositCubit>(create: (context) => UsdtDepositCubit()),
            ],
            child: UsdtWithdrawView(),
          ),
          BlocProvider(
            create: (context) => AlipayWithdrawCubit(),
            child: AlipayWithdrawView(),
          ),
        ],
      ),
    );
  }
}
