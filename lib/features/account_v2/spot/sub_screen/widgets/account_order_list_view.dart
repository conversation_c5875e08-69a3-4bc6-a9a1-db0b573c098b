import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/order_list_state.dart';
import 'package:scrollview_observer/scrollview_observer.dart';
import '../../utils/account_cell_builder.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'list_view_cell/account_entrust_cell.dart';
import 'list_view_cell/account_position_cell.dart';
import 'list_view_cell/account_trade_cell.dart';

class AccountOrderListView extends StatefulWidget {
  final TradingAccountType tradingAccountType;
  final MarketCategory marketCategory;
  final Contract? contractModel; // 合约类型必传，用于点击跳转至交易时传值
  final OrderListType orderType;
  final OrderListState orderListState;
  final Function(List<int> visibleIds)? onVisibleRecordsChanged; // 可见记录 ID 变化回调
  final VoidCallback onSellSuccess; // 卖出成功回调
  final VoidCallback? onCancelSuccess; // 撤单成功回调
  final double? margin;


  const AccountOrderListView({
    super.key,
    required this.tradingAccountType,
    required this.marketCategory,
    this.contractModel,
    required this.orderType,
    required this.orderListState,
    this.onVisibleRecordsChanged,
    required this.onSellSuccess,
    this.onCancelSuccess,
    this.margin,
  });

  @override
  State<StatefulWidget> createState() => _AccountOrderListViewState();
}

class _AccountOrderListViewState extends State<AccountOrderListView> {
  // 使用 PrimaryScrollController 实现滚动联动，正确管理 ListObserverController 生命周期
  // Use PrimaryScrollController for scroll linkage with proper ListObserverController lifecycle management
  ListObserverController? _observerController;
  ScrollController? _currentScrollController;
  BuildContext? _sliverContext; // 用于捕获 SliverList 的 context

  late OrderListState model = widget.orderListState;

  @override
  void initState() {
    super.initState();
    // 初始数据加载由父页面处理 (account_screen_page_v2.dart)
    // 避免重复加载
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 获取 PrimaryScrollController
    // Get PrimaryScrollController
    final scrollController = PrimaryScrollController.of(context);

    // 只在 controller 变化时重新创建，避免每次 build 都创建新实例
    // Only recreate when controller changes, avoid creating new instance on every build
    if (_currentScrollController != scrollController) {
      // ListObserverController 不需要手动 dispose
      // ListObserverController doesn't need manual disposal
      _currentScrollController = scrollController;
      _observerController = ListObserverController(controller: scrollController);
    }
  }

  @override
  void dispose() {
    // ListObserverController 不需要手动 dispose
    // ListObserverController doesn't need manual disposal
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AccountOrderListView oldWidget) {
    if (widget.orderListState != oldWidget.orderListState) {
      setState(() {
        model = widget.orderListState;
      });
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    // 等待 ListObserverController 在 didChangeDependencies 中初始化完成
    // Wait for ListObserverController to be initialized in didChangeDependencies
    if (_observerController == null) {
      return const SizedBox.shrink();
    }

    return ListViewObserver(
      controller: _observerController!,
      triggerOnObserveType: ObserverTriggerOnObserveType.directly,
      sliverListContexts: () => [
        if (_sliverContext != null) _sliverContext!,
      ],
      onObserveAll: (resultMap) {
          // 从 resultMap 中获取对应 context 的观察结果
          final resultModel = resultMap[_sliverContext];
          if (resultModel == null || !resultModel.visible) return;

          // 获取实际可见项的索引列表
          final visibleIndexes = resultModel.displayingChildIndexList;
          if (visibleIndexes.isEmpty) return;

          // 将索引转换为 ID
          final visibleIds = visibleIndexes
              .where((index) => index < model.records.length)
              .map((index) => model.records[index].id)
              .toList();

          LogD("📋 [${widget.orderType.tradingCenterKey}] 可见ID: $visibleIds (索引: $visibleIndexes) 总数: ${model.records.length}");

          widget.onVisibleRecordsChanged?.call(visibleIds);
        },
        child: CustomScrollView(
          // 不指定 controller，让 NestedScrollView 提供滚动控制器以实现滚动联动
          // No controller specified, let NestedScrollView provide scroll controller for linkage
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
          // 使用 SliverOverlapInjector 注入由外层 NestedScrollView 的 SliverOverlapAbsorber 预留的重叠区域，
          // 解决外层可折叠头部与内层 CustomScrollView(slivers) 的可滚动内容之间的重叠问题。
          // 外层实现要求：
          // 1) 用 NestedScrollView 包裹该列表；
          // 2) 在 NestedScrollView 的 headerSliverBuilder 中放置 SliverOverlapAbsorber，
          //    且 handle 使用同一个 NestedScrollView.sliverOverlapAbsorberHandleFor(context)；
          // 3) 内层 body 的 slivers 列表顶部加入本 SliverOverlapInjector（当前这段），以抵消重叠。
          SliverOverlapInjector(
            handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
          ),
          if (model.records.isEmpty) ...[
            if (model.isInitialLoad && model.status == DataStatus.loading) ...[
              ///骨架图
              SliverToBoxAdapter(child: _buildShimmerWidget(widget.orderType)),
            ] else ...[
              /// 空视图
              SliverFillRemaining(
                  child: TableEmptyWidget(
                height: 40,
                width: 40,
                title: widget.orderType.listEmptyText.tr(),
                margin: EdgeInsets.symmetric(horizontal: widget.margin ?? 18.gw),
                radius: 10.gw,
              )),
            ]
          ],
          if (model.records.isNotEmpty)
            SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: widget.margin ?? 18.gw),
              sliver: SliverList.separated(
                itemCount: model.records.length,
                separatorBuilder: (_, __) =>
                    widget.orderType == OrderListType.positions ? 10.verticalSpace : const SizedBox.shrink(),
                itemBuilder: (context, index) {
                  // 在 itemBuilder 中捕获 context
                  if (_sliverContext != context) {
                    _sliverContext = context;
                  }


                  final item = model.records[index];
                  return AccountCellBuilder.buildCell(
                    context,
                    category: widget.marketCategory,
                    item: item,
                    index: index,
                    isLast: index == model.records.length - 1,
                    isIndexTrading: item.isIndex,
                    orderType: widget.orderType,
                    tradingAccountType: widget.tradingAccountType,
                    contractModel: widget.contractModel,
                    onSellSuccess: widget.onSellSuccess,
                    onCancelSuccess: widget.onCancelSuccess,
                  );
                },
              ),
            ),
            SliverToBoxAdapter(child: SizedBox(height: 20.gw)),
        ],
      ),
    );
  }


  Widget _buildShimmerWidget(OrderListType type) {
    // 生成 3 个条目索引
    return Column(
      children: List.generate(3, (_) {
        return switch (type) {
          OrderListType.positions => AccountPositionShimmerCell(marketCategory: widget.marketCategory),
          OrderListType.trades => AccountTradeShimmerCell(),
          OrderListType.orders => AccountEntrustShimmerCell(),
        };
      }),
    );
  }
}
