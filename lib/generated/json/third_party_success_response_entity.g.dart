import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/third_party_success_response_entity.dart';

ThirdPartySuccessEntity $ThirdPartySuccessResponseEntityFromJson(Map<String, dynamic> json) {
  return ThirdPartySuccessEntity(
      amount: jsonConvert.convert<int>(json['amount']) ?? 0,
      currency: jsonConvert.convert<String>(json['currency']) ?? '',
      orderNo: jsonConvert.convert<String>(json['orderNo']) ?? '',
      payUrl: jsonConvert.convert<String>(json['payUrl']) ?? '',
      payWayName: jsonConvert.convert<String>(json['payWayName']) ?? '',
      tradeTime: jsonConvert.convert<String>(json['tradeTime']) ?? '');
}

Map<String, dynamic> $ThirdPartySuccessResponseEntityToJson(ThirdPartySuccessEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['amount'] = entity.amount;
  data['currency'] = entity.currency;
  data['orderNo'] = entity.orderNo;
  data['payUrl'] = entity.payUrl;
  data['payWayName'] = entity.payWayName;
  data['tradeTime'] = entity.tradeTime;
  return data;
}

extension ThirdPartySuccessResponseEntityExtension on ThirdPartySuccessEntity {
  ThirdPartySuccessEntity copyWith({
    int? amount,
    String? currency,
    String? orderNo,
    String? payUrl,
    String? payWayName,
    String? tradeTime,
  }) {
    return ThirdPartySuccessEntity(
        amount: amount ?? this.amount,
        currency: currency ?? this.currency,
        orderNo: orderNo ?? this.orderNo,
        payUrl: payUrl ?? this.payUrl,
        payWayName: payWayName ?? this.payWayName,
        tradeTime: tradeTime ?? this.tradeTime);
  }
}
