part of 'activity_cubit.dart';

class ActivityState extends Equatable {
  final int selectedTab;
  final int selectedEventIndex;
  final DataStatus tasksFetchStatus;
  final DataStatus? rewardCollectionStatus;
  final List<TaskEntity>? newUserTasks;
  final List<TaskEntity>? tradeTasks;
  final List<TaskEntity>? dailyTasks;
  final String? signInRules;
  final String? error;

  /// 热门活动通知列表 / Hot events notification list
  final List<NotificationEntity> notifications;

  /// 通知加载状态 / Notification loading status
  final DataStatus notificationStatus;

  const ActivityState({
    this.selectedTab = 0,
    this.selectedEventIndex = 0,
    this.tasksFetchStatus = DataStatus.idle,
    this.rewardCollectionStatus,
    this.newUserTasks,
    this.tradeTasks,
    this.dailyTasks,
    this.signInRules,
    this.error,
    this.notifications = const [],
    this.notificationStatus = DataStatus.idle,
  });

  @override
  List<Object?> get props => [
        selectedTab,
        tasksFetchStatus,
        rewardCollectionStatus,
        newUserTasks,
        tradeTasks,
        dailyTasks,
        signInRules,
        error,
        selectedEventIndex,
        notifications,
        notificationStatus,
      ];

  ActivityState copyWith({
    int? selectedTab,
    int? selectedEventIndex,
    DataStatus? tasksFetchStatus,
    DataStatus? rewardCollectionStatus,
    List<TaskEntity>? newUserTasks,
    List<TaskEntity>? tradeTasks,
    List<TaskEntity>? dailyTasks,
    String? signInRules,
    String? error,
    List<NotificationEntity>? notifications,
    DataStatus? notificationStatus,
  }) {
    return ActivityState(
      selectedTab: selectedTab ?? this.selectedTab,
      selectedEventIndex: selectedEventIndex ?? this.selectedEventIndex,
      tasksFetchStatus: tasksFetchStatus ?? this.tasksFetchStatus,
      rewardCollectionStatus: rewardCollectionStatus ?? this.rewardCollectionStatus,
      newUserTasks: newUserTasks ?? this.newUserTasks,
      tradeTasks: tradeTasks ?? this.tradeTasks,
      dailyTasks: dailyTasks ?? this.dailyTasks,
      signInRules: signInRules ?? this.signInRules,
      error: error ?? this.error,
      notifications: notifications ?? this.notifications,
      notificationStatus: notificationStatus ?? this.notificationStatus,
    );
  }
}
