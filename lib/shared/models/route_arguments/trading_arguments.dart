import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';

/// V2 交易中心路由参数（简洁版）
/// Trading Center V2 route arguments (simplified version)
class TradingArgumentsV2 {
  /// 证券标的（必需）
  /// Security instrument (required)
  final Security security;

  /// 初始显示的标签页类型（可选，默认：行情）
  /// Initial tab type to display (optional, default: Quotes)
  final TradeTabType initialTabType;

  /// 初始选中的交易账户ID（可选）
  /// - 现货：传递 kDropDownValueSpotId
  /// - 合约：传递合约的 id.toString()
  /// - 不传递：默认显示现货余额（不自动选中账户）
  /// Initial selected trade account ID (optional)
  /// - Spot: pass kDropDownValueSpotId
  /// - Contract: pass contract id.toString()
  /// - Not passed: default to spot balance (no account auto-selected)
  final String? initialTradeAccountId;

  /// 是否为股指交易（可选，默认：false）
  /// Whether it's index trading (optional, default: false)
  final bool isIndexTrading;

  TradingArgumentsV2({
    required this.security,
    this.initialTabType = TradeTabType.Quotes,
    this.initialTradeAccountId,
    this.isIndexTrading = false,
  });
}
